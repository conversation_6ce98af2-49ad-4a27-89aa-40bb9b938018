package com.wosai.trade.optimus.export.application.support;

import okhttp3.internal.Util;

import java.util.Optional;
import java.util.concurrent.*;

/**
 * <AUTHOR> Date: 2023/8/7 Time: 14:44
 */
public class ExecutorFacade {
    private static final ConcurrentMap<Integer, ThreadPoolExecutor> GENERAL_EXECUTORS
            = new ConcurrentHashMap<>();
    private static final ThreadPoolExecutor COMMON_EXECUTOR_SERVICE
            = new ThreadPoolExecutor(50, 500, 2
            , TimeUnit.HOURS,  new ArrayBlockingQueue<>(100)
            , Util.threadFactory("Module:Common"
            , false)
            , new ThreadPoolExecutor.AbortPolicy());

    private static final int GENERAL_EXECUTORS_SIZE = 10;

    private static final String DEFAULT_KEY = "2.718281828459045";



    static {
        for (int code = 0; code < GENERAL_EXECUTORS_SIZE; code++) {
            ThreadPoolExecutor GENERAL_EXECUTOR_SERVICE
                    = new ThreadPoolExecutor(10, 100, 2
                    , TimeUnit.HOURS,  new ArrayBlockingQueue<>(50)
                    , Util.threadFactory("Module:General:" + code
                    , false)
                    , new ThreadPoolExecutor.DiscardPolicy());
            GENERAL_EXECUTORS.put(code, GENERAL_EXECUTOR_SERVICE);
        }

    }

    public static void destroy() {
        GENERAL_EXECUTORS.values().forEach(ThreadPoolExecutor::shutdown);
        COMMON_EXECUTOR_SERVICE.shutdown();
    }


    public static ThreadPoolExecutor general(Object key) {
        int hashCode = Optional.ofNullable(key).orElse(DEFAULT_KEY).hashCode();
        return GENERAL_EXECUTORS.get(Math.abs(hashCode % GENERAL_EXECUTORS_SIZE));
    }

    public static ThreadPoolExecutor common() {
        return COMMON_EXECUTOR_SERVICE;
    }


}
