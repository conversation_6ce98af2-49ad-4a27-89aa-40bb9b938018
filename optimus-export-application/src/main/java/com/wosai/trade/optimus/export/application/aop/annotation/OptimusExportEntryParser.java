package com.wosai.trade.optimus.export.application.aop.annotation;

import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/6/12 Time: 3:20 PM
 */
@Component
public class OptimusExportEntryParser {

    public boolean isCommonLogging(MethodSignature methodSignature) {
        OptimusExportEntry tradeOrderEntry = methodSignature.getMethod()
                .getDeclaredAnnotation(OptimusExportEntry.class);
        return Objects.isNull(tradeOrderEntry)
                || (!tradeOrderEntry.skipLogging()
                && !tradeOrderEntry.skipLoggingParams());
    }

    public boolean isLoggingMethod(MethodSignature methodSignature) {
        OptimusExportEntry tradeOrderEntry = methodSignature.getMethod()
                .getDeclaredAnnotation(OptimusExportEntry.class);
        return Objects.nonNull(tradeOrderEntry) && !tradeOrderEntry.skipLogging()
                && tradeOrderEntry.skipLoggingParams();
    }

    public boolean isSkipValidate(MethodSignature methodSignature) {
        OptimusExportEntry tradeOrderEntry = methodSignature.getMethod()
                .getDeclaredAnnotation(OptimusExportEntry.class);
        return Objects.nonNull(tradeOrderEntry) && tradeOrderEntry.skipValidate();
    }

    public boolean isThrowUnknownException(MethodSignature methodSignature) {
        OptimusExportEntry tradeOrderEntry = methodSignature.getMethod()
                .getDeclaredAnnotation(OptimusExportEntry.class);
        return Objects.nonNull(tradeOrderEntry) && tradeOrderEntry.throwUnknownException();
    }

    public boolean isThrowBizException(MethodSignature methodSignature) {
        OptimusExportEntry tradeOrderEntry = methodSignature.getMethod()
                .getDeclaredAnnotation(OptimusExportEntry.class);
        return Objects.nonNull(tradeOrderEntry) && tradeOrderEntry.throwBizException();
    }

}
