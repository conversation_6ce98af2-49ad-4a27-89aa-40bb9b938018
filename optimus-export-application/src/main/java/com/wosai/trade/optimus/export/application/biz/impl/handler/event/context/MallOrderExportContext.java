package com.wosai.trade.optimus.export.application.biz.impl.handler.event.context;


import com.wosai.trade.optimus.export.application.biz.impl.handler.event.holder.EventHandlerContext;
import com.wosai.trade.optimus.export.application.biz.impl.handler.event.impl.MallOrderExportStrategy;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.query.ExportMallOrderAggrQuery;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view.TradeDataSummaryView;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportInfoVO;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req.ExportMallListQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req.ExportMallOrdersQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req.ExportMallTicketsQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallListQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallOrdersQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallTicketsQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.taskcenter.req.TaskCenterTaskModifyRequest;
import lombok.Getter;

import java.util.Objects;


/**
 * <AUTHOR> Date: 2023/7/18 Time: 15:52
 */
@Getter
public class MallOrderExportContext extends EventStrategyContext<Void> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.MALL_ORDER_EXPORT;

    public static final int COUNT = 1000;

    static {
        registerContext(EVENT_TYPE, new MallOrderExportContext());
    }

    private ExportOrderAggrRoot exportOrderAggrRoot;
    private MallOrderExportStrategy.TradeDataExcelExportTemplate tradeDataExcelExportTemplate;
    private TradeDataSummaryView tradeDataSummaryView;
    private ExportMallListQueryResult mallListQueryResult;

    public MallOrderExportContext() {
    }

    public MallOrderExportContext(EventAggrRoot eventAggrRoot) {
        super(eventAggrRoot);
    }

    @Override
    public EventStrategyContext<Void> rebuildContext(EventHandlerContext context) {
        return new MallOrderExportContext(context.getEventAggrRoot());
    }

    @Override
    public Void getBizContent() {
        return null;
    }

    public void bindMallExportOrderAggrRoot(ExportOrderAggrRoot exportOrderAggrRoot) {
        this.exportOrderAggrRoot = exportOrderAggrRoot;
    }

    public void bindTradeDataExcelExportTemplate(MallOrderExportStrategy.TradeDataExcelExportTemplate tradeDataExcelExportTemplate) {
        this.tradeDataExcelExportTemplate = tradeDataExcelExportTemplate;
    }

    public void bindTradeDataSummaryView(TradeDataSummaryView tradeDataSummaryView) {
        this.tradeDataSummaryView = tradeDataSummaryView;
    }

    public ExportMallOrderAggrQuery genMallExportOrderAggrQuery() {
        return ExportMallOrderAggrQuery.builder()
                .id(getEventAggrRoot().getAssociatedSnNum())
                .build();
    }

    public ExportMallOrdersQueryRequest genExportMallOrdersQueryRequest(ExportMallListQueryResult.ExportMallHolder exportMall, ExportMallOrdersQueryResult lastOrdersResult) {
        String endCursor = null;
        if (Objects.nonNull(lastOrdersResult)
                && lastOrdersResult.isOrderExist()) {
            endCursor = lastOrdersResult.getTailOrderSn();
        }

        ExportInfoVO exportInfo = exportOrderAggrRoot.getExportInfo();
        return ExportMallOrdersQueryRequest.builder()
                .mallSn(exportMall.getMallSn())
                .mallSignature(exportMall.getMallSignature())
                .orderStates(exportInfo.getOrderStates())
                .beginDateTime(exportInfo.getBeginDateTime())
                .endDateTime(exportInfo.getEndDateTime())
                .payTsn(exportInfo.getPayTsn())
                .orderSn(exportInfo.getOrderSn())
                .endCursor(endCursor)
                .count(COUNT)
                .build();
    }

    public ExportMallTicketsQueryRequest genExportMallTicketsQueryRequest(ExportMallListQueryResult.ExportMallHolder exportMall, ExportMallTicketsQueryResult lastTicketsResult) {
        String endCursor = null;
        if (Objects.nonNull(lastTicketsResult)
                && lastTicketsResult.isTicketExist()) {
            endCursor = lastTicketsResult.getTailTicketSn();
        }

        ExportInfoVO exportInfo = exportOrderAggrRoot.getExportInfo();
        return ExportMallTicketsQueryRequest.builder()
                .mallSn(exportMall.getMallSn())
                .mallSignature(exportMall.getMallSignature())
                .ticketStates(exportInfo.getTicketStates())
                .beginDateTime(exportInfo.getBeginDateTime())
                .endDateTime(exportInfo.getEndDateTime())
                .payTsn(exportInfo.getPayTsn())
                .orderSn(exportInfo.getOrderSn())
                .endCursor(endCursor)
                .count(COUNT)
                .build();
    }

    public ExportMallListQueryRequest genExportMallListQueryRequest() {
        return ExportMallListQueryRequest.builder()
                .mallSnSignatures(exportOrderAggrRoot.getMallSnSignaturePairs())
                .build();
    }

    public TaskCenterTaskModifyRequest genTaskCenterUpdateTaskRequest() {
        return TaskCenterTaskModifyRequest.builder()
                .taskLogId(exportOrderAggrRoot.getExt().getTaskLogId())
                .filePath(exportOrderAggrRoot.genFilePath()).build();
    }

    public void bindExportMallListQueryResult(ExportMallListQueryResult mallListQueryResult) {
        this.mallListQueryResult = mallListQueryResult;
    }
}
