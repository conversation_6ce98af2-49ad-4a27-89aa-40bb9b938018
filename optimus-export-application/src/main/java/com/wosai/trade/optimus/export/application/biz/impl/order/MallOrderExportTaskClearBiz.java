package com.wosai.trade.optimus.export.application.biz.impl.order;

import com.wosai.trade.optimus.export.application.biz.BaseClearBiz;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportTaskClearContext;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.ExportMallOrderDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2023/7/19 Time: 18:41
 */
@Service
public class MallOrderExportTaskClearBiz extends BaseClearBiz<MallOrderExportTaskClearContext> {

    @Resource
    private ExportMallOrderDao exportMallOrderDao;


    @Override
    protected int delete(MallOrderExportTaskClearContext context) {
        return exportMallOrderDao.delete(context.genMallExportOrderDelete());
    }

    @Override
    protected String getClearTaskName(MallOrderExportTaskClearContext context) {
        return "清除过期订单导出任务";
    }
}
