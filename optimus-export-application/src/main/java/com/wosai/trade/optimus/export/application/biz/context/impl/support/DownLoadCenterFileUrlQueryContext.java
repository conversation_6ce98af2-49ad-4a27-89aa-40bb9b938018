package com.wosai.trade.optimus.export.application.biz.context.impl.support;

import com.wosai.trade.optimus.export.application.biz.context.BaseContext;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model.OssGeneratePreSignedUrlResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model.OssUploadRequest;

/**
 * <AUTHOR> Date: 2024/7/26 Time: 10:33
 */
public class DownLoadCenterFileUrlQueryContext extends BaseContext {

    private final String filePath;

    private OssGeneratePreSignedUrlResult urlResult;

    private DownLoadCenterFileUrlQueryContext(String filePath) {
        this.filePath = filePath;
    }

    public static DownLoadCenterFileUrlQueryContext newInstance(String filePath) {
        return new DownLoadCenterFileUrlQueryContext(filePath);
    }

    public String genDownLoadCenterTaskResult() {
        if (urlResult.isInvokeSucceed()) {
            return urlResult.getUrl();
        }
        return null;
    }

    public OssUploadRequest genOssUploadRequest(String bucketName) {
        return OssUploadRequest.builder()
                .path(filePath)
                .bucketName(bucketName).build();
    }

    public void bindOssGeneratePresignedUrlResult(OssGeneratePreSignedUrlResult urlResult) {
        this.urlResult = urlResult;
    }
}
