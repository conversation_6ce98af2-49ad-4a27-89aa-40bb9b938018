package com.wosai.trade.optimus.export.application.biz.context.impl.mall;

import com.wosai.trade.optimus.export.api.model.req.ExportSellerIDModel;
import com.wosai.trade.optimus.export.api.request.order.OrderExportFileSendRequest;
import com.wosai.trade.optimus.export.application.biz.context.BaseContext;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.query.EventAggrQuery;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.query.ExportMallOrderAggrQuery;
import lombok.Getter;

/**
 * <AUTHOR> Date: 2023/7/19 Time: 19:59
 */
public class MallOrderExportFileSendContext extends BaseContext {
    private final String merchantId;
    private final String merchantUserId;
    private final String taskId;

    @Getter
    private final String email;

    private ExportOrderAggrRoot exportOrderAggrRoot;

    private MallOrderExportFileSendContext(String merchantId, String merchantUserId, String taskId, String email) {
        this.merchantId = merchantId;
        this.merchantUserId = merchantUserId;
        this.taskId = taskId;
        this.email = email;
    }

    public static MallOrderExportFileSendContext newInstance(OrderExportFileSendRequest request) {
        ExportSellerIDModel seller = request.getSeller();
        return new MallOrderExportFileSendContext(seller.getMerchantId()
                , seller.getMerchantUserId()
                , request.getTaskId()
                , request.getEmail());
    }

    public void bindMallExportOrderAggrRoot(ExportOrderAggrRoot exportOrderAggrRoot) {
        this.exportOrderAggrRoot = exportOrderAggrRoot;
    }

    public ExportMallOrderAggrQuery genMallExportOrderAggrQuery() {
        String plaintextTaskId = ExportOrderAggrRoot.getPlaintextId(taskId);
        return ExportMallOrderAggrQuery.builder()
                .ownerId(merchantId)
                .ownerUserId(merchantUserId)
                .id(Long.parseLong(plaintextTaskId))
                .build();
    }

    public EventAggrQuery genEventAggrQuery() {
        return EventAggrQuery.builder()
                .associatedSn(exportOrderAggrRoot.getIdStr())
                .type(EventTypeEnum.MALL_ORDER_EXPORT)
                .build();
    }




}
