package com.wosai.trade.optimus.export.application.biz.impl.order;

import com.wosai.trade.optimus.export.application.biz.BaseBiz;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportTaskQueryContext;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.ExportOrderDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.OptimusOssClient;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model.OssGeneratePreSignedUrlResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2024/4/18 Time: 09:45
 */
@Service
public class OrderExportTaskQueryBiz extends BaseBiz<MallOrderExportTaskQueryContext> {

    @Resource
    private ExportOrderDomainRepository exportOrderDomainRepository;

    @Resource
    private OptimusOssClient optimusOssClient;

    @Value("${aliyun.oss.bucket-name}")
    private String bucketName;

    @Override
    protected void doBiz(MallOrderExportTaskQueryContext context) {
        ExportOrderAggrRoot exportOrderAggrRoot = exportOrderDomainRepository.query(context.genMallExportOrderAggrQuery());
        exportOrderAggrRoot.checkExist();
        context.bindMallExportOrderAggrRoot(exportOrderAggrRoot);

        OssGeneratePreSignedUrlResult urlResult = optimusOssClient.generatePresignedUrl(context.genOssUploadRequest(bucketName));
        context.bindOssGeneratePresignedUrlResult(urlResult);
    }
}
