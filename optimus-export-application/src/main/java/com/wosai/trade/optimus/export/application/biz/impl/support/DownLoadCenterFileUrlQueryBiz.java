package com.wosai.trade.optimus.export.application.biz.impl.support;

import com.wosai.trade.optimus.export.application.biz.BaseBiz;
import com.wosai.trade.optimus.export.application.biz.context.impl.support.DownLoadCenterFileUrlQueryContext;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.OptimusOssClient;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model.OssGeneratePreSignedUrlResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2024/7/26 Time: 10:33
 */
@Service
public class DownLoadCenterFileUrlQueryBiz extends BaseBiz<DownLoadCenterFileUrlQueryContext> {

    @Resource
    private OptimusOssClient optimusOssClient;

    @Value("${aliyun.oss.bucket-name}")
    private String bucketName;

    @Override
    protected void doBiz(DownLoadCenterFileUrlQueryContext context) {
        OssGeneratePreSignedUrlResult urlResult = optimusOssClient.generatePresignedUrl(context.genOssUploadRequest(bucketName));
        context.bindOssGeneratePresignedUrlResult(urlResult);
    }
}
