package com.wosai.trade.optimus.export.application.biz.impl.order;

import com.wosai.trade.optimus.export.application.biz.BaseBiz;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportTasksPagingContext;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.ExportOrderDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.query.ExportMallOrderAggrQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Date: 2023/7/19 Time: 19:13
 */
@Service
public class OrderExportTasksPagingBiz extends BaseBiz<MallOrderExportTasksPagingContext> {

    @Resource
    private ExportOrderDomainRepository exportOrderDomainRepository;

    @Override
    protected void doBiz(MallOrderExportTasksPagingContext context) {
        ExportMallOrderAggrQuery exportMallOrderAggrQuery = context.genMallExportOrderAggrQuery();
        long total = exportOrderDomainRepository.count(exportMallOrderAggrQuery);
        context.bindTotal(total);
        if (total > 0) {
            List<ExportOrderAggrRoot> exportOrderAggrRoots = exportOrderDomainRepository
                    .queryList(exportMallOrderAggrQuery);
            context.bindMallExportOrderAggrRoots(exportOrderAggrRoots);
        }
    }

}
