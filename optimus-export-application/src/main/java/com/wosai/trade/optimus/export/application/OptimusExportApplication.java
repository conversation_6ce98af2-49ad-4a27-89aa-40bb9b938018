package com.wosai.trade.optimus.export.application;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.wosai.database.instrumentation.springboot.v2.EnableDataSourceTranslate;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR> Date: 2023/6/12 Time: 3:02 PM
 */
@Slf4j
@EnableApolloConfig
@EnableDataSourceTranslate
@MapperScan(basePackages = "com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql")
@SpringBootApplication(scanBasePackages = {"com.wosai.trade.optimus.export"})
public class OptimusExportApplication {

    public static void main(String[] args) {
        SpringApplication.run(OptimusExportApplication.class, args);
    }


}
