package com.wosai.trade.optimus.export.application.biz.impl.order;

import com.wosai.trade.optimus.export.application.biz.BaseBiz;
import com.wosai.trade.optimus.export.application.biz.context.impl.order.BaseOrderContext;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.OptimusCoreClient;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallOrderQueryResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 2:15 PM
 */
@Service
public abstract class BaseOrderBiz<T extends BaseOrderContext> extends BaseBiz<T> {

    @Resource
    private OptimusCoreClient optimusCoreClient;

    @Override
    protected void preBiz(T context) {
        ExportMallOrderQueryResult exportMallOrderQueryResult = optimusCoreClient.queryMallOrder(context.genMallOrderQueryRequest());
        context.bindMallOrderQueryResult(exportMallOrderQueryResult);
        context.checkPermission();
    }

    @Override
    protected void doBiz(T context) {
        //核心子业务处理
        processModule(context);
    }

    protected abstract void processModule(T context);

}
