package com.wosai.trade.optimus.export.application.biz.impl.order;

import com.wosai.trade.optimus.export.application.biz.BaseBiz;
import com.wosai.trade.optimus.export.application.biz.context.impl.event.EventProcessContext;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportFileSendContext;
import com.wosai.trade.optimus.export.application.biz.impl.event.EventProcessBiz;
import com.wosai.trade.optimus.export.domain.aggregate.event.EventDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.ExportOrderDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2023/7/19 Time: 19:59
 */
@Service
public class OrderExportFileSendBiz extends BaseBiz<MallOrderExportFileSendContext> {

    @Resource
    private ExportOrderDomainRepository exportOrderDomainRepository;
    @Resource
    private EventDomainRepository eventDomainRepository;
    @Resource
    private EventProcessBiz eventProcessBiz;

    @Override
    protected void doBiz(MallOrderExportFileSendContext context) {
        ExportOrderAggrRoot exportOrderAggrRoot = exportOrderDomainRepository
                .query(context.genMallExportOrderAggrQuery());
        exportOrderAggrRoot.updateReceiveEmail(context.getEmail());
        exportOrderDomainRepository.save(exportOrderAggrRoot);

        context.bindMallExportOrderAggrRoot(exportOrderAggrRoot);

        EventAggrRoot eventAggrRoot = eventDomainRepository.query(context.genEventAggrQuery());
        eventAggrRoot.checkExist();

        eventProcessBiz.process(EventProcessContext.newInstance(eventAggrRoot));
    }

}
