package com.wosai.trade.optimus.export.application.adapter.rpc;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.general.annotation.UserContext;
import com.wosai.general.result.SingleResult;
import com.wosai.trade.optimus.export.api.SupportService;
import com.wosai.trade.optimus.export.api.request.order.DownloadCenterTaskCallBackRequest;
import com.wosai.trade.optimus.export.api.support.DownloadCenterTaskCallBackResult;
import com.wosai.trade.optimus.export.application.biz.context.impl.support.DownLoadCenterFileUrlQueryContext;
import com.wosai.trade.optimus.export.application.biz.context.impl.support.DownLoadCenterTaskCallBackContext;
import com.wosai.trade.optimus.export.application.biz.impl.support.DownLoadCenterFileUrlQueryBiz;
import com.wosai.trade.optimus.export.application.biz.impl.support.DownLoadCenterTaskCallBackBiz;
import com.wosai.trade.optimus.export.common.util.ResultUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2024/7/22 Time: 10:33
 */
@Service
@AutoJsonRpcServiceImpl
public class SupportServiceImpl implements SupportService {

    @Resource
    private DownLoadCenterTaskCallBackBiz downLoadCenterTaskCallBackBiz;

    @Resource
    private DownLoadCenterFileUrlQueryBiz downLoadCenterFileUrlQueryBiz;

    @UserContext
    @Override
    public SingleResult<DownloadCenterTaskCallBackResult> submitTask(String taskLogId, Integer type, DownloadCenterTaskCallBackRequest downLoadCenterTaskCallBackRequest) {
        DownLoadCenterTaskCallBackContext context = DownLoadCenterTaskCallBackContext.newInstance(taskLogId, type, downLoadCenterTaskCallBackRequest);
        downLoadCenterTaskCallBackBiz.process(context);
        return ResultUtils.buildSuccessfulSingleResult(context.genDownLoadCenterTaskResult());
    }

    @Override
    public String queryFileUrl(String filePath) {
        DownLoadCenterFileUrlQueryContext context = DownLoadCenterFileUrlQueryContext.newInstance(filePath);
        downLoadCenterFileUrlQueryBiz.process(context);
        return context.genDownLoadCenterTaskResult();
    }


}
