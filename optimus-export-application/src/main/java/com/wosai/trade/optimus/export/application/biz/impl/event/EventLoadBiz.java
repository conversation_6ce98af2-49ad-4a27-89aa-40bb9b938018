package com.wosai.trade.optimus.export.application.biz.impl.event;

import com.wosai.trade.optimus.export.application.biz.BaseBiz;
import com.wosai.trade.optimus.export.application.biz.context.impl.event.EventLoadContext;
import com.wosai.trade.optimus.export.domain.aggregate.event.EventDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.infrastructure.support.KafkaRateLimit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Date: 2023/1/16 Time: 3:10 PM
 */
@Slf4j
@Service
public class EventLoadBiz extends BaseBiz<EventLoadContext> {

    @Resource
    private EventDomainRepository eventDomainRepository;
    @Resource
    private KafkaRateLimit kafkaRateLimit;


    @Override
    protected void doBiz(EventLoadContext context) {
        List<EventAggrRoot> eventAggrRoots = eventDomainRepository
                .batchQuery(context.genEventAggrQuery());
        if (CollectionUtils.isNotEmpty(eventAggrRoots)) {
            if (context.isEnableLimitProcess() && kafkaRateLimit.isCrowded()) {
                log.warn("[Module事件加载]>>>>>>MQ消费者繁忙, 暂停加载事件");
                return;
            }
            eventDomainRepository.batchSend(eventAggrRoots);
        }
    }


}
