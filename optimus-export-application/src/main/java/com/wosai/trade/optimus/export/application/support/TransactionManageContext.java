package com.wosai.trade.optimus.export.application.support;

import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/5/5 Time: 11:57 AM
 */
public abstract class TransactionManageContext {

    private PlatformTransactionManager transactionManager;
    private TransactionStatus transactionStatus;
    private Object savepoint;
    private boolean isStartedTransaction;

    public void startTransaction(PlatformTransactionManager transactionManager
            , TransactionDefinition transactionDefinition) {
        this.transactionManager = transactionManager;
        this.transactionStatus = transactionManager.getTransaction(transactionDefinition);
        this.isStartedTransaction = true;
    }

    public void createSavepoint() {
        if (isStartedTransaction) {
            savepoint = transactionStatus.createSavepoint();
            return;
        }
        throw new OptimusExportBizException(OptimusExportRespCodeEnum.MISSING_PRE_OPERATION);
    }

    public void rollbackToSavepoint() {
        if (isStartedTransaction && Objects.nonNull(savepoint)) {
            transactionStatus.rollbackToSavepoint(savepoint);
        }
    }

    public void commit() {
        if (isStartedTransaction) {
            transactionManager.commit(transactionStatus);
        }
    }

    public void rollback() {
        if (isStartedTransaction) {
            transactionManager.rollback(transactionStatus);
        }
    }


}
