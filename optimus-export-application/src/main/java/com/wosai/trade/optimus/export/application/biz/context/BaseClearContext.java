package com.wosai.trade.optimus.export.application.biz.context;


import lombok.Getter;

import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/2/16 Time: 4:21 PM
 */
@Getter
public abstract class BaseClearContext extends BaseContext {
    private static final long MINIMUM_HOLDING_DAYS = 2;
    private static final long MAX_SINGLE_DELETE_COUNT = 10000;
    private static final long MIN_SINGLE_DELETE_COUNT = 1;
    private static final long DEFAULT_SINGLE_DELETE_COUNT = 5000;


    protected final long deleteDaysBefore;
    protected final long singleDeleteCount;

    protected BaseClearContext(Long deleteDaysBefore, Long singleDeleteCount) {
        if (Objects.isNull(deleteDaysBefore) || deleteDaysBefore < MINIMUM_HOLDING_DAYS) {
            deleteDaysBefore = MINIMUM_HOLDING_DAYS;
        }
        if (Objects.isNull(singleDeleteCount) || singleDeleteCount > MAX_SINGLE_DELETE_COUNT
                || singleDeleteCount < MIN_SINGLE_DELETE_COUNT) {
            singleDeleteCount = DEFAULT_SINGLE_DELETE_COUNT;
        }
        this.deleteDaysBefore = deleteDaysBefore;
        this.singleDeleteCount = singleDeleteCount;
    }

    protected LocalDate getDeleteDateBefore() {
        return LocalDate.now().minusDays(deleteDaysBefore);
    }

}
