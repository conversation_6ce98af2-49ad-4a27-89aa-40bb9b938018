package com.wosai.trade.optimus.export.application.biz.context.impl.mall;

import com.wosai.trade.optimus.export.api.model.req.ExportMallIDModel;
import com.wosai.trade.optimus.export.api.model.req.ExportSellerIDModel;
import com.wosai.trade.optimus.export.api.request.order.OrderExportTaskCreateRequest;
import com.wosai.trade.optimus.export.api.result.order.OrderExportTaskCreateResult;
import com.wosai.trade.optimus.export.application.biz.context.BaseContext;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRootFactory;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.vo.EventExtVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRootFactory;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.enums.ExportStateEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.enums.ReceiveMethodEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.query.ExportMallOrderAggrQuery;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportInfoVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportOperatorVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.OrderExportExtVO;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.req.MerchantQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res.MerchantQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.req.OptimusGroupUserMerchantQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.res.OptimusGroupUserMerchantQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req.ExportMallListQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallListQueryResult;
import com.wosai.trade.optimus.export.infrastructure.support.DefaultSerialGenerator;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/7/18 Time: 11:05
 */
@Getter
public class MallOrderExportTaskCreateContext extends BaseContext {
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final DateTimeFormatter ORDER_EXPORT_FILE_NAME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final int SINGLE_MALL_COUNT = 1;
    private static final String ROLE_SUPER_ADMIN = "super_admin";

    private final List<ExportMallIDModel> mallIDList;

    private final List<Integer> orderStates;
    private final List<Integer> ticketStates;
    private final ExportSellerIDModel seller;
    private final String beginDateTime;
    private final String endDateTime;
    private final Byte receiveMethod;
    private final String email;
    private final String payTsn;
    private final String orderSn;

    private ExportMallListQueryResult exportMallListQueryResult;
    private ExportOrderAggrRoot exportOrderAggrRoot;
    private OptimusGroupUserMerchantQueryResult groupUserMerchantQueryResult;
    private MerchantQueryResult merchantQueryResult;
    private String taskLogId;

    private MallOrderExportTaskCreateContext(List<ExportMallIDModel> mallIDList
            , List<Integer> orderStates
            , List<Integer> ticketStates
            , ExportSellerIDModel seller
            , String beginDateTime
            , String endDateTime
            , Byte receiveMethod
            , String email
            , String payTsn
            , String orderSn) {
        this.mallIDList = mallIDList;
        this.orderStates = orderStates;
        this.ticketStates = ticketStates;
        this.seller = seller;
        this.beginDateTime = beginDateTime;
        this.endDateTime = endDateTime;
        this.receiveMethod = receiveMethod;
        this.email = email;
        this.payTsn = payTsn;
        this.orderSn = orderSn;
    }

    public static MallOrderExportTaskCreateContext newInstance(OrderExportTaskCreateRequest request) {
        return new MallOrderExportTaskCreateContext(request.getMallIDList()
                , request.getOrderStates()
                , request.getTicketStates()
                , request.getSeller()
                , request.getBeginDateTime()
                , request.getEndDateTime()
                , request.getReceiveMethod()
                , request.getEmail()
                , request.getPayTsn()
                , request.getOrderSn());
    }

    public OrderExportTaskCreateResult genMallOrderExportTaskCreateResult() {
        return new OrderExportTaskCreateResult()
                .setTaskId(exportOrderAggrRoot.getCiphertextId());
    }

    public void bindMallExportOrderAggrRoot(ExportOrderAggrRoot exportOrderAggrRoot) {
        this.exportOrderAggrRoot = exportOrderAggrRoot;
    }


    public boolean isGroupUser() {
        return Objects.nonNull(seller.getGroupId())
                && Objects.nonNull(seller.getGroupUserId())
                && StringUtils.isEmpty(seller.getMerchantId());
    }

    public OptimusGroupUserMerchantQueryRequest genOptimusGroupUserMerchantQueryRequest() {
        return OptimusGroupUserMerchantQueryRequest.builder().groupId(seller.getGroupId()).build();
    }

    public List<String> getOwnerMerchantSnList() {
        if (isGroupUser()) {
            return groupUserMerchantQueryResult.getMerchantSnList();
        }
        return List.of(merchantQueryResult.getMerchantSn());
    }

    public MerchantQueryRequest genMerchantQueryRequest() {
        return MerchantQueryRequest.builder().merchantId(seller.getMerchantId()).build();
    }

    public ExportMallListQueryRequest genExportMallListQueryRequest(List<Map<String, String>> mallSnSignatures) {
        return ExportMallListQueryRequest.builder()
                .mallSnSignatures(mallSnSignatures)
                .build();
    }

    public void bindExportMallListQueryResult(ExportMallListQueryResult exportMallListQueryResult) {
        this.exportMallListQueryResult = exportMallListQueryResult;
    }

    public ExportMallOrderAggrQuery genMallExportOrderAggrQuery() {
        return ExportMallOrderAggrQuery.builder()
                .ownerId(exportOrderAggrRoot.getOperator().getOwnerId())
                .exportInfoDigest(exportOrderAggrRoot.getExportInfo().getDigest())
                .build();
    }


    public ExportOrderAggrRoot genMallExportOrderAggrRoot() {
        ExportMallListQueryResult.ExportMallHolder exportMallHolder = exportMallListQueryResult.getFirstMallHolder();

        return ExportOrderAggrRootFactory.builder()
                .coreBuilder()
                .id(DefaultSerialGenerator.getInstance().genExportMallOrderId())
                .state(ExportStateEnum.IN_GENERATING)
                .operator(ExportOperatorVO.builder()
                        .ownerId(isGroupUser() ? this.seller.getGroupId() : this.seller.getMerchantId())
                        .ownerUserId(isGroupUser() ? this.seller.getGroupUserId() : this.seller.getMerchantUserId())
                        .build())
                .exportInfo(ExportInfoVO.builder()
                        .receiveMethod(ReceiveMethodEnum.ofCode(receiveMethod))
                        .orderStates(orderStates)
                        .ticketStates(ticketStates)
                        .beginDateTime(LocalDateTime.from(DATE_TIME_FORMATTER.parse(beginDateTime)))
                        .endDateTime(LocalDateTime.from(DATE_TIME_FORMATTER.parse(endDateTime)))
                        .receiveEmail(email)
                        .payTsn(payTsn)
                        .orderSn(orderSn)
                        .mallSnSignaturePairs(genMallSnSignatureMaps())
                        .build())
                .optionalBuilder()
                .ext(OrderExportExtVO.builder()
                        .merchantSn(exportMallHolder.getMerchantSn())
                        .merchantName(exportMallHolder.getMerchantName())
                        .storeSn(exportMallHolder.getStoreSn())
                        .storeName(exportMallHolder.getStoreName())
                        .mallSn(exportMallHolder.getMallSn())
                        .mallName(exportMallHolder.getMallName())
                        .mallSize(mallIDList.size())
                        .taskLogId(taskLogId)
                        .build())
                .build();
    }

    public EventAggrRoot genOrderExportEvent() {
        return EventAggrRootFactory.builder()
                .coreBuilder()
                .id(DefaultSerialGenerator.getInstance().genEventId())
                .type(EventTypeEnum.MALL_ORDER_EXPORT)
                .associatedSn(exportOrderAggrRoot.getIdStr())
                .optionalBuilder()
                .ext(EventExtVO.builder()
                        .mqKey(exportOrderAggrRoot.getIdStr())
                        .build())
                .build();
    }

    public void bindOptimusGroupUserMerchantQueryResult(OptimusGroupUserMerchantQueryResult groupUserMerchantQueryResult) {
        this.groupUserMerchantQueryResult = groupUserMerchantQueryResult;
    }

    public void bindMerchantQueryResult(MerchantQueryResult merchantQueryResult) {
        this.merchantQueryResult = merchantQueryResult;
    }

    public List<Map<String, String>> genMallSnSignatureMaps() {
        List<Map<String, String>> pairList = new ArrayList<>();
        for (ExportMallIDModel model : mallIDList) {
            pairList.add(Map.of(model.getMallSn(), model.getSignature()));
        }

        return pairList;
    }

    public void bindTaskLogId(String taskLogId) {
        this.taskLogId = taskLogId;
    }
}
