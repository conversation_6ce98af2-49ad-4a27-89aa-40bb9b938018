package com.wosai.trade.optimus.export.application.aop;


import com.wosai.general.result.Result;

/**
 * <AUTHOR> Date: 2023/7/15 Time: 16:27
 */
public abstract class BaseInterceptor {

    protected Object genExceptionResult(Class<?> resultClass, String code, String msg) {
        try {
            Object obj = resultClass.getDeclaredConstructor().newInstance();
            if (obj instanceof Result result) {
                result.setSuccess(false);
                result.setCode(code);
                result.setMsg(msg);
                return result;
            }
            return null;
        } catch (Throwable ignored) {
            return null;
        }
    }
}
