package com.wosai.trade.optimus.export.application.support;

import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.vo.DelayRuleVO;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.feishu.FeishuClient;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.feishu.model.FeishuSendRequest;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2023/5/5 Time: 2:48 PM
 */
@Component
public class EventProcessSupportService {

    @Resource
    private FeishuClient feishuClient;

    public void sendAlarm(EventAggrRoot eventAggrRoot) {
        try {
            if (eventAggrRoot.isNeedAlert()) {
                DelayRuleVO delayRuleVO = eventAggrRoot.getDelayRule();
                FeishuSendRequest request = FeishuSendRequest
                        .genEventProcessAlarmRequest(eventAggrRoot.getType().getDesc()
                                , eventAggrRoot.getId()
                                , delayRuleVO.getAlarmThreshold()
                                , delayRuleVO.getProcessedCount());
                feishuClient.sendNotice(request);
            }
        } catch (Throwable ignored) {
        }
    }

}
