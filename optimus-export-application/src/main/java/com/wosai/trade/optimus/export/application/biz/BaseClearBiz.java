package com.wosai.trade.optimus.export.application.biz;

import com.wosai.trade.optimus.export.application.biz.context.BaseClearContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Date: 2023/2/16 Time: 4:21 PM
 */
@Slf4j
public abstract class BaseClearBiz<T extends BaseClearContext> extends BaseBiz<T> {

    @SneakyThrows
    @Override
    protected void doBiz(T context) {
        String taskName = getClearTaskName(context);
        log.info("[{}]>>>>>>任务开始", taskName);
        long singleDeleteCount = context.getSingleDeleteCount();
        ThreadLocalRandom random = ThreadLocalRandom.current();
        for (;;) {
            int affectRow = delete(context);
            log.debug("[{}]>>>>>>本次删除数据量[{}]", taskName, affectRow);
            if (affectRow < singleDeleteCount) {
                break;
            }
            //随机暂停[1-500]毫秒，稍微降低一点数据库iops&cpu负载
            TimeUnit.MILLISECONDS.sleep(random.nextInt(500) + 1);
        }
        log.info("[{}]>>>>>>任务结束", taskName);
    }


    protected abstract int delete(T context);
    protected abstract String getClearTaskName(T context);

}
