package com.wosai.trade.optimus.export.application.biz.context.impl.order;


import com.wosai.trade.optimus.export.api.model.req.ExportOrderIDModel;
import com.wosai.trade.optimus.export.api.model.req.ExportSellerIDModel;
import com.wosai.trade.optimus.export.api.request.order.BaseOrderOpRequest;
import com.wosai.trade.optimus.export.application.biz.context.BaseContext;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req.ExportMallOrderQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallOrderQueryResult;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 2:15 PM
 */
@Getter
public abstract class BaseOrderContext extends BaseContext {

    protected final String orderSn;
    protected final String orderSignature;
    protected final String userMerchantId;

    protected ExportMallOrderQueryResult exportMallOrderQueryResult;

    protected BaseOrderContext(BaseOrderOpRequest request) {

        ExportOrderIDModel orderID = request.getOrderID();
        if (Objects.nonNull(orderID)) {
            this.orderSn = orderID.getSn();
            this.orderSignature = orderID.getSignature();
        } else {
            this.orderSn = null;
            this.orderSignature = null;
        }

        ExportSellerIDModel seller = request.getSeller();
        if (Objects.nonNull(seller)) {
            this.userMerchantId = seller.getMerchantId();
        } else {
            this.userMerchantId = null;
        }

    }

    public ExportMallOrderQueryRequest genMallOrderQueryRequest() {
        return ExportMallOrderQueryRequest.builder()
                    .orderSn(orderSn)
                    .orderSignature(orderSignature)
                .build();
    }

    public void bindMallOrderQueryResult(ExportMallOrderQueryResult exportMallOrderQueryResult) {
        this.exportMallOrderQueryResult = exportMallOrderQueryResult;
    }

    public void checkPermission() {
        if (Objects.nonNull(userMerchantId) && !Objects.equals(userMerchantId, exportMallOrderQueryResult.getMerchantId())) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.ORDER_NO_PERMISSION_ERROR);
        }
    }

}
