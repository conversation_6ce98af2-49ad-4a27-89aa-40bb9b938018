package com.wosai.trade.optimus.export.application.adapter.rpc;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.general.annotation.UserContext;
import com.wosai.general.annotation.UsingDataSource;
import com.wosai.general.annotation.UsingDataSourceTypeEnum;
import com.wosai.general.result.PagingResult;
import com.wosai.general.result.SingleResult;
import com.wosai.general.result.VoidResult;
import com.wosai.general.validation.ValidationGroup;
import com.wosai.trade.optimus.api.request.group.OrderExportFileSend;
import com.wosai.trade.optimus.api.request.group.OrderExportTaskCreate;
import com.wosai.trade.optimus.api.request.group.OrderExportTasksPaging;
import com.wosai.trade.optimus.export.api.ExportOrderService;
import com.wosai.trade.optimus.export.api.request.order.OrderExportFileSendRequest;
import com.wosai.trade.optimus.export.api.request.order.OrderExportTaskCreateRequest;
import com.wosai.trade.optimus.export.api.request.order.OrderExportTaskQueryRequest;
import com.wosai.trade.optimus.export.api.request.order.OrderExportTasksPagingRequest;
import com.wosai.trade.optimus.export.api.result.order.OrderExportTaskCreateResult;
import com.wosai.trade.optimus.export.api.result.order.OrderExportTaskQueryResult;
import com.wosai.trade.optimus.export.api.result.order.OrderExportTasksQueryResult;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportFileSendContext;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportTaskCreateContext;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportTaskQueryContext;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportTasksPagingContext;
import com.wosai.trade.optimus.export.application.biz.impl.order.OrderExportFileSendBiz;
import com.wosai.trade.optimus.export.application.biz.impl.order.OrderExportTaskCreateBiz;
import com.wosai.trade.optimus.export.application.biz.impl.order.OrderExportTaskQueryBiz;
import com.wosai.trade.optimus.export.application.biz.impl.order.OrderExportTasksPagingBiz;
import com.wosai.trade.optimus.export.common.util.ResultUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.groups.Default;

/**
 * <AUTHOR> Date: 2023/6/12 Time: 3:39 PM
 */
@Service
@AutoJsonRpcServiceImpl
public class ExportOrderServiceImpl implements ExportOrderService {

    @Resource
    private OrderExportTaskCreateBiz orderExportTaskCreateBiz;
    @Resource
    private OrderExportTasksPagingBiz orderExportTasksPagingBiz;
    @Resource
    private OrderExportFileSendBiz orderExportFileSendBiz;
    @Resource
    private OrderExportTaskQueryBiz orderExportTaskQueryBiz;

    @Override
    @UserContext
    public SingleResult<OrderExportTaskCreateResult> createOrderExportTask(OrderExportTaskCreateRequest request) {
        MallOrderExportTaskCreateContext context = MallOrderExportTaskCreateContext.newInstance(request);
        orderExportTaskCreateBiz.process(context);
        return ResultUtils.buildSuccessfulSingleResult(context.genMallOrderExportTaskCreateResult());
    }

    @Override
    @UserContext
    @UsingDataSource(source = UsingDataSourceTypeEnum.PERIPHERY)
    public PagingResult<OrderExportTasksQueryResult> pagingOrderExportTasks(OrderExportTasksPagingRequest request) {
        MallOrderExportTasksPagingContext context = MallOrderExportTasksPagingContext.newInstance(request);
        orderExportTasksPagingBiz.process(context);
        return ResultUtils.buildSuccessfulPagingResult(context.genMallOrderExportTasksQueryResults()
                , context.getTotal());
    }

    @Override
    @UserContext
    public VoidResult sendExportFile(OrderExportFileSendRequest request) {
        MallOrderExportFileSendContext context = MallOrderExportFileSendContext.newInstance(request);
        orderExportFileSendBiz.process(context);
        return ResultUtils.buildSuccessfulVoidResult();
    }

    @Override
    public SingleResult<OrderExportTaskQueryResult> queryOrderExportTask(OrderExportTaskQueryRequest request) {
        MallOrderExportTaskQueryContext context = MallOrderExportTaskQueryContext.newInstance(request);
        orderExportTaskQueryBiz.process(context);
        return ResultUtils.buildSuccessfulSingleResult(context.genMallOrderExportTaskQueryResult());
    }
}
