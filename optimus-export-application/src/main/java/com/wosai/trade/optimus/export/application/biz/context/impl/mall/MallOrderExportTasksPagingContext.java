package com.wosai.trade.optimus.export.application.biz.context.impl.mall;

import com.google.common.collect.Lists;
import com.wosai.trade.optimus.export.api.model.req.ExportPagingModel;
import com.wosai.trade.optimus.export.api.model.req.ExportSellerIDModel;
import com.wosai.trade.optimus.export.api.request.order.OrderExportTasksPagingRequest;
import com.wosai.trade.optimus.export.api.result.order.OrderExportTasksQueryResult;
import com.wosai.trade.optimus.export.application.biz.context.BaseContext;
import com.wosai.trade.optimus.export.common.constant.CommonConstants;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.enums.ReceiveMethodEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.query.ExportMallOrderAggrQuery;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR> Date: 2023/7/19 Time: 19:13
 */
public class MallOrderExportTasksPagingContext extends BaseContext {
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String DEFAULT_SORT_FIELD = CommonConstants.ID_FIELD;
    private static final boolean DEFAULT_SORTED_DESC = true;
    private final String merchantId;
    private final String merchantUserId;
    private final ExportPagingModel paging;
    private final Byte receiveMethod;

    @Getter
    private long total;
    private List<ExportOrderAggrRoot> exportOrderAggrRoots;


    private MallOrderExportTasksPagingContext(String merchantId
            , String merchantUserId
            , ExportPagingModel paging
            , Byte receiveMethod) {
        this.merchantId = merchantId;
        this.merchantUserId = merchantUserId;
        this.paging = paging;
        this.receiveMethod = receiveMethod;
    }

    public static MallOrderExportTasksPagingContext newInstance(OrderExportTasksPagingRequest request) {
        ExportSellerIDModel seller = request.getSeller();
        return new MallOrderExportTasksPagingContext(seller.getMerchantId()
                , seller.getMerchantUserId()
                , request.getPaging()
                , request.getReceiveMethod());
    }

    public List<OrderExportTasksQueryResult> genMallOrderExportTasksQueryResults() {
        if (CollectionUtils.isEmpty(exportOrderAggrRoots)) {
            return List.of();
        }
        List<OrderExportTasksQueryResult> results = Lists.newArrayListWithCapacity(exportOrderAggrRoots.size());
        for (ExportOrderAggrRoot aggrRoot : exportOrderAggrRoots) {
            OrderExportTasksQueryResult result = new OrderExportTasksQueryResult()
                    .setTaskId(aggrRoot.getCiphertextId())
                    .setState(aggrRoot.getState().getCode())
                    .setEmail(aggrRoot.getExportInfo().getReceiveEmail())
                    .setCreatedAt(DATE_TIME_FORMATTER.format(aggrRoot.getCtime()))
                    .setTaskName(aggrRoot.genExportSimpleTaskName());
            results.add(result);
        }
        return results;
    }

    public void bindTotal(long total) {
        this.total = total;
    }

    public void bindMallExportOrderAggrRoots(List<ExportOrderAggrRoot> exportOrderAggrRoots) {
        this.exportOrderAggrRoots = exportOrderAggrRoots;
    }

    public ExportMallOrderAggrQuery genMallExportOrderAggrQuery() {
        return ExportMallOrderAggrQuery.builder()
                .ownerId(merchantId)
                .ownerUserId(merchantUserId)
                .receiveMethod(ReceiveMethodEnum.ofCode(receiveMethod))
                .offset((paging.getCurrentPage() - 1) * paging.getPageSize())
                .querySize(paging.getPageSize())
                .sortField(DEFAULT_SORT_FIELD)
                .isDesc(DEFAULT_SORTED_DESC)
                .build();
    }


}
