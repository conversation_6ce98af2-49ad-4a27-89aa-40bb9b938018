package com.wosai.trade.optimus.export.application.adapter.mq;

import com.wosai.trade.optimus.export.application.aop.annotation.OptimusExportEntry;
import com.wosai.trade.optimus.export.application.biz.context.impl.event.EventProcessContext;
import com.wosai.trade.optimus.export.application.biz.impl.event.EventProcessBiz;
import com.wosai.trade.optimus.export.application.support.ExecutorFacade;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.template.InvokeProcessor;
import com.wosai.trade.optimus.export.common.template.WithoutResultTemplate;
import com.wosai.trade.optimus.export.domain.aggregate.event.EventDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.query.EventAggrQuery;
import com.wosai.trade.optimus.export.infrastructure.config.support.DataSourceContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR> Date: 2023/1/2 Time: 9:09 PM
 */
@Slf4j
@Component
@ConditionalOnExpression("${kafka.consumer.enable}")
public class KafkaConsumer {
    @Resource
    private EventProcessBiz eventProcessBiz;
    @Resource
    private EventDomainRepository eventDomainRepository;


    @OptimusExportEntry(skipValidate = true, skipLogging = true)
    @KafkaListener(id = "module-event-consumer"
            , groupId = "${spring.kafka.consumer.group-id}"
            , topics = {"${spring.kafka.consumer.topic.module}"})
    public void consumeModuleEvent(List<ConsumerRecord<?, ?>> records) {
        log.info("[导出事件消息]>>>>>>批量拉取长度: " + records.size());
        for (ConsumerRecord<?, ?> consumerRecord : records) {
            log.debug("[导出事件消息]>>>>>>topic: {}, partition: {}, offset: {}" +
                            ", timestamp: {}, key: {}, value: {}"
                    , consumerRecord.topic(), consumerRecord.partition()
                    , consumerRecord.offset(), consumerRecord.timestamp()
                    , consumerRecord.key(), consumerRecord.value());
            Object valueObj = consumerRecord.value();
            if (Objects.isNull(valueObj)) {
                log.warn("[导出事件消息]>>>>>>消息Value为空");
                continue;
            }
            Long eventId = Long.parseLong((String) valueObj);
            EventAggrRoot aggrRoot = eventDomainRepository
                    .query(EventAggrQuery.builder()
                            .id(eventId)
                            .build());
            if (aggrRoot.isNotExist()) {
                log.debug("[导出事件消息]>>>>>>事件不存在, 事件ID: {}", aggrRoot.getId());
                continue;
            }
            if (aggrRoot.isProcessed()) {
                log.debug("[导出事件消息]>>>>>>事件已处理(跳过), 事件ID: {}", aggrRoot.getId());
                continue;
            }
            processEvent(aggrRoot);
        }
    }

    private void processEvent(EventAggrRoot aggrRoot) {
        ThreadPoolExecutor executor = ExecutorFacade.general(aggrRoot.getMqKeyNullDefault());

        executor.execute(() -> {
            InvokeProcessor.processWithoutResult(aggrRoot, new WithoutResultTemplate<>() {
                @Override
                protected void invoke(EventAggrRoot aggrRoot) throws Throwable {
                    try {
                        //标记为次要数据源
                        DataSourceContextHolder.markAsPeripheryDataSource();
                        //事件处理
                        EventProcessContext context = EventProcessContext.newInstance(aggrRoot);
                        eventProcessBiz.process(context);
                    } finally {
                        //清除数据源标记
                        DataSourceContextHolder.clearDataSourceKey();
                    }

                }

                @Override
                protected void onBizFailure(EventAggrRoot eventAggrRoot, OptimusExportBizException e) {
                    log.warn("[导出事件消息]>>>>>>事件消费失败, 业务异常, 异常栈: ", e);
                }

                @Override
                protected void onFailure(EventAggrRoot aggrRoot, Throwable throwable) {
                    log.error("[导出事件消息]>>>>>>事件消费失败, 未知异常, 异常栈: ", throwable);
                }
            });
        });
    }

}
