package com.wosai.trade.optimus.export.application.aop;

import com.wosai.general.annotation.UsingDataSource;
import com.wosai.general.annotation.UsingDataSourceTypeEnum;
import com.wosai.general.util.ValidationUtils;
import com.wosai.general.validation.ValidationGroup;
import com.wosai.trade.optimus.export.application.aop.annotation.OptimusExportEntryParser;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.infrastructure.config.support.DataSourceContextHolder;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/5/23 Time: 2:10 PM
 */
@Slf4j
@Component
@Aspect
@Order(Integer.MIN_VALUE + 1)
public class OptimusExportMethodInterceptor extends BaseInterceptor {

    private static final long NANO_TO_MILLIS = 1000 * 1000;

    @Pointcut("execution (* com.wosai.trade.optimus.export.application.adapter.rpc.*.*(..)))")
    public void rpcPoint() {}
    @Pointcut("execution (* com.wosai.trade.optimus.export.application.adapter.rest.*.*(..)))")
    public void restPoint() {}
    @Pointcut("execution (* com.wosai.trade.optimus.export.application.adapter.mq.*.*(..)))")
    public void mqPoint() {}

    @Resource
    private OptimusExportEntryParser optimusExportEntryParser;

    @SneakyThrows
    @Around("rpcPoint() || restPoint() || mqPoint()")
    public Object invoke(ProceedingJoinPoint point) {
        long begin = System.nanoTime();
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        String calledMethod = methodSignature.getDeclaringType().getSimpleName()
                + "#" + methodSignature.getName();
        Object result = null;
        try {
            loggingRequest(methodSignature, point.getArgs(), calledMethod);
            validate(point);
            tagDataSource(point);
            result = point.proceed();
        } catch(OptimusExportBizException e) {
            log.warn("[调用异常]>>>>>>业务异常, 异常码: " + e.getCode() + ", 异常信息: " + e.getMsg() + ", 异常栈: ", e);
            if (optimusExportEntryParser.isThrowBizException(methodSignature)) {
                throw  e;
            }
            result = genExceptionResult(methodSignature.getReturnType(), e.getCode(), e.getMsg());
        } catch(Throwable t) {
            log.error("[调用异常]>>>>>>系统异常, 异常信息: " + t.getMessage() + ", 异常栈: ", t);
            if (optimusExportEntryParser.isThrowUnknownException(methodSignature)) {
                throw t;
            }
            result = genExceptionResult(methodSignature.getReturnType()
                    , OptimusExportRespCodeEnum.SERVER_ERROR.getCode()
                    , OptimusExportRespCodeEnum.SERVER_ERROR.getMsg());
        } finally {
            clearDataSourceTag();
            loggingResult(methodSignature, result, begin, calledMethod);
        }
        return result;
    }

    private void loggingRequest(MethodSignature methodSignature, Object[] requests
            , String calledMethod) {
        try {
            if (optimusExportEntryParser.isCommonLogging(methodSignature)) {
                log.info("[调用开始]>>>>>>方法: {}, 入参: {}"
                        , calledMethod, JsonUtils.toJsonStringIgnoreException(requests));
                return;
            }
            if (optimusExportEntryParser.isLoggingMethod(methodSignature)) {
                log.info("[调用开始]>>>>>>方法: {}", calledMethod);
            }
        } catch (Throwable throwable) {
            try {
                log.error("[记录入参日志异常]>>>>>>异常栈: ", throwable);
            } catch (Throwable ignored) {}
        }
    }

    public void loggingResult(MethodSignature methodSignature, Object result
            , long begin, String calledMethod) {
        try {
            long end = System.nanoTime();
            if (optimusExportEntryParser.isCommonLogging(methodSignature)) {
                log.info("[调用结束]>>>>>>方法: {}, 出参: {}, 耗时: {}ms"
                        , calledMethod, JsonUtils.toJsonStringIgnoreException(result)
                        , (end - begin) / NANO_TO_MILLIS);
            }
            if (optimusExportEntryParser.isLoggingMethod(methodSignature)) {
                log.info("[调用结束]>>>>>>方法: {}, 耗时: {}ms"
                        , calledMethod, (end - begin) / NANO_TO_MILLIS);
            }
        } catch (Throwable throwable) {
            try {
                log.error("[记录出参日志异常]>>>>>>异常栈: ", throwable);
            } catch (Throwable ignored) {}
        }
    }

    private void validate(ProceedingJoinPoint point) {
        Object[] requests = point.getArgs();
        if (ArrayUtils.isEmpty(requests)) {
            return;
        }
        MethodSignature methodSignature = ((MethodSignature) point.getSignature());
        if (optimusExportEntryParser.isSkipValidate(methodSignature)) {
            return;
        }
        Method method = methodSignature.getMethod();
        ValidationGroup validationGroup = method.getDeclaredAnnotation(ValidationGroup.class);
        Class<?>[] groups = null;
        if (Objects.nonNull(validationGroup)) {
            groups = validationGroup.values();
        }

        for (Object request : requests) {
            if (Objects.nonNull(request)) {
                ValidationUtils.ValidationResult validationResult;
                if (ArrayUtils.isNotEmpty(groups)) {
                    validationResult = ValidationUtils.validate(request, groups);
                } else {
                    validationResult = ValidationUtils.validate(request);
                }
                if (validationResult.isInvalid()) {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.ILLEGAL_ARGUMENT
                            , validationResult.getMsg());
                }
            }
        }
    }


    private void tagDataSource(ProceedingJoinPoint point) {
        try {
            MethodSignature methodSignature = ((MethodSignature) point.getSignature());
            Method method = methodSignature.getMethod();
            UsingDataSource usingDataSource = method.getDeclaredAnnotation(UsingDataSource.class);
            if (Objects.nonNull(usingDataSource)
                    && Objects.equals(usingDataSource.source(), UsingDataSourceTypeEnum.PERIPHERY)) {
                DataSourceContextHolder.markAsPeripheryDataSource();
            }
        } catch (Throwable ignored) {}
    }

    private void clearDataSourceTag() {
        try {
            DataSourceContextHolder.clearDataSourceKey();
        } catch (Throwable ignored) {}
    }

}
