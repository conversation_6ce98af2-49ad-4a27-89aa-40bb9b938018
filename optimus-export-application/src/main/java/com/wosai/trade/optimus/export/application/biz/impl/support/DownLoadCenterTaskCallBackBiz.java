package com.wosai.trade.optimus.export.application.biz.impl.support;

import com.wosai.trade.optimus.export.application.biz.BaseBiz;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportTaskCreateContext;
import com.wosai.trade.optimus.export.application.biz.context.impl.support.DownLoadCenterTaskCallBackContext;
import com.wosai.trade.optimus.export.application.biz.impl.order.OrderExportTaskCreateBiz;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2024/7/22 Time: 10:33
 */
@Service
public class DownLoadCenterTaskCallBackBiz extends BaseBiz<DownLoadCenterTaskCallBackContext> {

    @Resource
    private OrderExportTaskCreateBiz orderExportTaskCreateBiz;

    @Override
    protected void doBiz(DownLoadCenterTaskCallBackContext context) {
        orderExportTaskCreateBiz.process(context.genDownLoadCenterTaskCallBackContext());
    }
}
