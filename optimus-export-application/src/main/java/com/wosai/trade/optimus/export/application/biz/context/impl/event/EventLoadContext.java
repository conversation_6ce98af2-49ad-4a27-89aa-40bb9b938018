package com.wosai.trade.optimus.export.application.biz.context.impl.event;

import com.wosai.trade.optimus.export.application.biz.context.BaseContext;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventStateEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.query.EventAggrQuery;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/7/6 Time: 10:05
 */
public class EventLoadContext extends BaseContext {
    private final Boolean enableLimitProcess;
    private final Integer loadCount;

    private EventLoadContext(Boolean enableLimitProcess, Integer loadCount) {
        this.enableLimitProcess = enableLimitProcess;
        this.loadCount = loadCount;
    }

    public static EventLoadContext newInstance(Boolean enableLimitProcess, Integer loadCount) {
        return new EventLoadContext(enableLimitProcess, loadCount);
    }

    public static EventLoadContext newInstance(Integer loadCount) {
        return new EventLoadContext(Boolean.TRUE, loadCount);
    }

    public EventAggrQuery genEventAggrQuery() {
        return EventAggrQuery.builder()
                .state(EventStateEnum.PENDING)
                .nextProcessTime(LocalDateTime.now())
                .count(loadCount)
                .build();
    }

    public boolean isEnableLimitProcess() {
        return Objects.nonNull(enableLimitProcess) && enableLimitProcess;
    }
}
