package com.wosai.trade.optimus.export.application.aop;

import com.fasterxml.jackson.databind.JsonNode;
import com.wosai.general.annotation.UserContext;
import com.wosai.general.annotation.UserInfo;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.infrastructure.util.HttpRequestUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> Date: 2023/7/15 Time: 13:42
 */
@Slf4j
@Component
@Aspect
@Order(Integer.MIN_VALUE)
public class OptimusExportUserContextInterceptor extends BaseInterceptor {
    private static final String BASE_PATH = "com.wosai.trade";
    private static final String TOKEN_USER_INFO_FIELD_NAME = "user";


    @Pointcut("execution (* com.wosai.trade.optimus.export.application.adapter.rpc.*.*(..)))")
    public void rpcPoint() {}
    @Pointcut("execution (* com.wosai.trade.optimus.export.application.adapter.rest.*.*(..)))")
    public void restPoint() {}


    @Around("rpcPoint() || restPoint()")
    public Object invoke(ProceedingJoinPoint point) {
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        Object result;
        try {
            Object[] objects = point.getArgs();
            Arrays.stream(objects).forEach(object -> process(point, object));
            result = point.proceed();
        } catch (Throwable t) {
            log.error("[调用异常]>>>>>>系统异常, 异常信息: " + t.getMessage() + ", 异常栈: ", t);
            result = genExceptionResult(methodSignature.getReturnType()
                    , OptimusExportRespCodeEnum.SERVER_ERROR.getCode()
                    , OptimusExportRespCodeEnum.SERVER_ERROR.getMsg());
        }
        return result;
    }

    private void process(ProceedingJoinPoint point, Object request) {
        if (Objects.isNull(request)) {
            return;
        }
        MethodSignature methodSignature = ((MethodSignature) point.getSignature());
        Method method = methodSignature.getMethod();
        if (method.isAnnotationPresent(UserContext.class)) {
            JsonNode tokenUser = getTokenUser();
            if (Objects.nonNull(tokenUser)) {
                replaceUserInfo(request.getClass(), request, tokenUser);
            }
        }
    }

    @SneakyThrows
    private void replaceUserInfo(Class<?> clazz, Object request, JsonNode tokenUser) {
        if (Objects.isNull(clazz) || !clazz.getPackageName().startsWith(BASE_PATH)) {
            return;
        }
        for (Field f : clazz.getDeclaredFields()) {
            f.setAccessible(true);
            if (f.isAnnotationPresent(UserContext.class)) {
                Object value = ReflectionUtils.getField(f, request);
                if (Objects.isNull(value)) {
                    value = f.getType().getDeclaredConstructor().newInstance();
                    ReflectionUtils.setField(f, request, value);
                }
                replaceUserInfo(f.getType(), value, tokenUser);
            }
            if (f.isAnnotationPresent(UserInfo.class)) {
                UserInfo userInfo = f.getDeclaredAnnotation(UserInfo.class);
                String tokenUserFieldValue = Optional.ofNullable(tokenUser.get(userInfo.value()))
                        .map(JsonNode::asText)
                        .orElse(null);
                if (StringUtils.isNotEmpty(tokenUserFieldValue)) {
                    ReflectionUtils.setField(f, request, tokenUserFieldValue);
                }
            }
        }
        // 递归处理父类
        replaceUserInfo(clazz.getSuperclass(), request, tokenUser);
    }


    private JsonNode getTokenUser() {
        HttpServletRequest request = HttpRequestUtils.getHttpServletRequest();
        if (Objects.nonNull(request)) {
            String base64User = request.getHeader(TOKEN_USER_INFO_FIELD_NAME);
            if (StringUtils.isNotEmpty(base64User)) {
                String userJsonString = new String(Base64.getDecoder().decode(base64User), StandardCharsets.UTF_8);
                log.info("[获取Token用户信息]>>>>>>user: {}", userJsonString);
                return JsonUtils.toJsonNode(userJsonString);
            }
        }
        return null;
    }

}
