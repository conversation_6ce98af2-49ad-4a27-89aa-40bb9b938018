package com.wosai.trade.optimus.export.application.biz.context.impl.support;

import com.wosai.trade.optimus.export.api.request.order.DownloadCenterTaskCallBackRequest;
import com.wosai.trade.optimus.export.api.support.DownloadCenterTaskCallBackResult;
import com.wosai.trade.optimus.export.application.biz.context.BaseContext;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportTaskCreateContext;

/**
 * <AUTHOR> Date: 2024/7/22 Time: 10:33
 */
public class DownLoadCenterTaskCallBackContext extends BaseContext {

    // 下载中心用于统计下载进度
    private final static Long TOTAL_PROCESS_LENGTH = 100L;

    private final String taskLogId;

    private final Integer type;

    private final DownloadCenterTaskCallBackRequest downloadCenterTaskCallBackRequest;


    private DownLoadCenterTaskCallBackContext(String taskLogId, Integer type, DownloadCenterTaskCallBackRequest downloadCenterTaskCallBackRequest) {
        this.taskLogId = taskLogId;
        this.downloadCenterTaskCallBackRequest = downloadCenterTaskCallBackRequest;
        this.type = type;
    }

    public static DownLoadCenterTaskCallBackContext newInstance(String taskLogId, Integer type, DownloadCenterTaskCallBackRequest downloadCenterTaskCallBackRequest) {
        return new DownLoadCenterTaskCallBackContext(taskLogId, type, downloadCenterTaskCallBackRequest);
    }

    public DownloadCenterTaskCallBackResult genDownLoadCenterTaskResult() {
        return new DownloadCenterTaskCallBackResult().setTotalProgressLength(TOTAL_PROCESS_LENGTH);
    }

    public MallOrderExportTaskCreateContext genDownLoadCenterTaskCallBackContext() {
        MallOrderExportTaskCreateContext mallOrderExportTaskCreateContext = MallOrderExportTaskCreateContext.newInstance(downloadCenterTaskCallBackRequest);
        mallOrderExportTaskCreateContext.bindTaskLogId(taskLogId);
        return mallOrderExportTaskCreateContext;
    }

}
