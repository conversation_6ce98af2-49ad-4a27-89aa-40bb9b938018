package com.wosai.trade.optimus.export.application.biz.context.impl.mall;


import com.wosai.trade.optimus.api.model.req.MallIDModel;
import com.wosai.trade.optimus.export.api.model.req.ExportMallIDModel;
import com.wosai.trade.optimus.export.api.model.req.ExportSellerIDModel;
import com.wosai.trade.optimus.export.api.request.mall.BaseMallOpRequest;
import com.wosai.trade.optimus.export.application.biz.context.BaseContext;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req.ExportMallQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallQueryResult;
import lombok.Getter;

import java.util.Objects;


/**
 * <AUTHOR> Date: 2024/5/10 Time: 2:15 PM
 */
@Getter
public abstract class BaseMallContext extends BaseContext {

    protected final String mallSn;
    protected final String mallSignature;
    protected final String userMerchantId;
    protected ExportMallQueryResult exportMallQueryResult;

    protected BaseMallContext(BaseMallOpRequest request) {

        ExportMallIDModel mallID = request.getMallID();
        if (Objects.nonNull(mallID)) {
            this.mallSn = mallID.getMallSn();
            this.mallSignature = mallID.getSignature();
        } else {
            this.mallSn = null;
            this.mallSignature = null;
        }

        ExportSellerIDModel seller = request.getSeller();
        if (Objects.nonNull(seller)) {
            this.userMerchantId = seller.getMerchantId();
        } else {
            this.userMerchantId = null;
        }
    }

    public ExportMallQueryRequest genOptimusMallQueryRequest() {
        return ExportMallQueryRequest.builder()
                .mallID(MallIDModel.builder()
                        .mallSn(mallSn)
                        .signature(mallSignature)
                        .build())
                .build();
    }

    public void bindOptimusMallQueryResult(ExportMallQueryResult exportMallQueryResult) {
        this.exportMallQueryResult = exportMallQueryResult;
    }

    public void checkPermission() {
        if (Objects.nonNull(userMerchantId) && !Objects.equals(userMerchantId, exportMallQueryResult.getMerchantId())) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.MALL_NO_PERMISSION_ERROR);
        }
    }

    public Long getMallId() {
        if (Objects.nonNull(exportMallQueryResult)) {
            return Long.parseLong(exportMallQueryResult.getMallSn());
        }
        String mallSn = getMallSn();
        if (Objects.nonNull(mallSn)) {
            return Long.parseLong(getMallSn());
        }
        return null;
    }


}
