package com.wosai.trade.optimus.export.application.biz.impl.handler.event;

import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.trade.optimus.export.application.biz.impl.handler.event.context.EventStrategyContext;
import com.wosai.trade.optimus.export.application.biz.impl.handler.event.holder.EventStrategyHolder;
import com.wosai.trade.optimus.export.application.support.EventProcessSupportService;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.infrastructure.support.ExternalInvokeThreadPoolFacade;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2023/5/6 Time: 2:32 PM
 */
@Slf4j
public abstract class BaseIndependentEventStrategy<T extends EventStrategyContext<?>> extends EventStrategyHolder<T> {

    @Resource
    protected ExternalInvokeThreadPoolFacade externalInvokeThreadPoolFacade;
    @Resource
    protected EventProcessSupportService eventProcessSupportService;

    @Override
    public void execute(T context) {
        EventAggrRoot eventAggrRoot = context.getEventAggrRoot();
        //抢占事件
        if (tryPreemptEvent(eventAggrRoot.getId())) {
            try {
                EventAggrRoot latestEvent = eventDomainRepository.query(context.genEventAggrQuery());
                if (latestEvent.isProcessed()) {
                    //事件已处理，释放事件独占，退出处理
                    releaseEvent(eventAggrRoot.getId());
                    log.debug("[独立事件处理]>>>>>>事件已处理(跳过)");
                    return;
                }
                //更新上下文中绑定的事件
                context.updateBoundEventAggrRoot(latestEvent);
                //提交线程池异步处理
                externalInvokeThreadPoolFacade.execute(null
                        , RunnableWrapper.of(getIndependentEventStrategyRunnable(context)));
            } catch (Throwable throwable) {
                //任务未提交成功就异常了，需在此处释放占用的事件
                releaseEvent(eventAggrRoot.getId());
                throw throwable;
            }
        }
    }

    protected void secureUpdateEvent(T context) {
        EventAggrRoot aggrRoot = context.getEventAggrRoot();
        try {
            //存储事件
            eventDomainRepository.save(aggrRoot);
            //提交事务
            context.commit();
        } catch (Throwable throwable) {
            //回滚事务
            context.rollback();
            //告警处理
            eventProcessSupportService.sendAlarm(aggrRoot);
            log.error("[独立事件处理]>>>>>>事件保存异常, 异常栈: ", throwable);
        } finally {
            //释放占用
            releaseEvent(aggrRoot.getId());
        }
    }

    protected abstract IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(T context);

    protected abstract static class IndependentEventStrategyRunnable implements Runnable {}

}
