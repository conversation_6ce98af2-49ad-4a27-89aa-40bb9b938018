package com.wosai.trade.optimus.export.application.support;

import com.wosai.trade.optimus.export.application.biz.impl.handler.event.context.EventStrategyContext;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.reflections.Reflections;
import org.reflections.scanners.Scanners;
import org.reflections.util.ConfigurationBuilder;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ApplicationContextEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Date: 2023/8/28 Time: 15:20
 */
@Slf4j
@Component
public class OptimusApplicationListener implements ApplicationListener<ApplicationContextEvent> {

    @Override
    public void onApplicationEvent(@NotNull ApplicationContextEvent event) {
        if (event instanceof ContextRefreshedEvent) {
            loadClass();
            log.info("[OptimusModule应用容器启动初始化]......");
        } else if (event instanceof ContextClosedEvent) {
            ExecutorFacade.destroy();
            log.info("[OptimusModule应用容器关闭销毁]......");
        }
    }

    private void loadClass() {
        Reflections reflections = new Reflections(new ConfigurationBuilder()
                .forPackage("com.wosai.trade.optimus.export.application.biz")
                .addScanners(Scanners.SubTypes)
        );
        var classSet = reflections.getSubTypesOf(EventStrategyContext.class);
        log.info("[获取EventStrategyContext子类]>>>>>>classSet: {}", classSet);
        if (CollectionUtils.isEmpty(classSet)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.BIZ_OBJECT_MISSING);
        }
        classSet.forEach(aClass -> {
            try {
                Class.forName(aClass.getName());
            } catch (ClassNotFoundException e) {
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.SERVER_ERROR);
            }
        });
    }
}
