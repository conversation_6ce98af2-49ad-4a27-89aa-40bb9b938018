package com.wosai.trade.optimus.export.application.biz.context.impl.mall;

import com.wosai.trade.optimus.export.api.request.order.OrderExportTaskQueryRequest;
import com.wosai.trade.optimus.export.api.result.order.OrderExportTaskQueryResult;
import com.wosai.trade.optimus.export.application.biz.context.BaseContext;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.query.ExportMallOrderAggrQuery;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model.OssGeneratePreSignedUrlResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model.OssUploadRequest;

/**
 * <AUTHOR> Date: 2024/4/18 Time: 09:30
 */
public class MallOrderExportTaskQueryContext extends BaseContext {

    private final String taskId;

    private ExportOrderAggrRoot exportOrderAggrRoot;

    private OssGeneratePreSignedUrlResult urlResult;

    private MallOrderExportTaskQueryContext(OrderExportTaskQueryRequest request) {
        this.taskId = request.getTaskId();
    }

    public static MallOrderExportTaskQueryContext newInstance(OrderExportTaskQueryRequest request) {
        return new MallOrderExportTaskQueryContext(request);
    }

    public OssUploadRequest genOssUploadRequest(String bucketName) {
        return OssUploadRequest.builder()
                .path(exportOrderAggrRoot.genFilePath())
                .bucketName(bucketName).build();
    }

    public ExportMallOrderAggrQuery genMallExportOrderAggrQuery() {
        return ExportMallOrderAggrQuery.builder()
                .id(Long.valueOf(ExportOrderAggrRoot.getPlaintextId(taskId)))
                .build();
    }

    public OrderExportTaskQueryResult genMallOrderExportTaskQueryResult() {
        if (urlResult.isInvokeSucceed()) {
            return new OrderExportTaskQueryResult().setUrl(urlResult.getUrl());
        }
        return OrderExportTaskQueryResult.DEFAULT;
    }

    public void bindMallExportOrderAggrRoot(ExportOrderAggrRoot exportOrderAggrRoot) {
        this.exportOrderAggrRoot = exportOrderAggrRoot;
    }


    public void bindOssGeneratePresignedUrlResult(OssGeneratePreSignedUrlResult urlResult) {
        this.urlResult = urlResult;
    }

}
