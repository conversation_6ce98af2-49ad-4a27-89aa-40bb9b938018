package com.wosai.trade.optimus.export.application.adapter.rest;

import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.trade.optimus.export.application.biz.context.impl.event.EventProcessContext;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportTaskClearContext;
import com.wosai.trade.optimus.export.application.biz.impl.event.EventProcessBiz;
import com.wosai.trade.optimus.export.application.biz.impl.order.MallOrderExportTaskClearBiz;
import com.wosai.trade.optimus.export.domain.aggregate.event.EventDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.query.EventAggrQuery;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <AUTHOR> Date: 2023/1/9 Time: 2:19 PM
 */
@Slf4j
@RestController()
@RequestMapping("/export/admin")
public class AdminController {
    private static final ResponseEntity<String> SUCCESS_RESULT = ResponseEntity.ok("success");
    private static final ResponseEntity<String> SKIP_RESULT = ResponseEntity.ok("skip");

    @Resource
    private EventProcessBiz eventProcessBiz;
    @Resource
    private EventDomainRepository eventDomainRepository;
    @Resource
    private MallOrderExportTaskClearBiz mallOrderExportTaskClearBiz;

    @PutMapping("/event/process/{eventId}")
    public ResponseEntity<String> processEvent(@PathVariable("eventId") Long eventId) {
        EventAggrRoot eventAggrRoot = eventDomainRepository.query(EventAggrQuery.builder()
                .id(eventId)
                .build());
        if (eventAggrRoot.isNotExist()) {
            return SKIP_RESULT;
        }
        eventProcessBiz.process(EventProcessContext.newInstance(eventAggrRoot));
        return SUCCESS_RESULT;
    }

    @PutMapping("/event/state/pending-manual-processing/{eventId}")
    public ResponseEntity<String> updateEvenToPendingManualProcessing(@PathVariable("eventId") Long eventId) {
        EventAggrRoot eventAggrRoot = eventDomainRepository.query(EventAggrQuery.builder()
                .id(eventId)
                .build());
        if (eventAggrRoot.isNotExist()) {
            return SKIP_RESULT;
        }
        eventAggrRoot.updateToPendingManualProcessing();
        eventDomainRepository.save(eventAggrRoot);
        return SUCCESS_RESULT;
    }

    @GetMapping("/orderexport/clear/{deleteDaysBefore}/{singleDeleteCount}")
    public ResponseEntity<String> clearExpiredOrderExport(
            @PathVariable("deleteDaysBefore") Long deleteDaysBefore
            , @PathVariable("singleDeleteCount") Long singleDeleteCount) {
        new Thread(RunnableWrapper.of(() -> {
            MallOrderExportTaskClearContext context = MallOrderExportTaskClearContext
                    .newInstance(deleteDaysBefore, singleDeleteCount);
            mallOrderExportTaskClearBiz.process(context);
        })).start();
        return SUCCESS_RESULT;
    }


    @Getter
    @Setter
    public static class SellerReportedAppIdUpdateRequest {
        private String merchantSn;
        private String appId;
    }


}