package com.wosai.trade.optimus.export.application.biz.impl.handler.event.context;

import com.wosai.trade.optimus.export.application.biz.impl.handler.event.holder.EventHandlerContext;
import com.wosai.trade.optimus.export.application.support.TransactionManageContext;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.query.EventAggrQuery;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> Date: 2023/1/3 Time: 11:39 AM
 */
@Slf4j
@Getter
public abstract class EventStrategyContext<E> extends TransactionManageContext {
    private static final Map<EventTypeEnum, EventStrategyContext<?>> EVENT_CONTEXT_MAP
            = new ConcurrentHashMap<>();

    private EventAggrRoot eventAggrRoot;


    protected static void registerContext(EventTypeEnum type, EventStrategyContext<?> contextClass) {
        EVENT_CONTEXT_MAP.put(type, contextClass);
        log.info("[事件策略注册]>>>>>>type: {}, contextClass: {}", type, contextClass);
    }

    protected EventStrategyContext() {
    }

    protected EventStrategyContext(EventAggrRoot eventAggrRoot) {
        this.eventAggrRoot = eventAggrRoot;
    }

    public static EventStrategyContext<?> newEventStrategyContext(EventHandlerContext context) {
        EventAggrRoot aggrRoot = context.getEventAggrRoot();
        EventStrategyContext<?> emptyContext = EVENT_CONTEXT_MAP.get(aggrRoot.getType());
        return emptyContext.rebuildContext(context);
    }

    public void updateBoundEventAggrRoot(EventAggrRoot eventAggrRoot) {
        this.eventAggrRoot = eventAggrRoot;
    }

    public EventAggrQuery genEventAggrQuery() {
        return EventAggrQuery.builder()
                .id(eventAggrRoot.getId())
                .build();
    }


    public abstract EventStrategyContext<E> rebuildContext(EventHandlerContext context);

    public abstract E getBizContent();

}
