package com.wosai.trade.optimus.export.application.aop.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR> Date: 2023/6/12 Time: 3:21 PM
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface OptimusExportEntry {

    boolean skipValidate() default false;

    boolean skipLogging() default false;

    boolean skipLoggingParams() default false;

    boolean throwUnknownException() default false;

    boolean throwBizException() default false;

}
