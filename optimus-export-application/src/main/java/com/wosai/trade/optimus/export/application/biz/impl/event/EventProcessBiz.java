package com.wosai.trade.optimus.export.application.biz.impl.event;

import com.wosai.middleware.hera.toolkit.trace.Trace;
import com.wosai.trade.optimus.export.application.biz.BaseBiz;
import com.wosai.trade.optimus.export.application.biz.context.impl.event.EventProcessContext;
import com.wosai.trade.optimus.export.application.biz.impl.handler.event.holder.EventStrategyHolder;
import com.wosai.trade.optimus.export.application.support.EventProcessSupportService;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.template.InvokeProcessor;
import com.wosai.trade.optimus.export.common.template.WithoutResultTemplate;
import com.wosai.trade.optimus.export.domain.aggregate.event.EventDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2023/6/21 Time: 15:12
 */
@Slf4j
@Service
public class EventProcessBiz extends BaseBiz<EventProcessContext> {

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private EventDomainRepository eventDomainRepository;
    @Resource
    private EventProcessSupportService eventProcessSupportService;


    @Trace
    @Override
    protected void doBiz(EventProcessContext context) {
        Long eventId = context.getEventId();
        log.info("[事件处理]>>>>>>事件ID: {}", eventId);
        EventAggrRoot eventAggrRoot = context.getEventAggrRoot();
        if (eventAggrRoot.isNotExist()) {
            log.debug("[事件处理]>>>>>>事件不存在");
            return;
        }
        if (eventAggrRoot.isProcessed()) {
            log.debug("[事件处理]>>>>>>事件已处理(跳过)");
            return;
        }
        processUnifiedEvent(context);

    }


    /**
     * 统一处理事件，处理前加排它锁，事件处理器只需要关注实际业务，无需关心重复处理、失败重试、异常记录等问题
     *
     * @param context
     */
    private void processUnifiedEvent(EventProcessContext context) {
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            EventAggrRoot eventAggrRoot = eventDomainRepository
                    .queryWithLockSkipLocked(context.genEventAggrQuery());
            if (eventAggrRoot.isNotExist()) {
                log.debug("[事件处理]>>>>>>未获取到锁");
                return;
            }
            if (eventAggrRoot.isProcessed()) {
                log.debug("[事件处理]>>>>>>事件已处理(跳过)");
                return;
            }
            //绑定事件至上下文
            context.bindEventAggrRoot(eventAggrRoot);

            Object savePoint = transactionStatus.createSavepoint();
            InvokeProcessor.processWithoutResult(context, new WithoutResultTemplate<>() {
                @Override
                protected void invoke(EventProcessContext context) throws Throwable {
                    EventStrategyHolder.process(context.genEventHandlerContext());
                }

                @Override
                protected void onBizFailure(EventProcessContext context, OptimusExportBizException e) {
                    transactionStatus.rollbackToSavepoint(savePoint);

                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    eventProcessSupportService.sendAlarm(aggrRoot);
                    String result = e.getCode() + ":" + e.getMsg();
                    log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                            + result + ", 异常栈: ", e);
                    aggrRoot.processFailure("业务异常: " + result);
                }

                @Override
                protected void onFailure(EventProcessContext context, Throwable throwable) {
                    transactionStatus.rollbackToSavepoint(savePoint);

                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    eventProcessSupportService.sendAlarm(aggrRoot);
                    log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ", throwable);
                    aggrRoot.processFailure("未知异常");
                }

                @Override
                protected void after(EventProcessContext context) {
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    eventDomainRepository.save(aggrRoot);
                }
            });
        });
    }


    /**
     * 独立处理事件，事件处理器需自行去重、更新事件的消费情况等
     *
     * @param context
     */
    private void processIndependentEvent(EventProcessContext context) {
        EventStrategyHolder.process(context.genEventHandlerContext());
    }


}
