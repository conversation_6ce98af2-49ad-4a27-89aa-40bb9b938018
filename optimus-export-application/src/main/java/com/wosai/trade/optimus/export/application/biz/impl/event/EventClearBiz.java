package com.wosai.trade.optimus.export.application.biz.impl.event;

import com.wosai.trade.optimus.export.application.biz.BaseClearBiz;
import com.wosai.trade.optimus.export.application.biz.context.impl.event.EventClearContext;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.EventDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2023/8/14 Time: 09:48
 */
@Service
public class EventClearBiz extends BaseClearBiz<EventClearContext> {

    @Resource
    private EventDao eventDao;

    @Override
    protected int delete(EventClearContext context) {
        return eventDao.delete(context.genEventDelete());
    }

    @Override
    protected String getClearTaskName(EventClearContext context) {
        return "清除业务事件";
    }

}
