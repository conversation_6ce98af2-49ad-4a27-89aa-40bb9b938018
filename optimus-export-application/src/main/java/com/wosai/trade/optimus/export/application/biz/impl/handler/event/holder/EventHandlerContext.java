package com.wosai.trade.optimus.export.application.biz.impl.handler.event.holder;

import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import lombok.Getter;

/**
 * <AUTHOR> Date: 2023/6/21 Time: 14:55
 */
@Getter
public class EventHandlerContext {
    private final EventAggrRoot eventAggrRoot;

    private EventHandlerContext(EventAggrRoot eventAggrRoot) {
        this.eventAggrRoot = eventAggrRoot;
    }


    public static EventHandlerContext newInstance(EventAggrRoot eventAggrRoot) {
        return new EventHandlerContext(eventAggrRoot);
    }


}
