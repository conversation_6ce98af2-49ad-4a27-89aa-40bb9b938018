package com.wosai.trade.optimus.export.application.biz.impl.handler.event;


import com.wosai.trade.optimus.export.application.biz.impl.handler.event.context.EventStrategyContext;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR> Date: 2023/2/27 Time: 3:28 PM
 */
public abstract class BaseEventStrategy<T extends EventStrategyContext<?>> {

    protected abstract Pair<EventTypeEnum, BaseEventStrategy<T>> register();

    public abstract void execute(T context);

}
