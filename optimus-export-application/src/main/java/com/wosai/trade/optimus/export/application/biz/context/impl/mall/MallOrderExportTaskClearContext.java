package com.wosai.trade.optimus.export.application.biz.context.impl.mall;

import com.wosai.trade.optimus.export.application.biz.context.BaseClearContext;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.model.MallExportOrderDelete;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Date: 2023/7/19 Time: 18:42
 */
public class MallOrderExportTaskClearContext extends BaseClearContext {
    private static final long MINIMUM_HOLDING_DAYS = 3;
    private static final long MAX_SINGLE_DELETE_COUNT = 10000;
    private static final long MIN_SINGLE_DELETE_COUNT = 500;
    private static final long DEFAULT_SINGLE_DELETE_COUNT = 1000;

    protected MallOrderExportTaskClearContext(Long deleteDaysBefore, Long singleDeleteCount) {
        super(deleteDaysBefore, singleDeleteCount);
    }

    public static MallOrderExportTaskClearContext newInstance(Long deleteDaysBefore, Long singleDeleteCount) {
//        if (Objects.isNull(deleteDaysBefore) || deleteDaysBefore < MINIMUM_HOLDING_DAYS) {
//            deleteDaysBefore = MINIMUM_HOLDING_DAYS;
//        }
//        if (Objects.isNull(singleDeleteCount) || singleDeleteCount > MAX_SINGLE_DELETE_COUNT
//                || singleDeleteCount < MIN_SINGLE_DELETE_COUNT) {
//            singleDeleteCount = DEFAULT_SINGLE_DELETE_COUNT;
//        }
        return new MallOrderExportTaskClearContext(deleteDaysBefore, singleDeleteCount);
    }



    public MallExportOrderDelete genMallExportOrderDelete() {
        return MallExportOrderDelete.builder()
                .expiredAt(LocalDateTime.now().minusDays(getDeleteDaysBefore()))
                .count(getSingleDeleteCount())
                .build();
    }
}
