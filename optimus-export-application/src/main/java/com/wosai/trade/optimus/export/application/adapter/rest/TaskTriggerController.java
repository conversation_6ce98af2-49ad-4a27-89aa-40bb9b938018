package com.wosai.trade.optimus.export.application.adapter.rest;

import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.trade.optimus.export.application.biz.context.impl.event.EventClearContext;
import com.wosai.trade.optimus.export.application.biz.context.impl.event.EventLoadContext;
import com.wosai.trade.optimus.export.application.biz.impl.event.EventClearBiz;
import com.wosai.trade.optimus.export.application.biz.impl.event.EventLoadBiz;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Date: 2023/5/25 Time: 9:41 AM
 */
@Slf4j
@RestController
@RequestMapping("/export/task")
public class TaskTriggerController {

    private static final ExecutorService EVENT_LOAD_EXECUTOR
            = new ThreadPoolExecutor(5, 20, 10
            , TimeUnit.MINUTES,  new ArrayBlockingQueue<>(20)
            , new ThreadPoolExecutor.AbortPolicy());
    private static final ResponseEntity<String> SUCCESS_RESULT = ResponseEntity.ok("success");
    private static final List<Pair<Byte, Period>> CLEARABLE_EVENTS = EventStateEnum.listClearableEventPairs();

    @Resource
    private EventLoadBiz eventLoadBiz;
    @Resource
    private EventClearBiz eventClearBiz;

    @GetMapping("/event/load/{count}")
    public ResponseEntity<String> loadEvent(@PathVariable("count") Integer count) {
        EventLoadContext context = EventLoadContext.newInstance(count);
        EVENT_LOAD_EXECUTOR.execute(RunnableWrapper.of(() -> eventLoadBiz.process(context)));
        return SUCCESS_RESULT;
    }

    @GetMapping("/event/clear/{singleDeleteCount}")
    public ResponseEntity<String> clearEvent (@PathVariable("singleDeleteCount") Long singleDeleteCount) {
        for (Pair<Byte, Period> pair : CLEARABLE_EVENTS) {
            new Thread(RunnableWrapper.of(() -> {
                long daysBefore = pair.getRight().get(ChronoUnit.DAYS);
                EventClearContext context = EventClearContext.newInstance(daysBefore
                        , singleDeleteCount
                        , pair.getLeft());
                eventClearBiz.process(context);
            })).start();
        }
        return SUCCESS_RESULT;
    }



}
