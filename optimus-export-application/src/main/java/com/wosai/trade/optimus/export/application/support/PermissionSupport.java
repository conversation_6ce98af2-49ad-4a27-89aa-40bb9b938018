package com.wosai.trade.optimus.export.application.support;

import com.wosai.trade.optimus.api.model.req.SellerIDModel;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res.SimpleStoreInfoQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.MerchantUserClient;
import com.wosai.trade.optimus.export.infrastructure.support.MerchantUserOwnerStoreFacade;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Date: 2023/7/21 Time: 16:26
 */
@Component
public class PermissionSupport {

    @Resource
    private MerchantUserClient merchantUserClient;
    @Resource
    private MerchantUserOwnerStoreFacade merchantUserOwnerStoreFacade;

    public List<String> getAndCheckOwnerStoreSnList(SellerIDModel seller) {
        SimpleStoreInfoQueryResult result = merchantUserOwnerStoreFacade.queryOwnerStore(seller.getMerchantUserId());
        List<String> inputStoreSnList = seller.getStoreSnList();
        if (CollectionUtils.isEmpty(inputStoreSnList)) {
            return result.getStoreSnList();
        }
        List<String> ownerStoreSnList = result.genContainsStoreSnList(inputStoreSnList);
        if (inputStoreSnList.size() != ownerStoreSnList.size()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.PERMISSION_INSUFFICIENT);
        }
        return ownerStoreSnList;
    }



}
