package com.wosai.trade.optimus.export.application.biz.context.impl.event;

import com.wosai.trade.optimus.export.application.biz.context.BaseContext;
import com.wosai.trade.optimus.export.application.biz.impl.handler.event.holder.EventHandlerContext;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.query.EventAggrQuery;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/6/21 Time: 15:12
 */
@Getter
public class EventProcessContext extends BaseContext {
    private EventAggrRoot eventAggrRoot;

    private EventProcessContext(EventAggrRoot eventAggrRoot) {
        if (Objects.isNull(eventAggrRoot) || eventAggrRoot.isNotExist()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.EVENT_NOT_EXIST);
        }
        this.eventAggrRoot = eventAggrRoot;
    }

    public static EventProcessContext newInstance(EventAggrRoot eventAggrRoot) {
        return new EventProcessContext(eventAggrRoot);
    }

    public EventAggrQuery genEventAggrQuery() {
        return EventAggrQuery.builder()
                .id(getEventId())
                .build();
    }

    public void bindEventAggrRoot(EventAggrRoot eventAggrRoot) {
        this.eventAggrRoot = eventAggrRoot;
    }

    public Long getEventId() {
        return Objects.isNull(eventAggrRoot) ? null : eventAggrRoot.getId();
    }

    public EventHandlerContext genEventHandlerContext() {
        return EventHandlerContext.newInstance(eventAggrRoot);
    }
}
