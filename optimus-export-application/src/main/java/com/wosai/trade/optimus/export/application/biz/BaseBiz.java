package com.wosai.trade.optimus.export.application.biz;

import com.wosai.trade.optimus.export.application.biz.context.BaseContext;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.NamespaceEnum;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.errormsg.ErrorMsgDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;


/**
 * <AUTHOR> Date: 2022/11/24 Time: 4:54 PM
 */
@Slf4j
public abstract class BaseBiz<T extends BaseContext> implements Biz {
    private static final String OPTIMUS_ERROR_MSG_TEMPLATE = "%s-%s";

    @Resource
    protected ErrorMsgDao errorMsgDao;

    public void process(T context) {
        try {
            //预处理
            preProcess(context);
            //处理业务
            processBiz(context);
            //后处理
            postProcess(context);
        } catch(OptimusExportBizException e) {
            processBizException(context, e);
            throw e;
        } finally {
            processAfter(context);
        }
    }

    private void preProcess(T context) {
        doBefore(context);
    }

    private void processBiz(T context) {
        preBiz(context);
        doBiz(context);
        postBiz(context);
    }

    private void postProcess(T context) {
        doSuccess(context);
    }

    private void processAfter(T context) {
        doAfter(context);
    }

    private void processBizException(T context, OptimusExportBizException e) {
        doBizException(context, e);
        mappingErrorMsg(e);
    }

    private void mappingErrorMsg(OptimusExportBizException e) {
        String code = e.getCode();
        String msg = e.getMsg();
        String key = String.format(OPTIMUS_ERROR_MSG_TEMPLATE, code, msg);
        String configMsg = errorMsgDao.query(key, NamespaceEnum.ERROR_MSG_OPTIMUS_MODULE);
        if (StringUtils.isNotEmpty(configMsg)) {
            e.setMsg(configMsg);
            return;
        }
        configMsg = errorMsgDao.query(e.getCode(), NamespaceEnum.ERROR_MSG_OPTIMUS_MODULE);
        if (StringUtils.isNotEmpty(configMsg)) {
            e.setMsg(configMsg);
            return;
        }
        configMsg = errorMsgDao.query(e.getMsg(), NamespaceEnum.ERROR_MSG_OPTIMUS_MODULE);
        if (StringUtils.isNotEmpty(configMsg)) {
            e.setMsg(configMsg);
        }
    }

    protected void postBiz(T context) {}

    protected void preBiz(T context) {}

    protected void doBefore(T context) {}

    protected void doSuccess(T context) {}

    protected void doAfter(T context) {}

    protected void doBizException(T context, OptimusExportBizException e) {}

    protected abstract void doBiz(T context);

}
