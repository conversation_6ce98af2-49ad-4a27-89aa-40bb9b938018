package com.wosai.trade.optimus.export.application.biz.impl.handler.event.holder;

import com.wosai.trade.optimus.export.application.biz.impl.handler.event.BaseEventStrategy;
import com.wosai.trade.optimus.export.application.biz.impl.handler.event.context.EventStrategyContext;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.EventDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> Date: 2023/1/3 Time: 11:32 AM
 */
public abstract class EventStrategyHolder<T extends EventStrategyContext<?>> extends BaseEventStrategy<T> {
    private static final Map<EventTypeEnum/*事件类型*/, BaseEventStrategy<? extends EventStrategyContext<?>>> STRATEGY_MAP = new ConcurrentHashMap<>();
    private static final ConcurrentMap<Long, Object> PROCESSING_EVENT_MAP = new ConcurrentHashMap<>(1024);

    @Resource
    protected EventDomainRepository eventDomainRepository;

    @PostConstruct
    protected void init() {
        Pair<EventTypeEnum, BaseEventStrategy<T>> pair = register();
        STRATEGY_MAP.put(pair.getLeft(), pair.getRight());
    }

    @SuppressWarnings("all")
    public static <T extends EventStrategyContext<?>> void process(EventHandlerContext context) {
        EventAggrRoot eventAggrRoot = context.getEventAggrRoot();
        EventStrategyContext<?> strategyContext = EventStrategyContext.newEventStrategyContext(context);
        BaseEventStrategy<T> eventStrategy
                = (BaseEventStrategy<T>) STRATEGY_MAP.get(eventAggrRoot.getType());
        if (Objects.isNull(eventStrategy)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.EVENT_STRATEGY_NOT_EXIST);
        }
        eventStrategy.execute((T) strategyContext);
    }

    protected boolean tryPreemptEvent(Long eventId) {
        return Objects.isNull(PROCESSING_EVENT_MAP.putIfAbsent(eventId, eventId));
    }

    protected void releaseEvent(Long eventId) {
        PROCESSING_EVENT_MAP.remove(eventId, eventId);
    }


}
