package com.wosai.trade.optimus.export.application.biz.context.impl.event;

import com.wosai.trade.optimus.export.application.biz.context.BaseClearContext;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.model.EventDelete;
import com.wosai.trade.optimus.export.infrastructure.support.DefaultSerialGenerator;

/**
 * <AUTHOR> Date: 2023/8/14 Time: 09:51
 */
public class EventClearContext extends BaseClearContext {
    private final Byte state;

    protected EventClearContext(Long deleteDaysBefore, Long singleDeleteCount, Byte state) {
        super(deleteDaysBefore, singleDeleteCount);
        this.state = state;
    }

    public static EventClearContext newInstance(Long deleteDaysBefore, Long singleDeleteCount, Byte state) {
        return new EventClearContext(deleteDaysBefore, singleDeleteCount, state);
    }

    public EventDelete genEventDelete() {
        return EventDelete.builder()
                .idBefore(DefaultSerialGenerator.getInstance().getDateBeginId(getDeleteDateBefore()))
                .count(getSingleDeleteCount())
                .state(state)
                .build();
    }

}
