package com.wosai.trade.optimus.export.application.biz.impl.order;

import com.wosai.trade.optimus.export.application.biz.BaseBiz;
import com.wosai.trade.optimus.export.application.biz.context.impl.mall.MallOrderExportTaskCreateContext;
import com.wosai.trade.optimus.export.domain.aggregate.event.EventDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.ExportOrderDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.CoreBusinessClient;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res.MerchantQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.MerchantUserClient;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.res.OptimusGroupUserMerchantQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.OptimusCoreClient;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallListQueryResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;


/**
 * <AUTHOR> Date: 2023/7/18 Time: 11:05
 */
@Service
public class OrderExportTaskCreateBiz extends BaseBiz<MallOrderExportTaskCreateContext> {

    @Resource
    private CoreBusinessClient coreBusinessClient;
    @Resource
    private MerchantUserClient merchantUserClient;
    @Resource
    private OptimusCoreClient optimusCoreClient;
    @Resource
    private ExportOrderDomainRepository exportOrderDomainRepository;
    @Resource
    private EventDomainRepository eventDomainRepository;
    @Resource
    private TransactionTemplate transactionTemplate;


    @Override
    protected void doBefore(MallOrderExportTaskCreateContext context) {
        super.doBefore(context);

        if (context.isGroupUser()) {
            OptimusGroupUserMerchantQueryResult groupUserMerchantQueryResult = merchantUserClient.getGroupUserMerchants(context.genOptimusGroupUserMerchantQueryRequest());
            groupUserMerchantQueryResult.checkMerchantListExist();
            groupUserMerchantQueryResult.checkMerchantPermission(groupUserMerchantQueryResult.getMerchantSnList());
            context.bindOptimusGroupUserMerchantQueryResult(groupUserMerchantQueryResult);
        } else {
            MerchantQueryResult merchantQueryResult = coreBusinessClient.queryMerchant(context.genMerchantQueryRequest());
            merchantQueryResult.checkExist();
            context.bindMerchantQueryResult(merchantQueryResult);
        }

        ExportMallListQueryResult mallListQueryResult = optimusCoreClient
                .queryMallList(context.genExportMallListQueryRequest(context.genMallSnSignatureMaps()));
        mallListQueryResult.checkExist();
        mallListQueryResult.checkTerminalSnList();
        mallListQueryResult.checkMallPermission(context.getMallIDList().size(), context.getOwnerMerchantSnList());
        context.bindExportMallListQueryResult(mallListQueryResult);
    }

    @Override
    protected void doBiz(MallOrderExportTaskCreateContext context) {

        ExportOrderAggrRoot exportOrderAggrRoot = context.genMallExportOrderAggrRoot();
        context.bindMallExportOrderAggrRoot(exportOrderAggrRoot);

        ExportOrderAggrRoot sameExportDateAggrRoot = exportOrderDomainRepository
                .query(context.genMallExportOrderAggrQuery());
        if (sameExportDateAggrRoot.isExist()) {
            exportOrderAggrRoot.replaceResult(sameExportDateAggrRoot);
        }

        EventAggrRoot eventAggrRoot = context.genOrderExportEvent();
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            exportOrderDomainRepository.save(exportOrderAggrRoot);
            eventDomainRepository.save(eventAggrRoot);
        });
    }

}
