package com.wosai.trade.optimus.export.application.biz.impl.handler.event.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.builder.ExcelWriterTableBuilder;
import com.wosai.trade.optimus.export.application.biz.impl.handler.event.BaseEventStrategy;
import com.wosai.trade.optimus.export.application.biz.impl.handler.event.BaseIndependentEventStrategy;
import com.wosai.trade.optimus.export.application.biz.impl.handler.event.context.MallOrderExportContext;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.template.ExternalInvokeWithTransactionTemplate;
import com.wosai.trade.optimus.export.common.template.InvokeProcessor;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.ExportOrderDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.enums.ExportStateEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view.*;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportInfoVO;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.email.EmailClient;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.email.model.EmailSendRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.OptimusCoreClient;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallListQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallOrdersQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallTicketsQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.OptimusOssClient;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model.OssCheckObjectExistRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model.OssDownloadRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model.OssUploadRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.taskcenter.TaskCenterClient;
import com.wosai.trade.optimus.export.infrastructure.support.excel.*;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/7/18 Time: 15:33
 */
@Slf4j
@Component
public class MallOrderExportStrategy extends BaseIndependentEventStrategy<MallOrderExportContext> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.MALL_ORDER_EXPORT;

    private static final String RESULT_EXPORT_TASK_NOT_FOUND = "导出任务不存在";
    private static final String RESULT_SEND_SUCCESS = "邮件发送成功";
    private static final String RESULT_SEND_FAILURE = "邮件发送失败";
    private static final String RESULT_GENERATE_SUCCESS = "文件生成成功";
    private static final String RESULT_GENERATE_FAILURE = "文件生成失败";

    private static final String RESULT_SEND_TASK_CENTER_SUCCESS = "发送下载中心成功";
    private static final String RESULT_SEND_TASK_CENTER_FAILURE = "邮件下载中心失败";

    private static final String MALL_LIST_NOT_EXIST = "商城列表不存在";
    @Resource
    private ExportOrderDomainRepository exportOrderDomainRepository;
    @Resource
    private OptimusCoreClient optimusCoreClient;
    @Resource
    private EmailClient emailClient;
    @Resource
    private OptimusOssClient optimusOssClient;
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Value("${spring.mail.username}")
    private String fromEmail;
    @Value("${aliyun.oss.bucket-name}")
    private String bucketName;
    @Resource
    private TaskCenterClient taskCenterClient;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<MallOrderExportContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(MallOrderExportContext context) {
        return new MallOrderExportRunnable(context);
    }

    private class MallOrderExportRunnable extends IndependentEventStrategyRunnable {
        private final MallOrderExportContext context;

        public MallOrderExportRunnable(MallOrderExportContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context, new ExternalInvokeWithTransactionTemplate<>() {
                @Override
                protected boolean preInvokeExternal(MallOrderExportContext context) {
                    ExportOrderAggrRoot exportOrderAggrRoot = exportOrderDomainRepository
                            .query(context.genMallExportOrderAggrQuery());
                    if (exportOrderAggrRoot.isExist()) {
                        context.bindMallExportOrderAggrRoot(exportOrderAggrRoot);
                        return true;
                    }
                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    aggrRoot.processSuccess(RESULT_EXPORT_TASK_NOT_FOUND);
                    return false;
                }

                @Override
                protected void invokeExternal(MallOrderExportContext context) throws Throwable {
                    ExportOrderAggrRoot exportOrderAggrRoot = context.getExportOrderAggrRoot();
                    EventAggrRoot eventAggrRoot = context.getEventAggrRoot();

                    byte[] fileBytes;
                    if (exportOrderAggrRoot.isExportHistoryOrder()
                            && optimusOssClient.isObjectExist(genOssCheckObjectExistRequest(exportOrderAggrRoot))) {
                        fileBytes = downloadHistoricalOrderFile(exportOrderAggrRoot);
                    } else {
                        // 加载商城列表
                        ExportMallListQueryResult mallListQueryResult = optimusCoreClient
                                .queryMallList(context.genExportMallListQueryRequest());
                        if (mallListQueryResult.isMallNotExist()) {
                            eventAggrRoot.processSuccess(MALL_LIST_NOT_EXIST);
                            return;
                        }
                        context.bindExportMallListQueryResult(mallListQueryResult);
                        fileBytes = generateNewOrderFile(context);
                    }

                    if (Objects.nonNull(fileBytes) && fileBytes.length > 0) {
                        if (exportOrderAggrRoot.isNeedUpload()) {
                            optimusOssClient.upload(genOssUploadRequest(exportOrderAggrRoot, fileBytes));
                        }
                        exportOrderAggrRoot.updateState(ExportStateEnum.GENERATE_SUCCESS);
                        eventAggrRoot.processStageCompleted(RESULT_GENERATE_SUCCESS);
                        // 处理订单文件
                        processOrderFile(context, fileBytes);
                        return;
                    }
                    // 订单文件不存在
                    exportOrderAggrRoot.updateState(ExportStateEnum.GENERATE_FAILURE);
                    eventAggrRoot.processFailure(RESULT_GENERATE_FAILURE);
                }

                @Override
                protected void postInvokeExternal(MallOrderExportContext mallOrderExportContext) {
                    context.startTransaction(transactionManager, transactionTemplate);
                    context.createSavepoint();
                    exportOrderDomainRepository.save(mallOrderExportContext.getExportOrderAggrRoot());
                }

                @Override
                protected void onBizFailure(MallOrderExportContext mallOrderExportContext, OptimusExportBizException e) {
                    //回滚至savepoint
                    context.rollbackToSavepoint();

                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    //告警处理
                    eventProcessSupportService.sendAlarm(aggrRoot);

                    String result = e.getCode()  + ":" + e.getMsg();
                    log.warn("[商城订单导出事件]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId()
                            + ", 错误信息: " + result + ", 异常栈: ", e);
                    aggrRoot.processFailure("业务异常: " + result);
                }

                @Override
                protected void onFailure(MallOrderExportContext mallOrderExportContext, Throwable throwable) {
                    //回滚至savepoint
                    context.rollbackToSavepoint();

                    EventAggrRoot aggrRoot = context.getEventAggrRoot();
                    //告警处理
                    eventProcessSupportService.sendAlarm(aggrRoot);

                    log.error("[商城订单导出事件]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId()
                            + ", 异常栈: ", throwable);
                    aggrRoot.processFailure("未知异常");
                }

                @Override
                protected void doFinally(MallOrderExportContext mallOrderExportContext) {
                    secureUpdateEvent(context);
                }
            });

        }

        private byte[] downloadHistoricalOrderFile(ExportOrderAggrRoot exportOrderAggrRoot) {
            OssDownloadRequest ossDownloadRequest = genOssDownloadRequest(exportOrderAggrRoot);
            byte[] fileBytes = optimusOssClient.download(ossDownloadRequest);
            exportOrderAggrRoot.updateState(ExportStateEnum.GENERATE_SUCCESS);
            return fileBytes;
        }

        private byte[] generateNewOrderFile(MallOrderExportContext context) {
            ExportOrderAggrRoot exportOrderAggrRoot = context.getExportOrderAggrRoot();
            ExportMallListQueryResult mallListQueryResult = context.getMallListQueryResult();

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            // 一次事件处理共用一个ExcelWriter
            try (ExcelWriter excelWriter = EasyExcel.write(outputStream).build()) {
                ExportMallListQueryResult.MallIterator mallIterator = mallListQueryResult.mallIterator();
                // 写入模版，业务上的
                TradeDataExcelExportTemplate tradeDataExcelExportTemplate = TradeDataExcelExportTemplate.create(excelWriter);
                context.bindTradeDataExcelExportTemplate(tradeDataExcelExportTemplate);

                // 初始化统计数据
                context.bindTradeDataSummaryView(TradeDataSummaryView.create(
                        exportOrderAggrRoot.getExportInfo().getBeginDateTime()
                        , exportOrderAggrRoot.getExportInfo().getEndDateTime()));

                ExportExcelHolder exportExcelHolder = ExportExcelHolder.create();
                ExportExcelHolder aftersaleExportExcelHolder = ExportExcelHolder.create();
                while (mallIterator.hasNext()) {
                    ExportMallListQueryResult.ExportMallHolder exportMall = mallIterator.next();

                    // 写入订单明细
                    exportExcelHolder.append(genOrderDetailsOperations(context, exportMall));

                    // 写入售后明细
                    aftersaleExportExcelHolder.append(genAftersaleDetailsOperations(context, exportMall));

                }
                tradeDataExcelExportTemplate.orderDetails(exportExcelHolder);
                tradeDataExcelExportTemplate.aftersaleDetails(aftersaleExportExcelHolder);

                // 写入首页汇总
                tradeDataExcelExportTemplate.homepageSummary(genHomepageSummaryOperations(context));
                tradeDataExcelExportTemplate.write();
            }

            return outputStream.toByteArray();
        }

        private void processOrderFile(MallOrderExportContext context, byte[] fileBytes) {
            ExportInfoVO export = context.getExportOrderAggrRoot().getExportInfo();

            if (export.isNeedSendEmail()) {
                sendEmail(context, fileBytes);
            }
            if (export.isNeedInvokeTaskCenter()) {
                invokeTaskCenter(context);
            }
        }

        private void sendEmail(MallOrderExportContext context, byte[] fileBytes) {
            ExportOrderAggrRoot exportOrderAggrRoot = context.getExportOrderAggrRoot();
            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();

            if (exportOrderAggrRoot.getExportInfo().isNeedSendEmail()) {
                EmailSendRequest emailSendRequest = genEmailSendRequest(context, fileBytes);
                boolean sendResult = emailClient.sendWithAttachment(emailSendRequest);
                if (sendResult) {
                    exportOrderAggrRoot.updateState(ExportStateEnum.SEND_SUCCESS);
                    eventAggrRoot.processStageCompleted(RESULT_SEND_SUCCESS);

                } else {
                    exportOrderAggrRoot.updateState(ExportStateEnum.SEND_FAILURE);
                    eventAggrRoot.processFailure(RESULT_SEND_FAILURE);
                }
            }
        }

        private void invokeTaskCenter(MallOrderExportContext context) {
            ExportOrderAggrRoot exportOrderAggrRoot = context.getExportOrderAggrRoot();
            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();
            if (exportOrderAggrRoot.isNeedHandleTaskCenter()) {
                boolean isInvokeSuccess = taskCenterClient.updateTaskResult(context.genTaskCenterUpdateTaskRequest());
                if (isInvokeSuccess) {
                    exportOrderAggrRoot.updateState(ExportStateEnum.SEND_SUCCESS);
                    eventAggrRoot.processStageCompleted(RESULT_SEND_TASK_CENTER_SUCCESS);
                } else {
                    exportOrderAggrRoot.updateState(ExportStateEnum.SEND_FAILURE);
                    eventAggrRoot.processFailure(RESULT_SEND_TASK_CENTER_FAILURE);
                }
            }
        }

        private ExportExcelModel genOrderDetailsOperations(MallOrderExportContext context
                , ExportMallListQueryResult.ExportMallHolder exportMall) {
            TradeDataExcelExportTemplate tradeDataExcelExportTemplate = context.getTradeDataExcelExportTemplate();


            TradeDataSummaryView tradeDataSummaryView = context.getTradeDataSummaryView();

            // 生成订单明细sheet页
            int sheetNo = tradeDataExcelExportTemplate.getAndIncrementSheetNo();

            // 当前商城的数据画布(sheet)
            ExportExcelModel exportExcel = ExportExcelModel.create(new ExcelWriterSheetBuilder()
                    .sheetNo(sheetNo)
                    .sheetName(MallOrderDetailView.genSheetName(exportMall.getMallName()
                            , exportMall.getStoreName()
                            , sheetNo))
                    .build());

            int tableNo = tradeDataExcelExportTemplate.getAndIncrementTableNo();

            boolean isNotExistHeader = true;
            ExportMallOrdersQueryResult lastOrdersResult = null;
            for (;;) {
                // 查询商城订单
                lastOrdersResult = optimusCoreClient
                        .queryMallOrders(context.genExportMallOrdersQueryRequest(exportMall, lastOrdersResult));

                // todo 处理业务逻辑

                // 订单数据查询完毕
                if (lastOrdersResult.isQueryEnd()) {
                    break;
                }

                // 判断是否到达统计汇总上限
                if (tradeDataSummaryView.isReachedSummaryLimit()) {
                    break;
                }
            }

            // 明细汇总
            exportExcel.append(ExportWriteData.create()
                    .writeRow(tradeDataSummaryView.genMallOrderDetailSummary(exportMall.getMallSn()))
                    .writeRow(List.of()));

            // 商品汇总
            MallOrderDetailProductSummaryView mallOrderDetailProductSummary = tradeDataSummaryView
                    .genMallOrderDetailProductSummary(exportMall.getMallSn());
            if (mallOrderDetailProductSummary.isNotEmpty()) {
                exportExcel.append(ExportWriteTable.create(new ExcelWriterTableBuilder()
                                .tableNo(tradeDataExcelExportTemplate.getAndIncrementTableNo())
                                .head(MallOrderDetailProductSummaryView.getHeaders())
                                .build())
                        .writeRows(mallOrderDetailProductSummary
                                .getContents()));
            }

            return exportExcel;
        }

        private ExcelOperations genAftersaleDetailsOperations(MallOrderExportContext context
                , ExportMallListQueryResult.ExportMallHolder exportMall) {
            TradeDataExcelExportTemplate tradeDataExcelExportTemplate = context.getTradeDataExcelExportTemplate();


            TradeDataSummaryView tradeDataSummaryView = context.getTradeDataSummaryView();

            //生成售后单明细sheet页
            int sheetNo = tradeDataExcelExportTemplate.getAndIncrementSheetNo();

            // 当前商城的数据画布(sheet)
            ExportExcelModel exportExcel = ExportExcelModel.create(new ExcelWriterSheetBuilder()
                    .sheetNo(sheetNo)
                    .sheetName(MallTicketDetailView.genSheetName(exportMall.getMallName()
                            , sheetNo))
                    .build());

            int tableNo = tradeDataExcelExportTemplate.getAndIncrementTableNo();

            boolean isNotExistHeader = true;
            ExportMallTicketsQueryResult lastTicketsResult = null;
            for (;;) {
                // 查询商城售后单
                lastTicketsResult = optimusCoreClient
                        .queryMallTickets(context.genExportMallTicketsQueryRequest(exportMall, lastTicketsResult));

                MallTicketDetailView mallTicketDetailView = lastTicketsResult.genTicketFileMallDetailView();

                tradeDataSummaryView.calculateTicketSummary(mallTicketDetailView);

                if (isNotExistHeader) { // 首次写入表头
                    isNotExistHeader = false;
                    exportExcel.append(ExportWriteTable.create(new ExcelWriterTableBuilder()
                                    .tableNo(tableNo)
                                    .head(mallTicketDetailView.genHeaderValues())
                                    .build())
                            .writeRows(mallTicketDetailView.genContentValues()));
                } else {
                    exportExcel.append(ExportWriteData.create().writeRows(mallTicketDetailView.genContentValues()));
                }

                // 售后单数据查询完毕
                if (lastTicketsResult.isQueryEnd()) {
                    break;
                }

                // 判断是否到达统计汇总上限
                if (tradeDataSummaryView.isReachedSummaryLimit()) {
                    break;
                }
            }

            //  明细汇总
            exportExcel.append(ExportWriteData.create()
                    .writeRow(tradeDataSummaryView.genMallTicketDetailSummary(exportMall.getMallSn()))
                    .writeRow(List.of()));

            // 商品汇总
            MallAftersaleDetailProductSummaryView mallAftersaleDetailProductSummary = tradeDataSummaryView
                    .genMallAftersaleDetailProductSummary(exportMall.getMallSn());
            if (mallAftersaleDetailProductSummary.isNotEmpty()) {
                exportExcel.append(ExportWriteTable.create(new ExcelWriterTableBuilder()
                                .tableNo(tradeDataExcelExportTemplate.getAndIncrementTableNo())
                                .head(MallAftersaleDetailProductSummaryView.getHeaders())
                                .build())
                        .writeRows(mallAftersaleDetailProductSummary
                                .getContents()));
            }

            return exportExcel;
        }

        private ExcelOperations genHomepageSummaryOperations(MallOrderExportContext context) {
            TradeDataExcelExportTemplate tradeDataExcelExportTemplate = context.getTradeDataExcelExportTemplate();
            TradeDataSummaryView tradeDataSummaryView = context.getTradeDataSummaryView();
            tradeDataSummaryView.statisticsSummary();
            tradeDataSummaryView.statisticsAftersaleSummary();

            // 生成首页汇总sheet页
            ExcelWriterSheetBuilder sheetBuilder = new ExcelWriterSheetBuilder()
                    .sheetNo(tradeDataExcelExportTemplate.getAndIncrementSheetNo())
                    .sheetName(TradeDataSummaryView.getSheetName());
            tradeDataSummaryView.getHomepageWriteHandler().forEach(sheetBuilder::registerWriteHandler);

            ExportExcelModel homepageSummary = ExportExcelModel.create(sheetBuilder.build());

            // 汇总页页头
            ExportWriteUnit writeData = ExportWriteData.create()
                    .writeRows(tradeDataSummaryView.genSummaryHeaderRow());
            homepageSummary.append(writeData);

            if (tradeDataSummaryView.isExistMallOrderVolumeSummary()) {
                // 商城订单交易数据汇总
                ExportWriteTable mallOrderTradeDataSummaryTable = ExportWriteTable.create(new ExcelWriterTableBuilder()
                        .head(MallOrderVolumeSummaryView.getHeaders())
                        .tableNo(tradeDataExcelExportTemplate.getAndIncrementTableNo())
                        .build());
                mallOrderTradeDataSummaryTable.writeRows(tradeDataSummaryView.getMallOrderVolumeSummaryView().getContents());
                // 空一行
                mallOrderTradeDataSummaryTable.writeRow(List.of());
                homepageSummary.append(mallOrderTradeDataSummaryTable);
            }

            if (tradeDataSummaryView.isExistMallAftersaleVolumeSummary()) {
                // 商城售后交易数据汇总
                ExportWriteTable mallAftersaleTradeDataSummaryTable = ExportWriteTable.create(new ExcelWriterTableBuilder()
                        .head(MallAftersaleVolumeSummaryView.getHeaders())
                        .tableNo(tradeDataExcelExportTemplate.getAndIncrementTableNo())
                        .build());
                mallAftersaleTradeDataSummaryTable.writeRows(tradeDataSummaryView.getMallAftersaleVolumeSummaryView().getContents());
                // 空一行
                mallAftersaleTradeDataSummaryTable.writeRow(List.of());
                homepageSummary.append(mallAftersaleTradeDataSummaryTable);
            }

            if (tradeDataSummaryView.isExistMallOrderProductSummary()) {
                // 商城订单商品汇总
                ExportWriteTable mallOrderProductSummaryTable = ExportWriteTable.create(new ExcelWriterTableBuilder()
                        .head(MallOrderProductSummaryView.getHeaders())
                        .tableNo(tradeDataExcelExportTemplate.getAndIncrementTableNo())
                        .build());
                mallOrderProductSummaryTable.writeRows(tradeDataSummaryView.getMallOrderProductSummaryView().getContents());
                // 空一行
                mallOrderProductSummaryTable.writeRow(List.of());
                homepageSummary.append(mallOrderProductSummaryTable);
            }

            if (tradeDataSummaryView.isExistMallAftersaleProductSummary()) {
                // 商城售后商品汇总
                ExportWriteTable mallAftersaleProductSummaryTable = ExportWriteTable.create(new ExcelWriterTableBuilder()
                        .head(MallAftersaleProductSummaryView.getHeaders())
                        .tableNo(tradeDataExcelExportTemplate.getAndIncrementTableNo())
                        .build());
                mallAftersaleProductSummaryTable.writeRows(tradeDataSummaryView.getMallAftersaleProductSummaryView().getContents());
                // 空一行
                mallAftersaleProductSummaryTable.writeRow(List.of());
                homepageSummary.append(mallAftersaleProductSummaryTable);
            }

            if (tradeDataSummaryView.isExistTradeOrderProductSummary()) {
                // 全部订单商品汇总
                ExportWriteTable fullyOrderProductSummaryTable = ExportWriteTable.create(new ExcelWriterTableBuilder()
                        .head(TradeOrderProductSummaryView.getHeaders())
                        .tableNo(tradeDataExcelExportTemplate.getAndIncrementTableNo())
                        .build());
                fullyOrderProductSummaryTable.writeRows(tradeDataSummaryView.getTradeOrderProductSummaryView().getContents());
                // 空一行
                fullyOrderProductSummaryTable.writeRow(List.of());
                homepageSummary.append(fullyOrderProductSummaryTable);
            }

            if (tradeDataSummaryView.isExistTradeAftersaleProductSummary()) {
                // 全部售后商品汇总
                ExportWriteTable fullyAftersaleProductSummaryTable = ExportWriteTable.create(new ExcelWriterTableBuilder()
                        .head(TradeAftersaleProductSummaryView.getHeaders())
                        .tableNo(tradeDataExcelExportTemplate.getAndIncrementTableNo())
                        .build());
                fullyAftersaleProductSummaryTable.writeRows(tradeDataSummaryView.getTradeAftersaleProductSummaryView().getContents());
                // 空一行
                fullyAftersaleProductSummaryTable.writeRow(List.of());
                homepageSummary.append(fullyAftersaleProductSummaryTable);
            }

            return homepageSummary;
        }

    }

    private EmailSendRequest genEmailSendRequest(MallOrderExportContext context, byte[] fileBytes) {
        ExportOrderAggrRoot exportOrderAggrRoot = context.getExportOrderAggrRoot();
        return EmailSendRequest.builder()
                .from(fromEmail)
                .to(exportOrderAggrRoot.getExportInfo().getReceiveEmail())
                .subject(exportOrderAggrRoot.genExportTaskName())
                .text(exportOrderAggrRoot.genExportOrderDescription())
                .attachmentFileName(exportOrderAggrRoot.genAttachmentFileName())
                .attachmentContent(fileBytes)
                .attachmentType(exportOrderAggrRoot.getExportContentType())
                .build();
    }

    private OssCheckObjectExistRequest genOssCheckObjectExistRequest(ExportOrderAggrRoot exportOrderAggrRoot) {
        return OssCheckObjectExistRequest.builder()
                .bucketName(bucketName)
                .path(exportOrderAggrRoot.genFilePath())
                .build();
    }

    private OssDownloadRequest genOssDownloadRequest(ExportOrderAggrRoot exportOrderAggrRoot) {
        return OssDownloadRequest.builder()
                .bucketName(bucketName)
                .path(exportOrderAggrRoot.genFilePath())
                .build();
    }

    public OssUploadRequest genOssUploadRequest(ExportOrderAggrRoot exportOrderAggrRoot, byte[] fileBytes ) {
        return OssUploadRequest
                .builder()
                .bucketName(bucketName)
                .path(exportOrderAggrRoot.genFilePath())
                .content(fileBytes)
                .fileName(exportOrderAggrRoot.genAttachmentFileName())
                .build();
    }

    @Builder
    public static final class TradeDataExcelExportTemplate {
        private ExcelWriter writer;
        private ExcelOperations homepageSummary;
        private ExcelOperations orderDetails;
        private ExcelOperations aftersaleDetails;
        private int sheetNo;
        private int tableNo;


        public static TradeDataExcelExportTemplate create() {
            return TradeDataExcelExportTemplate.builder().build();
        }


        public static TradeDataExcelExportTemplate create(ExcelWriter writer) {
            if (Objects.isNull(writer)) {
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.BIZ_PARAMS_MISSING);
            }
            return TradeDataExcelExportTemplate.builder()
                    .writer(writer)
                    .build();
        }

        public int getAndIncrementSheetNo() {
            return sheetNo++;
        }

        public int getAndIncrementTableNo() {
            return tableNo++;
        }


        public TradeDataExcelExportTemplate withWriter(ExcelWriter writer) {
            if (Objects.isNull(this.writer)) {
                this.writer = writer;
                return self();
            }
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.UNSUPPORTED_OPERATION_EXCEPTION);
        }

        public TradeDataExcelExportTemplate homepageSummary(ExcelOperations homepageSummary) {
            if (Objects.isNull(this.homepageSummary)) {
                this.homepageSummary = homepageSummary;
                return self();
            }
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.UNSUPPORTED_OPERATION_EXCEPTION);
        }

        public TradeDataExcelExportTemplate orderDetails(ExcelOperations orderDetails) {
            if (Objects.isNull(this.orderDetails)) {
                this.orderDetails = orderDetails;
                return self();
            }
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.UNSUPPORTED_OPERATION_EXCEPTION);
        }

        public TradeDataExcelExportTemplate aftersaleDetails(ExcelOperations aftersaleDetails) {
            if (Objects.isNull(this.aftersaleDetails)) {
                this.aftersaleDetails = aftersaleDetails;
                return self();
            }
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.UNSUPPORTED_OPERATION_EXCEPTION);
        }

        public void write() {
            if (Objects.isNull(writer)) {
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.BIZ_OBJECT_MISSING);
            }
            if (Objects.nonNull(homepageSummary)) {
                homepageSummary.writeWithClear(writer);
            }
            if (Objects.nonNull(orderDetails)) {
                orderDetails.writeWithClear(writer);
            }
            if (Objects.nonNull(aftersaleDetails)) {
                aftersaleDetails.write(writer);
            }
        }

        private TradeDataExcelExportTemplate self() {
            return this;
        }
    }
}
