package com.wosai.trade.optimus.export.application.adapter.rest;

import com.wosai.middleware.hera.toolkit.metrics.MetricsManager;
import com.wosai.trade.optimus.export.application.aop.annotation.OptimusExportEntry;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Date: 2022/12/26 Time: 4:33 PM
 */
@RestController
public class MetaController {

    @GetMapping("/check")
    @OptimusExportEntry(skipLogging = true)
    public ResponseEntity<String> healthy() {
        return  ResponseEntity.ok("success");
    }

    @GetMapping(path = "/metrics", produces = {"text/plain;version=0.0.4;charset=utf-8"})
    @OptimusExportEntry(skipLoggingParams = true)
    public String endpoint() {
        return MetricsManager.scrape();
    }

}
