<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.wosai.trade</groupId>
    <artifactId>optimus-export</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>optimus-export-api</module>
        <module>optimus-export-application</module>
        <module>optimus-export-domain</module>
        <module>optimus-export-infrastructure</module>
        <module>optimus-export-common</module>
    </modules>
    <version>1.0.12</version>

    <parent>
        <groupId>com.shouqianba.middleware</groupId>
        <artifactId>spring-boot-starter-parent-shouqi<PERSON>ba</artifactId>
        <version>2.7.18-20240321</version>
        <relativePath/>
    </parent>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <wosai-general.version>2.0.2</wosai-general.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--一方库-->
            <dependency>
                <groupId>com.wosai.trade.optimus</groupId>
                <artifactId>optimus-export-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.trade.optimus</groupId>
                <artifactId>optimus-export-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.trade.optimus</groupId>
                <artifactId>optimus-export-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.trade.optimus</groupId>
                <artifactId>optimus-export-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!--二方库-->
            <dependency>
                <groupId>com.wosai.general</groupId>
                <artifactId>wosai-general</artifactId>
                <version>${wosai-general.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${shouqianba.apollo.verison}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>snakeyaml</artifactId>
                        <groupId>org.yaml</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.wosai.upay</groupId>
                <artifactId>core-business-api</artifactId>
                <version>3.6.59</version>
                <exclusions>
                    <exclusion>
                        <artifactId>avro</artifactId>
                        <groupId>org.apache.avro</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.wosai.app</groupId>
                <artifactId>merchant-user-api</artifactId>
                <version>1.9.28</version>
                <exclusions>
                    <exclusion>
                        <artifactId>wosai-common</artifactId>
                        <groupId>com.wosai.common</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-common-web-api</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>upay-common</artifactId>
                        <groupId>com.wosai.upay</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-common-validation</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.wosai.pantheon</groupId>
                <artifactId>wosai-common-web-api</artifactId>
                <version>2.0.2</version>
            </dependency>

            <dependency>
                <groupId>com.wosai</groupId>
                <artifactId>aop-gateway-api</artifactId>
                <version>1.8.7</version>
                <exclusions>
                    <exclusion>
                        <artifactId>wosai-common-validation</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-common-web-api</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>upay-common</artifactId>
                        <groupId>com.wosai.upay</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-common-validation</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-util</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-common-logging</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>com.wosai.common</groupId>
                <artifactId>wosai-common</artifactId>
                <version>1.7.6</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-data-redis</artifactId>
                        <groupId>org.springframework.data</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>validation-api</artifactId>
                        <groupId>javax.validation</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jsonrpc4j</artifactId>
                        <groupId>com.wosai.middleware</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dom4j</artifactId>
                        <groupId>dom4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>cglib</artifactId>
                        <groupId>cglib</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>apollo-client</artifactId>
                        <groupId>com.wosai.middleware</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>apacheds-core</artifactId>
                        <groupId>org.apache.directory.server</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hibernate-validator</artifactId>
                        <groupId>org.hibernate</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 内容安全  -->
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>aliyun-sdk-openapi</artifactId>
                <version>0.12.0</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.trade.optimus</groupId>
                <artifactId>optimus-api</artifactId>
                <version>1.2.16</version>
            </dependency>
            <!-- 下载中心 -->
            <dependency>
                <groupId>com.wosai.upay</groupId>
                <artifactId>upay-task-center-api</artifactId>
                <version>1.0.6</version>
            </dependency>
            <!--三方库-->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.ws</groupId>
                <artifactId>jaxws-ri</artifactId>
                <version>4.0.0</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.3.2</version>
            </dependency>
            <!-- 生成二维码-->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>3.4.1</version>
            </dependency>
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>0.10.2</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.13.0</version>
            </dependency>
            <!--kafka-avro-serializer-->
            <dependency>
                <groupId>io.confluent</groupId>
                <artifactId>kafka-avro-serializer</artifactId>
                <version>6.0.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>swagger-annotations</artifactId>
                        <groupId>io.swagger</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>

</project>