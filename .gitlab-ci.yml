# 引用git.wosai-inc.com/do/gitlab-ci这个项目中的maven.yml作为CI模板
include:
  - project: "do/gitlab-ci"
    file: "/maven.yml"
  - project: "do/gitlab-ci"
    file: "/autodeploy.yml"

variables:
  JDK_VERSION: 17


maven-build:
  variables:
    MAVEN_CLI_OPTS: "-Dmaven.test.skip=true"
    JFROG_MVNC_OPTS: "--exclude-patterns=*"

maven-build-tags:
  variables:
    JFROG_MVNC_OPTS: "--exclude-patterns=*optimus-export-application*.jar,*optimus-export-common*.jar,*optimus-export-domain*.jar,*optimus-export-infrastructure*.jar"
