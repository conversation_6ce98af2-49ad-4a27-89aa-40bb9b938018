package com.wosai.trade.optimus.export.common.util;

import com.google.common.hash.Hashing;
import lombok.SneakyThrows;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR> Date: 2023/1/6 Time: 3:49 PM
 */
public class SecurityUtils {
    private static final byte[] HMAC_KEY = "yFoYInaOIPsQ817t".getBytes(StandardCharsets.UTF_8);
    private static final char[] HASH_BASE_CHAR
            = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-".toCharArray();
    private static final String ALGORITHM_AES = "AES";

    public static String hmacSHA1To64Decimal(String text) {
        StringBuilder hashTemp = new StringBuilder(20);
        byte[] hashBytes = Hashing.hmacSha1(HMAC_KEY).hashString(text, StandardCharsets.UTF_8).asBytes();
        for (byte b : hashBytes) {
            int pos = ((b >> 4) ^ b) & 0x3f;
            hashTemp.append(HASH_BASE_CHAR[pos]);
        }
        return hashTemp.toString();
    }



    @SneakyThrows
    public static String encryptWithAES(String plaintext, String key) {
        Cipher cipher = Cipher.getInstance(ALGORITHM_AES);
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), ALGORITHM_AES);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        byte[] encrypted = cipher.doFinal(plaintext.getBytes());
        return Base64.getEncoder().encodeToString(encrypted);
    }

    @SneakyThrows
    public static String decryptWithAES(String ciphertext, String key) {
        Cipher cipher = Cipher.getInstance(ALGORITHM_AES);
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), ALGORITHM_AES);
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decode = Base64.getDecoder().decode(ciphertext);
        byte[] decrypted = cipher.doFinal(decode);
        return new String(decrypted);
    }

}
