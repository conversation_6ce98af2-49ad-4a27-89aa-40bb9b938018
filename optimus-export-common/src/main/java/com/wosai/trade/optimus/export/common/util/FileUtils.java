package com.wosai.trade.optimus.export.common.util;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2024/7/24 Time: 14:16
 */
public class FileUtils {

    private static final String HTTPS = "https://";
    private static final String HTTP = "http://";

    /**
     * 处理url http替换https
     *
     * @param url
     * @return
     */
    public static String genHttpsUrl(String url) {
        if (Objects.isNull(url)) {
            return null;
        }
        if (url.contains(HTTP) && !url.contains(HTTPS)) {
            return url.replace(HTTP, HTTPS);
        }
        return url;
    }

    /**
     * 替换Excel工作表名称中的特殊字符为下划线，并确保名称不超过31个字符。
     * 特殊字符包括：/（正斜杠）、\（反斜杠）、*（星号）、[（左方括号）、]（右方括号）、?（问号）、:（冒号）、"（双引号）
     *
     * @param sheetName 原工作表名称
     * @return 替换和截断后的工作表名称
     */
    public static String sanitizeSheetName(String sheetName) {
        if (sheetName == null || sheetName.isEmpty()) {
            return sheetName;
        }

        // 替换特殊字符为下划线
        return sheetName.replaceAll("[/\\\\*\\[\\]?\":]", "");

    }

}
