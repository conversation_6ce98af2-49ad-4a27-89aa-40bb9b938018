package com.wosai.trade.optimus.export.common.template;

import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;

/**
 * <AUTHOR> Date: 2023/1/16 Time: 2:52 PM
 */
public abstract class WithoutResultTemplate<T> {

    protected final void doBefore(T t) {
        before(t);
    }

    protected final void doAfter(T t) {
        after(t);
    }

    protected abstract void invoke(T t) throws Throwable;

    protected void before(T t) {}

    protected void after(T t) {}

    protected void onSuccess(T t) {}

    protected void onBizFailure(T t, OptimusExportBizException e) {
        throw e;
    }

    protected void onFailure(T t, Throwable throwable) throws Throwable {
        throw throwable;
    }

}
