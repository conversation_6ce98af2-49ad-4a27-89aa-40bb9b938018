package com.wosai.trade.optimus.export.common.util;

/**
 * <AUTHOR>
 * @date 2023/7/30
 */
public class UrlParamUtils {

    public static String getUrlParam(String url, String paramKey) {
        String queryStr = url.substring(url.lastIndexOf("?") + 1);
        String[] keyValuePairs = queryStr.split("&");
        for (String pair : keyValuePairs) {
            String[] keyValue = pair.split("=");
            String key = keyValue[0];
            String value = keyValue[1];
            if (paramKey.equals(key)) {
                return value;
            }
        }
        return null;
    }
}
