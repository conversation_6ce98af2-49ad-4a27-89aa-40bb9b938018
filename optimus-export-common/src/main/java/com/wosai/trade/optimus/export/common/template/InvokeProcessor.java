package com.wosai.trade.optimus.export.common.template;


import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import lombok.SneakyThrows;

/**
 * <AUTHOR> Date: 2022/12/9 Time: 3:45 PM
 */
public class InvokeProcessor {

    @SneakyThrows
    public static <T, R> R process(T t, InvokeTemplate<T, R> template) {
        R r = null;
        try {
            template.doBefore(t);
            r = template.invoke(t);
            template.onSuccess(t, r);
        } catch (OptimusExportBizException e) {
            throw e;
        } catch (Throwable throwable) {
            return template.onFailure(t, throwable);
        } finally {
            template.doAfter(t, r);
        }
        return r;
    }

    public static <T, R> R process(T t, EventProcessTemplate<T, R> template) {
        R r = null;
        try {
            template.doBefore(t);
            r = template.invoke(t);
            template.onSuccess(t, r);
        } catch (OptimusExportBizException e) {
            template.onBizFailure(t, e);
        } catch (Throwable throwable) {
            return template.onFailure(t, throwable);
        } finally {
            template.doAfter(t, r);
        }
        return r;
    }

    @SneakyThrows
    public static <T> void processWithoutResult(T t, WithoutResultTemplate<T> template) {
        try {
            template.doBefore(t);
            template.invoke(t);
            template.onSuccess(t);
        } catch (OptimusExportBizException e) {
            template.onBizFailure(t, e);
        } catch (Throwable throwable) {
            template.onFailure(t, throwable);
        } finally {
            template.doAfter(t);
        }
    }

    @SneakyThrows
    public static <R> R processWithoutRequest(WithoutRequestTemplate<R> template) {
        R r = null;
        try {
            template.doBefore();
            r = template.invoke();
            template.onSuccess(r);
        } catch (OptimusExportBizException e) {
            throw e;
        } catch (Throwable throwable) {
            return template.onFailure(throwable);
        } finally {
            template.doAfter(r);
        }
        return r;
    }

    public static <T> void processExternalInvokeWithTransaction(T t, ExternalInvokeWithTransactionTemplate<T> template) {
        try {
            if (template.preInvokeExternal(t)) {
                template.invokeExternal(t);
                template.postInvokeExternal(t);
            }
        } catch (OptimusExportBizException e) {
            template.onBizFailure(t, e);
        } catch (Throwable throwable) {
            template.onFailure(t, throwable);
        } finally {
            template.doFinally(t);
        }
    }

}
