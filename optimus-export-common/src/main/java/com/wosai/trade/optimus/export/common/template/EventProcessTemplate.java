package com.wosai.trade.optimus.export.common.template;

import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;

/**
 * <AUTHOR> Date: 2023/1/3 Time: 12:03 PM
 */
public abstract class EventProcessTemplate<T, R>  {

    protected final void doBefore(T t) {
        before(t);
    }

    protected final void doAfter(T t, R r) {
        after(t, r);
    }

    protected abstract R invoke(T t) throws Throwable;

    protected void before(T t) {}

    protected void after(T t, R r) {}

    protected void onSuccess(T t, R r) {}

    protected abstract R onBizFailure(T t, OptimusExportBizException e);

    protected abstract R onFailure(T t, Throwable throwable);
}
