package com.wosai.trade.optimus.export.common.support;

import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR> Date: 2023/2/13 Time: 4:57 PM
 */
public interface SerialGenerator {

    char[] RANDOM_CHAR_BASE = "abcdefghijklmnopqrstuvwxyz0123456789".toCharArray();

    static String genRandomText(int length) {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        StringBuilder temp = new StringBuilder();
        for (int i = 0; i < length; i++) {
            temp.append(RANDOM_CHAR_BASE[random.nextInt(RANDOM_CHAR_BASE.length)]);
        }
        return temp.toString();
    }


    long genEventId();

    String genCommonSn();

}
