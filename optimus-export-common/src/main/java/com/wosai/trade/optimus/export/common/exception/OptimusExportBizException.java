package com.wosai.trade.optimus.export.common.exception;


import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.exception.enums.RespCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> Date: 2023/5/23 Time: 2:30 PM
 */
@Getter
@Setter
public class OptimusExportBizException extends RuntimeException {
    private String code;
    private String msg;

    public OptimusExportBizException(String code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public OptimusExportBizException(Throwable throwable) {
        super(throwable);
        this.code = OptimusExportRespCodeEnum.SERVER_ERROR.getCode();
        this.msg = throwable.getMessage();
    }

    public OptimusExportBizException(RespCode respCode) {
        super(respCode.getMsg());
        this.code = respCode.getCode();
        this.msg = respCode.getMsg();
    }

    public OptimusExportBizException(RespCode respCode, String msg) {
        super(msg);
        this.code = respCode.getCode();
        this.msg = msg;
    }
}
