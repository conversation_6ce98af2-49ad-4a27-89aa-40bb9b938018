package com.wosai.trade.optimus.export.common.template;


/**
 * <AUTHOR> Date: 2022/12/9 Time: 3:25 PM
 */
public abstract class WithoutRequestTemplate<R> {

    protected final void doBefore() {
        before();
    }

    protected final void doAfter(R r) {
        after(r);
    }

    protected abstract R invoke() throws Throwable;

    protected void before() {}

    protected void after(R r) {}

    protected void onSuccess(R r) {}

    protected R onFailure(Throwable throwable) throws Throwable {
        return null;
    }

}
