package com.wosai.trade.optimus.export.common.exception.enums;

import lombok.Getter;

/**
 * <AUTHOR> Date: 2023/6/12 Time: 3:24 PM
 */
@Getter
public enum OptimusExportRespCodeEnum implements RespCode {
    PROCESS_SUCCESS("0000", "处理成功"),
    ILLEGAL_ARGUMENT("4000", "入参错误"),

    MALL_NOT_EXIST("5001", "商城不存在"),
    MALL_EXPORT_ORDER_NOT_EXIST("5110", "商城导出记录不存在"),
    CHECK_MERCHANT_ERROR("5217", "请选择商户"),
    MALL_NO_PERMISSION_ERROR("5217", "该商城无权操作"),
    STORE_NOT_EXIST("5221", "门店不存在"),
    ORDER_NO_PERMISSION_ERROR("5218", "该订单无权操作"),
    EVENT_NOT_EXIST("5801", "事件不存在"),
    EVENT_STRATEGY_NOT_EXIST("5802", "事件处理策略不存在"),
    GROUP_MERCHANT_NOT_EXIST("5801", "集团商户不存在"),


    EXTERNAL_SERVICE_ERROR("6000", "外部服务错误"),
    EXTERNAL_SERVICE_RESULT_NOT_EXIST("6001", "外部服务结果不存在"),
    MERCHANT_INFO_NOT_FOUND("6002", "商户信息不存在"),
    STORE_INFO_NOT_FOUND("6003", "门店信息不存在"),
    TERMINAL_INFO_NOT_FOUND("6004", "终端信息不存在"),
    TERMINAL_CREATE_FAILURE("6005", "终端创建失败"),
    ORDER_NOT_EXIST("6048", "订单不存在"),
    OSS_CONTENT_NOT_FOUND("6060", "文件不存在"),

    EXTERNAL_SERVICE_INVOKE_FAILURE("6999", "外部服务调用失败"),


    UNSUPPORTED_OPERATION_EXCEPTION("9001", "不支持的操作"),
    APOLLO_CONFIG_NOT_EXIST("9002", "Apollo配置文件不存在"),
    MISSING_PRE_OPERATION("9003", "缺少前置操作"),
    PERMISSION_INSUFFICIENT("9004", "权限不足"),
    CONCURRENT_MODIFY_ERROR("9005", "并发修改异常"),
    CONFIG_NOT_EXIST("9006", "配置不存在"),
    SEQUENCE_GENERATOR_NOT_FOUND("9020", "序列生成器不存在"),
    MISSING_THREAD_POOL("9022", "线程池缺失"),
    BIZ_PARAMS_MISSING("9024", "业务参数缺失"),
    BIZ_OBJECT_MISSING("9026", "业务对象缺失"),
    SERVER_ERROR("9999", "系统错误"),
    ;
    private final String code;
    private final String msg;

    OptimusExportRespCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }


}
