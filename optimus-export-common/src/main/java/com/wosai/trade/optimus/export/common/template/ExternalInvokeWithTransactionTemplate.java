package com.wosai.trade.optimus.export.common.template;

import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;

/**
 * <AUTHOR> Date: 2022/12/9 Time: 3:40 PM
 */
public abstract class ExternalInvokeWithTransactionTemplate<T> {

    protected abstract boolean preInvokeExternal(T t);

    protected abstract void invokeExternal(T t) throws Throwable;

    protected abstract void postInvokeExternal(T t);

    protected abstract void onBizFailure(T t, OptimusExportBizException e);

    protected abstract void onFailure(T t, Throwable throwable);

    protected abstract void doFinally(T t);

}
