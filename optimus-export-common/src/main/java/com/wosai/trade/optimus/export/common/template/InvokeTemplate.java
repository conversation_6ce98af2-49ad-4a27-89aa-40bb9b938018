package com.wosai.trade.optimus.export.common.template;



/**
 * <AUTHOR> Date: 2022/12/9 Time: 3:25 PM
 */
public abstract class InvokeTemplate<T, R> {

    protected final void doBefore(T t) {
        before(t);
    }

    protected final void doAfter(T t, R r) {
        after(t, r);
    }

    protected abstract R invoke(T t) throws Throwable;

    protected void before(T t) {}

    protected void after(T t, R r) {}

    protected void onSuccess(T t, R r) {}

    protected R onFailure(T t, Throwable throwable) throws Throwable {
        throw throwable;
    }

}
