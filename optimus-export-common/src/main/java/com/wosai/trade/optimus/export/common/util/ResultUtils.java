package com.wosai.trade.optimus.export.common.util;


import com.wosai.general.result.*;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;

import java.util.List;

/**
 * <AUTHOR> Date: 2022/12/8 Time: 3:22 PM
 */
public class ResultUtils {

    public static <T> SingleResult<T> buildSuccessfulSingleResult(T t) {
        SingleResult<T> result =  new SingleResult<>();
        result.setSuccess(true);
        result.setCode(OptimusExportRespCodeEnum.PROCESS_SUCCESS.getCode());
        result.setMsg(OptimusExportRespCodeEnum.PROCESS_SUCCESS.getMsg());
        result.setData(t);
        return result;
    }

    public static <T> MultiResult<T> buildSuccessfulMultiResult(List<T> data) {
        MultiResult<T> result = new MultiResult<>();
        result.setSuccess(true);
        result.setCode(OptimusExportRespCodeEnum.PROCESS_SUCCESS.getCode());
        result.setMsg(OptimusExportRespCodeEnum.PROCESS_SUCCESS.getMsg());
        result.setData(data);
        return result;
    }

    public static <T> PagingResult<T> buildSuccessfulPagingResult(List<T> data, Long total) {
        PagingResult<T> result = new PagingResult<>();
        result.setSuccess(true);
        result.setCode(OptimusExportRespCodeEnum.PROCESS_SUCCESS.getCode());
        result.setMsg(OptimusExportRespCodeEnum.PROCESS_SUCCESS.getMsg());
        result.setData(data);
        result.setTotal(total);
        return result;
    }

    public static VoidResult buildSuccessfulVoidResult() {
        VoidResult result = new VoidResult();
        result.setSuccess(true);
        result.setCode(OptimusExportRespCodeEnum.PROCESS_SUCCESS.getCode());
        result.setMsg(OptimusExportRespCodeEnum.PROCESS_SUCCESS.getMsg());
        return result;
    }

    public static <T> WodaPagingResult<T> buildSuccessfulWodaPagingResult(List<T> data, Long total) {
        WodaPagingResult<T> result = new WodaPagingResult<>();
        result.setSuccess(true);
        result.setCode(OptimusExportRespCodeEnum.PROCESS_SUCCESS.getCode());
        result.setMsg(OptimusExportRespCodeEnum.PROCESS_SUCCESS.getMsg());
        result.setRecords(data);
        result.setTotal(total);
        return result;
    }
}
