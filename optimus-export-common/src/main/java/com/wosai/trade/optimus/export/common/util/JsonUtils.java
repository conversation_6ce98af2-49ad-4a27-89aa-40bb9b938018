package com.wosai.trade.optimus.export.common.util;

import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.jsontype.NamedType;
import com.google.common.collect.Lists;
import org.reflections.Reflections;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * <AUTHOR> Date: 2022/12/1 Time: 6:34 PM
 */
public class JsonUtils {

    private static final ObjectMapper objectMapper;
    private static final String MODULE_PACKAGE = "com.wosai.trade.optimus";

    static {
        Reflections reflections = new Reflections(MODULE_PACKAGE);
        Set<Class<?>> jsonTypeNameClassSet = reflections.getTypesAnnotatedWith(JsonTypeName.class);
        List<NamedType> jsonSubTypes = Lists.newArrayListWithCapacity(jsonTypeNameClassSet.size());
        for (Class<?> jsonTypeNameClass : jsonTypeNameClassSet) {
            String jsonTypeNameValue = jsonTypeNameClass.getDeclaredAnnotation(JsonTypeName.class).value();
            jsonSubTypes.add(new NamedType(jsonTypeNameClass, jsonTypeNameValue));
        }

        objectMapper = JsonMapper.builder()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, false)
                .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
                .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                .registerSubtypes(jsonSubTypes.toArray(NamedType[]::new))
                .findAndAddModules()
                .build();
    }

    public static String toJsonString(Object value) {
        try {
            return objectMapper.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static String toJsonStringIgnoreException(Object value) {
        try {
            return toJsonString(value);
        } catch (Throwable ignored) {
        }
        return null;
    }

    public static byte[] toJsonBytes(Object value) {
        try {
            return objectMapper.writeValueAsBytes(value);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> String toJsonArray(Object value, Class<T> tClass) {
        JavaType type = objectMapper.getTypeFactory().constructParametricType(List.class, tClass);
        try {
            return objectMapper.writerFor(type).writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseObject(String jsonString, Class<T> tClass) {
        try {
            return objectMapper.readValue(jsonString, tClass);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseObject(String jsonString, TypeReference<T> tClass) {
        try {
            return objectMapper.readValue(jsonString, tClass);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> parseArray(String jsonArray, Class<T> tClass) {
        JavaType type = objectMapper.getTypeFactory().constructParametricType(List.class, tClass);
        try {
            return objectMapper.readValue(jsonArray, type);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> parseArray(JsonNode jsonNode, Class<T> tClass) {
        JavaType type = objectMapper.getTypeFactory().constructParametricType(List.class, tClass);
        return objectMapper.convertValue(jsonNode, type);
    }

    public static <T> Set<T> parseSet(String jsonArray, Class<T> tClass) {
        JavaType type = objectMapper.getTypeFactory().constructParametricType(Set.class, tClass);
        try {
            return objectMapper.readValue(jsonArray, type);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> Set<T> parseSet(JsonNode jsonNode, Class<T> tClass) {
        JavaType type = objectMapper.getTypeFactory().constructParametricType(Set.class, tClass);
        return objectMapper.convertValue(jsonNode, type);
    }

    public static <T> Map<String, T> parseHashMap(String jsonString, Class<T> tClass) {
        TypeReference<Map<String, T>> typeReference = new TypeReference<Map<String, T>>() {};
        try {
            return objectMapper.readValue(jsonString, typeReference);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <K, V> TreeMap<K, V> parseTreeMap(String jsonString, Class<K> kClass, Class<V> vClass) {
        TypeReference<TreeMap<K, V>> typeReference = new TypeReference<TreeMap<K, V>>() {};
        try {
            return objectMapper.readValue(jsonString, typeReference);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static JsonNode toJsonNode(String jsonString) {
        try {
            return objectMapper.readTree(jsonString);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static JsonNode toJsonNode(Object obj) {
        return objectMapper.valueToTree(obj);
    }

    public static <T> T convertToObject(Object fromValue, Class<T> toValueType) {
        return objectMapper.convertValue(fromValue, toValueType);
    }

    public static <T> T convertToObject(Object fromValue, TypeReference<T> typeReference) {
        return objectMapper.convertValue(fromValue, typeReference);
    }

    public static <T> T parseObject(ByteBuffer byteBuffer, Class<T> tClass) {
        try {
            return objectMapper.readValue(byteBuffer.array(), tClass);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseObject(byte[] bytes, Class<T> tClass) {
        try {
            return objectMapper.readValue(bytes, tClass);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseObject(byte[] bytes, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(bytes, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T cloneObject(Object obj, TypeReference<T> typeReference) {
        return objectMapper.convertValue(obj, typeReference);
    }
}
