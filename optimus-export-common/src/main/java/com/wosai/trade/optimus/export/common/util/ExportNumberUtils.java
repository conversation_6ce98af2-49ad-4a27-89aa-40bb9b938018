package com.wosai.trade.optimus.export.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2021/9/30 Time: 4:38 下午
 */
public class ExportNumberUtils {
    public static final String DEFAULT_VALUE = "0.00";
    private static final String CENT_TO_YUAN_BASE = "100";


    public static String centToYuan(Long amount) {
        return centToYuan(amount,DEFAULT_VALUE);
    }

    public static String centToYuan(Long amount, String defaultValue) {
        if (Objects.isNull(amount)) {
            return defaultValue;
        }
        return NumberUtils.toScaledBigDecimal(Long.toString(amount)
                        , 2, RoundingMode.HALF_UP).divide(new BigDecimal(CENT_TO_YUAN_BASE)
                        , 2, RoundingMode.HALF_UP)
                .toString();
    }

    public static long yuanToCent(String amount) {
        return NumberUtils.toScaledBigDecimal(amount
                        , 2, RoundingMode.HALF_UP).multiply(new BigDecimal(CENT_TO_YUAN_BASE))
                .toBigInteger().longValue();
    }

    public static long calculateTotalPrice(long amount, String quantity) {
        BigDecimal bd1 = new BigDecimal(amount);
        BigDecimal bd2 = new BigDecimal(quantity);
        BigDecimal result = bd1.multiply(bd2);
        return result.setScale(0, RoundingMode.HALF_UP).longValue();
    }

    public static String calculateStringSum(String s1, String s2) {
        BigDecimal num1 = new BigDecimal(s1);
        BigDecimal num2 = new BigDecimal(s2);
        return num1.add(num2).toString();
    }

    public static BigDecimal convertToBigDecimal(String s) {
        if (StringUtils.isEmpty(s)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(s);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }
    public static int compare(BigDecimal num1, BigDecimal num2) {
        return num1.compareTo(num2);
    }

    public static boolean isEqual(BigDecimal num1, BigDecimal num2) {
        return num1.compareTo(num2) == 0;
    }

    public static boolean isGreaterThan(BigDecimal num1, BigDecimal num2) {
        return num1.compareTo(num2) > 0;
    }

    public static boolean isLessThan(BigDecimal num1, BigDecimal num2) {
        return num1.compareTo(num2) < 0;
    }


}
