package com.wosai.trade.optimus.export.api.result.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> Date: 2023/7/18 Time: 09:55
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class OrderExportTaskQueryResult {

    public static final OrderExportTaskQueryResult DEFAULT = new OrderExportTaskQueryResult().setUrl(null);

    private String url;
}
