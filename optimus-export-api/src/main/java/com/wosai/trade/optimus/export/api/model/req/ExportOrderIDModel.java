package com.wosai.trade.optimus.export.api.model.req;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 2:15 PM
 */
@Getter
@Builder
@ToString
@Jacksonized
public class ExportOrderIDModel {

    /**
     * 订单编号
     */
    @NotEmpty(message = "订单编号不能为空")
    @Size(max = 40, message = "订单编号格式错误")
    private String sn;

    /**
     * 订单签名
     */
    @NotEmpty(message = "订单签名不能为空")
    @Size(max = 40, message = "订单签名格式错误")
    private String signature;

}
