package com.wosai.trade.optimus.export.api.request.order;

import com.wosai.trade.optimus.export.api.model.req.ExportOrderIDModel;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 2:15 PM
 */
@Getter
@ToString
@SuperBuilder(toBuilder = true)
public class BaseOrderRequest {

    @Valid
    @NotNull(message = "订单ID不能为空")
    private ExportOrderIDModel orderID;

}
