package com.wosai.trade.optimus.export.api.request.order;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> Date: 2023/7/18 Time: 09:55
 */
@Getter
@Builder(toBuilder = true)
@ToString
@Jacksonized
public class OrderExportTaskQueryRequest {


    @NotEmpty(message = "任务ID不能为空")
    @Size(max = 64, message = "任务ID格式错误")
    private String taskId;


}
