package com.wosai.trade.optimus.export.api.model.req;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Date: 2023/5/11 Time: 4:44 PM
 */
@Getter
@Builder
@ToString
@Jacksonized
public class ExportPagingModel {

    /**
     * 当前页
     */
    @NotNull(message = "页数不能为空")
    @Min(value = 1, message = "页数格式错误")
    private Integer currentPage;

    /**
     * 每页大小
     */
    @NotNull(message = "查询数量不能为空")
    @Min(value = 1, message = "查询数量格式错误")
    private Integer pageSize;


}
