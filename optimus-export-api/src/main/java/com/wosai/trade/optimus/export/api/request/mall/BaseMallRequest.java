package com.wosai.trade.optimus.export.api.request.mall;

import com.wosai.trade.optimus.export.api.model.req.ExportMallIDModel;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 2:15 PM
 */
@Getter
@ToString
@SuperBuilder(toBuilder = true)
public class BaseMallRequest {

    @Valid
    @NotNull(message = "商城ID不能为空")
    protected ExportMallIDModel mallID;

}
