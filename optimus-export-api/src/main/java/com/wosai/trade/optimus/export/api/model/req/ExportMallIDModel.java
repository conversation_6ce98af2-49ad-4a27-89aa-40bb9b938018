package com.wosai.trade.optimus.export.api.model.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wosai.trade.optimus.export.api.constant.PatternConstant;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 2:15 PM
 */
@Getter
@Builder
@ToString
@Jacksonized
public class ExportMallIDModel {

    /**
     * 商城编号
     */
    @NotEmpty(message = "商城编号不能为空")
    @Pattern(regexp = PatternConstant.ID_PATTERN, message = "商城编号格式错误")
    private String mallSn;

    /**
     * 商城签名
     */
    @NotEmpty(message = "商城签名不能为空")
    @Size(max = 40, message = "商城签名格式错误")
    private String signature;

    @JsonIgnore
    public Long getMallId() {
        return Long.parseLong(mallSn);
    }

}
