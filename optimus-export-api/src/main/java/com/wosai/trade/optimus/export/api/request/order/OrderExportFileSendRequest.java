package com.wosai.trade.optimus.export.api.request.order;

import com.wosai.general.annotation.UserContext;
import com.wosai.trade.optimus.export.api.model.req.ExportSellerIDModel;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> Date: 2023/7/19 Time: 19:56
 */
@Getter
@Builder(toBuilder = true)
@ToString
@Jacksonized
public class OrderExportFileSendRequest {

    @Valid
    @NotNull(message = "卖家信息不能为空")
    @UserContext
    private ExportSellerIDModel seller;

    @NotEmpty(message = "任务ID不能为空")
    @Size(max = 64, message = "任务ID格式错误")
    private String taskId;

    @Size(max = 128, message = "接收邮箱格式错误")
    private String email;


}
