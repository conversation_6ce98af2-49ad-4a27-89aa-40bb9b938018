package com.wosai.trade.optimus.export.api;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.general.result.PagingResult;
import com.wosai.general.result.SingleResult;
import com.wosai.general.result.VoidResult;
import com.wosai.trade.optimus.export.api.request.order.OrderExportFileSendRequest;
import com.wosai.trade.optimus.export.api.request.order.OrderExportTaskCreateRequest;
import com.wosai.trade.optimus.export.api.request.order.OrderExportTaskQueryRequest;
import com.wosai.trade.optimus.export.api.request.order.OrderExportTasksPagingRequest;
import com.wosai.trade.optimus.export.api.result.order.OrderExportTaskCreateResult;
import com.wosai.trade.optimus.export.api.result.order.OrderExportTaskQueryResult;
import com.wosai.trade.optimus.export.api.result.order.OrderExportTasksQueryResult;

/**
 * <AUTHOR> Date: 2023/5/11 Time: 10:42 AM
 */
@JsonRpcService("/rpc/order")
public interface ExportOrderService {

    SingleResult<OrderExportTaskCreateResult> createOrderExportTask(OrderExportTaskCreateRequest request);

    PagingResult<OrderExportTasksQueryResult> pagingOrderExportTasks(OrderExportTasksPagingRequest request);

    VoidResult sendExportFile(OrderExportFileSendRequest request);

    SingleResult<OrderExportTaskQueryResult> queryOrderExportTask(OrderExportTaskQueryRequest request);

}
