package com.wosai.trade.optimus.export.api.request.order;

import com.wosai.general.annotation.UserContext;
import com.wosai.trade.optimus.export.api.model.req.ExportPagingModel;
import com.wosai.trade.optimus.export.api.model.req.ExportSellerIDModel;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Date: 2023/7/18 Time: 09:56
 */
@Getter
@Builder(toBuilder = true)
@ToString
@Jacksonized
public class OrderExportTasksPagingRequest {

    @Valid
    @NotNull(message = "卖家信息不能为空")
    @UserContext
    private ExportSellerIDModel seller;

    @Valid
    @NotNull(message = "分页数据格式错误")
    private ExportPagingModel paging;

    private Byte receiveMethod;


}
