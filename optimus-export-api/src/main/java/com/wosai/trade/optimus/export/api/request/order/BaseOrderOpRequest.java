package com.wosai.trade.optimus.export.api.request.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wosai.general.annotation.UserContext;
import com.wosai.trade.optimus.export.api.model.req.ExportSellerIDModel;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 2:15 PM
 */
@Getter
@SuperBuilder(toBuilder = true)
@ToString
@Jacksonized
public class BaseOrderOpRequest extends BaseOrderRequest {

    @Valid
    @UserContext
    private ExportSellerIDModel seller;

    @JsonIgnore
    @AssertTrue(message = "卖家信息不能为空")
    public boolean isValidSeller() {
        return Objects.nonNull(seller);
    }

}
