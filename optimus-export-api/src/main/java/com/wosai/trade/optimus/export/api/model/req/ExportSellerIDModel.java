package com.wosai.trade.optimus.export.api.model.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wosai.general.annotation.UserInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 2:15 PM
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ExportSellerIDModel {

    @Size(max = 40, message = "商户ID不能格式错误")
    @UserInfo(value = "merchant_id")
    private String merchantId;

    @Size(max = 40, message = "集团商户ID格式错误")
    @UserInfo(value = "group_id")
    private String groupId;

    @Size(max = 40, message = "集团商户用户ID格式错误")
    @UserInfo(value = "group_user_id")
    private String groupUserId;

    @Size(max = 40, message = "商户用户ID格式错误")
    @UserInfo(value = "merchant_user_id")
    private String merchantUserId;

    @NotEmpty(message = "用户角色不能为空")
    @Size(max = 40, message = "用户角色格式错误")
    @UserInfo(value = "role")
    private String role;

    @JsonIgnore
    @AssertTrue(message = "商户信息不能为空")
    public boolean isValidMerchant() {
        return StringUtils.isNotEmpty(merchantId) && StringUtils.isNotEmpty(merchantUserId)
                || StringUtils.isNotEmpty(groupId) && StringUtils.isNotEmpty(groupUserId);
    }

}
