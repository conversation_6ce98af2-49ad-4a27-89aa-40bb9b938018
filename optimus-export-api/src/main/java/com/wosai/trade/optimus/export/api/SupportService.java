package com.wosai.trade.optimus.export.api;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.general.result.SingleResult;
import com.wosai.trade.optimus.export.api.request.order.DownloadCenterTaskCallBackRequest;
import com.wosai.trade.optimus.export.api.support.DownloadCenterTaskCallBackResult;

/**
 * <AUTHOR> Date: 2024/7/22 Time: 10:33
 */
@JsonRpcService("/rpc/support")
public interface SupportService {

    /**
     * 下载中心提交任务回调
     */
    SingleResult<DownloadCenterTaskCallBackResult> submitTask(String taskLogId, Integer type, DownloadCenterTaskCallBackRequest downLoadCenterTaskCallBackRequest) ;

    /**
     * 下载中心获取下载信息
     */
    String queryFileUrl(String url) ;


}
