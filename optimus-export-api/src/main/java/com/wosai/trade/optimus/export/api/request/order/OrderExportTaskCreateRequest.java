package com.wosai.trade.optimus.export.api.request.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wosai.general.annotation.UserContext;
import com.wosai.trade.optimus.export.api.model.req.ExportMallIDModel;
import com.wosai.trade.optimus.export.api.model.req.ExportSellerIDModel;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR> Date: 2023/6/16 Time: 2:38 PM
 */
@Getter
@SuperBuilder(toBuilder = true)
@ToString
@Jacksonized
public class OrderExportTaskCreateRequest {
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 卖家检索信息
     */
    @NotNull(message = "卖家信息不能为空")
    @UserContext
    @Valid
    private ExportSellerIDModel seller;

    /**
     * 商城标识列表
     */
    @Valid
    @NotEmpty(message = "商城标识列表不能为空")
    @Size(max = 100, message = "最大支持一次导出100个商城的订单数据，请缩小商城选择范围")
    private List<ExportMallIDModel> mallIDList;

    /**
     * 订单状态列表
     */
    @NotEmpty(message = "订单状态列表不能为空")
    private List<Integer> orderStates;

    /**
     * 售后单状态列表
     */
    @NotEmpty(message = "售后单状态列表不能为空")
    private List<Integer> ticketStates;

    /**
     * 实体创建开始时间
     */
    @NotEmpty(message = "开始时间格式错误")
    @Pattern(regexp = "^\\d{14}$", message = "开始时间格式错误")
    private String beginDateTime;

    /**
     * 实体创建结束时间
     */
    @NotEmpty(message = "结束时间格式错误")
    @Pattern(regexp = "^\\d{14}$", message = "结束时间格式错误")
    private String endDateTime;

    /**
     * 接收方式
     */
    @NotNull(message = "接收方式不能为空")
    private Byte receiveMethod;

    /**
     * 接收邮箱
     */
    @Size(max = 128, message = "接收邮箱格式错误")
    private String email;

    /**
     * 交易流水号
     */
    private String payTsn;


    /**
     * 订单号
     */
    private String orderSn;


    @JsonIgnore
    @AssertTrue(message = "结束时间必须大于开始时间")
    public boolean isValidDate() {
        if(StringUtils.isEmpty(beginDateTime) || StringUtils.isEmpty(endDateTime)){
            return true;
        }
        LocalDateTime beginDateLLocalDateTime = LocalDateTime.from(DATE_TIME_FORMATTER.parse(beginDateTime));
        LocalDateTime endDateLLocalDateTime = LocalDateTime.from(DATE_TIME_FORMATTER.parse(endDateTime));
        return !beginDateLLocalDateTime.isAfter(endDateLLocalDateTime);
    }
}
