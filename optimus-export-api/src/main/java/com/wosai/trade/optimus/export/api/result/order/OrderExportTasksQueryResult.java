package com.wosai.trade.optimus.export.api.result.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> Date: 2023/7/18 Time: 11:04
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class OrderExportTasksQueryResult {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务状态
     */
    private Byte state;

    /**
     * 接收邮箱
     */
    private String email;

    /**
     * 任务创建时间
     */
    private String createdAt;

    /**
     * 文件名称
     */
    private String taskName;

}
