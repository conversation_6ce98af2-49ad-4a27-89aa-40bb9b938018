package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view;

import com.google.common.collect.Lists;
import com.wosai.trade.optimus.export.common.util.ExportNumberUtils;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> Date: 2024/8/6 Time: 11:34
 */
@Builder(access = AccessLevel.PRIVATE)
public class MallOrderVolumeSummaryView {
    private static final MallOrderVolumeSummaryView DEFAULT_EMPTY_INSTANCE = MallOrderVolumeSummaryView.builder().build();
    private static final List<List<String>> HEADERS = List.of(List.of("商城销售汇总", "商户名")
            , List.of("商城销售汇总", "商户号")
            , List.of("商城销售汇总", "门店名")
            , List.of("商城销售汇总", "门店号")
            , List.of("商城销售汇总", "商城名称")
            , List.of("商城销售汇总", "笔数汇总")
            , List.of("商城销售汇总", "收款金额汇总")
            , List.of("商城销售汇总", "优惠金额汇总")
            , List.of("商城销售汇总", "实收金额汇总"));
    private static final List<String> TAIL_LINE_SUMMARY_PREFIX = List.of("汇总", "", "", "", "");

    private final List<List<?>> contents;


    public static MallOrderVolumeSummaryView newInstance(List<MallOrderVolumeSummaryItem> rowData) {
        if (CollectionUtils.isEmpty(rowData)) {
            return DEFAULT_EMPTY_INSTANCE;
        }
        List<List<?>> contents = Lists.newArrayListWithCapacity(rowData.size());

        long totalCount = 0L;
        long totalAmount = 0L;
        long disCountTotalAmount = 0L;
        long receiveTotalAmount = 0L;
        for (MallOrderVolumeSummaryItem summaryItem : rowData) {
            totalCount += summaryItem.getMallTotalCount();
            totalAmount += summaryItem.getMallTotalAmount();
            disCountTotalAmount += summaryItem.getMallDiscountTotalAmount();
            receiveTotalAmount += summaryItem.getMallReceiveTotalAmount();
            contents.add(List.of(summaryItem.getMerchantName()
                    , summaryItem.getMerchantSn()
                    , summaryItem.getStoreName()
                    , summaryItem.getStoreSn()
                    , summaryItem.getMallName()
                    , summaryItem.getMallTotalCount()
                    , ExportNumberUtils.centToYuan(summaryItem.getMallTotalAmount())
                    , ExportNumberUtils.centToYuan(summaryItem.getMallDiscountTotalAmount())
                    , ExportNumberUtils.centToYuan(summaryItem.getMallReceiveTotalAmount())));
        }

        List<Object> tailLine = Lists.newArrayList(TAIL_LINE_SUMMARY_PREFIX);
        tailLine.add(totalCount);
        tailLine.add(ExportNumberUtils.centToYuan(totalAmount));
        tailLine.add(ExportNumberUtils.centToYuan(disCountTotalAmount));
        tailLine.add(ExportNumberUtils.centToYuan(receiveTotalAmount));

        contents.add(tailLine);

        return MallOrderVolumeSummaryView.builder()
                .contents(contents)
                .build();
    }

    public static List<List<String>> getHeaders() {
        return HEADERS;
    }

    public boolean isEmpty() {
        return this == DEFAULT_EMPTY_INSTANCE || CollectionUtils.isEmpty(contents);
    }

    public List<List<?>> getContents() {
        if (isEmpty()) {
            return List.of();
        }
        return contents;
    }


    @Getter
    @Builder
    public static class MallOrderVolumeSummaryItem {
        private final String merchantName;
        private final String merchantSn;
        private final String storeName;
        private final String storeSn;
        private final String mallName;
        private final long mallTotalCount;
        private final long mallTotalAmount;
        private final long mallDiscountTotalAmount;
        private final long mallReceiveTotalAmount;
    }

}
