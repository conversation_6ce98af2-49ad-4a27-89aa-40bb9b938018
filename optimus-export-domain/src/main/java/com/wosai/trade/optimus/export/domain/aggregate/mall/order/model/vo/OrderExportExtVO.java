package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo;

import com.wosai.trade.optimus.export.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR> Date: 2023/7/17 Time: 09:11
 */
@Getter
@Builder(toBuilder = true)
@Jacksonized
public class OrderExportExtVO extends BaseVO<OrderExportExtVO> {

    private String merchantSn;
    private String merchantName;
    private String storeSn;
    private String storeName;
    private String mallSn;
    private String mallName;
    private Integer mallSize;
    private String taskLogId;

    public static OrderExportExtVO newEmptyInstance() {
        return OrderExportExtVO.builder().build();
    }

    @Override
    protected OrderExportExtVO doReplaceNotNull(OrderExportExtVO vo) {
        return null;
    }
}
