package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model;

import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.support.PropertiesSupporter;
import com.wosai.trade.optimus.export.common.util.ApplicationContextUtils;
import com.wosai.trade.optimus.export.common.util.SecurityUtils;
import com.wosai.trade.optimus.export.domain.aggregate.BaseEntity;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.enums.ExportStateEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.enums.ReceiveMethodEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportInfoVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportOperatorVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportResultVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.OrderExportExtVO;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/7/16 Time: 17:40
 */
@Getter
@Setter(AccessLevel.PACKAGE)
public class ExportOrderAggrRoot extends BaseEntity {
    private static final String AES_KEY = "ZH2QB36LR9a2fhEm";
    private static final String FILE_PATH_TEMPLATE = "optimus/%s/export/order/%s/%s/%s";
    private static final long VALID_PERIOD_DAY = 7L;
    private static final int MAX_FILENAME_LENGTH = 255;

    private static final String TEXT_TEMPLATE = """
                商家您好:
                
                商户：%s(商户号：%s)，门店：%s(门店号：%s)，商城：%s(商城号：%s)%s，%s——%s的交易订单明细文件已生成，请查看附件。
                
                祝生意兴隆！
            """;
    private static final String EXPORT_CONTENT_TYPE = "application/vnd.ms-excel";
    private static final DateTimeFormatter ORDER_EXCEL_DATE_TIME_FORMATTER
            = DateTimeFormatter.ofPattern("yyyy年-MM月-dd日 HH:mm:ss");
    private static final DateTimeFormatter ORDER_EXPORT_FILE_NAME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final String MALL_SIZE_FORMATTER = "等%s家收款商城";
    private static final String SIMPLE_TASK_NAME_MULTI_MALL_TEMPLATE = "%s等%s家商城";
    private static final String SIMPLE_TASK_NAME_SINGLE_MALL_TEMPLATE = "%s";
    private static final String TASK_NAME_MULTI_MALL_TEMPLATE = "【%s等%s家收款商城】%s-%s订单明细";
    private static final String TASK_NAME_SINGLE_MALL_TEMPLATE = "【%s】%s-%s订单明细";
    private static final String DISPLAY_FILE_NAME_TEMPLATE = "%s.xlsx";
    public static final int SINGLE_MALL_COUNT = 1;

    @NotNull(message = "导出状态不能为空")
    private ExportStateEnum state;
    @Valid
    @NotNull(message = "导出操作人不能为空")
    private ExportOperatorVO operator;
    @Valid
    @NotNull(message = "导出信息不能为空")
    private ExportInfoVO exportInfo;
    @Valid
    @NotNull(message = "导出结果不能为空")
    private ExportResultVO exportResult;
    @Valid
    @NotNull(message = "扩展字段不能为空")
    private OrderExportExtVO ext;
    @NotNull(message = "过期时间不能为空")
    private LocalDateTime expiredAt;

    public static ExportOrderAggrRoot newEmptyInstance() {
        return new ExportOrderAggrRoot();
    }


    public void checkExist() {
        if (isNotExist()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.MALL_EXPORT_ORDER_NOT_EXIST);
        }
    }

    public void updateState(ExportStateEnum state) {
        if (!Objects.equals(this.state, state)) {
            this.state = state;
            markForModify();
        }
    }

    public void replaceResult(ExportOrderAggrRoot aggrRoot) {
        if (aggrRoot.isExist()
                && aggrRoot.getState().isGenerateSuccess()
                && Objects.equals(aggrRoot.getExportInfo().getDigest(), exportInfo.getDigest())) {
            state = ExportStateEnum.GENERATE_SUCCESS;
            exportResult = exportResult.replaceNotNull(aggrRoot.getExportResult());
            markForModify();
        }
    }

    public String genExportOrderDescription() {
        LocalDateTime beginTime = exportInfo.getBeginDateTime();
        LocalDateTime endTime = exportInfo.getEndDateTime();

        int mallSize = ext.getMallSize();
        String sizeExpression = StringUtils.EMPTY;
        if (mallSize > SINGLE_MALL_COUNT) {
            sizeExpression = String.format(MALL_SIZE_FORMATTER, mallSize);
        }

        String beginFormatTime = ORDER_EXCEL_DATE_TIME_FORMATTER.format(beginTime);
        String endFormatTime = ORDER_EXCEL_DATE_TIME_FORMATTER.format(endTime);
        return String.format(TEXT_TEMPLATE
                , ext.getMerchantName()
                , ext.getMerchantSn()
                , ext.getStoreName()
                , ext.getStoreSn()
                , ext.getMallName()
                , ext.getMallSn()
                , sizeExpression
                , beginFormatTime
                , endFormatTime);
    }

    public String genFilePath() {
        String env = ApplicationContextUtils.getBean(PropertiesSupporter.class).getEnv();
        return String.format(FILE_PATH_TEMPLATE, env, operator.getOwnerId(), exportInfo.getDigest(), exportInfo.genStorageFileName());
    }

    public String getExportContentType() {
        return EXPORT_CONTENT_TYPE;
    }

    public void updateReceiveEmail(String email) {
        if (StringUtils.isNotEmpty(email)) {
            exportInfo = exportInfo.replaceNotNull(ExportInfoVO.builder()
                    .receiveEmail(email)
                    .build());
            markForModify();
        }
    }

    public String getCiphertextId() {
        return SecurityUtils.encryptWithAES(getIdStr(), AES_KEY);
    }

    public static String getPlaintextId(String ciphertextId) {
        try {
            return SecurityUtils.decryptWithAES(ciphertextId, AES_KEY);
        } catch (Exception exception){
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.MALL_EXPORT_ORDER_NOT_EXIST);
        }
    }

    public boolean isExportHistoryOrder() {
        LocalDateTime endDateTime = exportInfo.getEndDateTime();
        if (Objects.isNull(endDateTime)) {
            return false;
        }
        return LocalDateTime.now().isAfter(endDateTime);
    }

    public boolean isNeedUpload() {
        return isExportHistoryOrder() || getExportInfo().isNeedInvokeTaskCenter();
    }

    public String genExportTaskName() {
        LocalDate beginDate = exportInfo.getBeginDateTime().toLocalDate();
        LocalDate endDate = exportInfo.getEndDateTime().toLocalDate();
        String mallName = ext.getMallName();
        Integer mallSize = ext.getMallSize();
        if (mallSize > SINGLE_MALL_COUNT) {
            return String.format(TASK_NAME_MULTI_MALL_TEMPLATE
                    , mallName
                    , mallSize
                    , beginDate.format(ORDER_EXPORT_FILE_NAME_FORMATTER)
                    , endDate.format(ORDER_EXPORT_FILE_NAME_FORMATTER));
        }

        return String.format(TASK_NAME_SINGLE_MALL_TEMPLATE
                , mallName
                , beginDate.format(ORDER_EXPORT_FILE_NAME_FORMATTER)
                , endDate.format(ORDER_EXPORT_FILE_NAME_FORMATTER));
    }

    public String genExportSimpleTaskName() {
        String mallName = ext.getMallName();
        Integer mallSize = ext.getMallSize();
        if (mallSize > SINGLE_MALL_COUNT) {
            return String.format(SIMPLE_TASK_NAME_MULTI_MALL_TEMPLATE
                    , mallName
                    , mallSize);
        }

        return String.format(SIMPLE_TASK_NAME_SINGLE_MALL_TEMPLATE
                , mallName);
    }

    public String genAttachmentFileName() {
        return String.format(DISPLAY_FILE_NAME_TEMPLATE, genExportTaskName());
    }

    public boolean isNotExistExportInfo() {
        return Objects.isNull(exportInfo);
    }

    public List<Map<String, String>> getMallSnSignaturePairs() {
        if(isNotExistExportInfo()){
            return null;
        }
        return exportInfo.getMallSnSignaturePairs();
    }

    public boolean isNeedHandleTaskCenter() {
        return Objects.nonNull(ext.getTaskLogId()) &&
                ReceiveMethodEnum.TASK_CENTER == exportInfo.getReceiveMethod();
    }

    @Override
    protected void setId(Long id) {
        super.id = id;
    }

    @Override
    protected void setCtime(LocalDateTime ctime) {
        super.ctime = ctime;
    }

    @Override
    protected void setMtime(LocalDateTime mtime) {
        super.mtime = mtime;
    }

    @Override
    protected void setVersion(Long version) {
        super.version = version;
    }

}
