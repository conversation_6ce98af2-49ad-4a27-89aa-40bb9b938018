package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view;

import com.google.common.collect.Lists;
import com.wosai.trade.optimus.export.common.util.ExportNumberUtils;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> Date: 2024/8/6 Time: 11:35
 */
@Builder(access = AccessLevel.PRIVATE)
public class MallAftersaleVolumeSummaryView {

    private static final MallAftersaleVolumeSummaryView DEFAULT_EMPTY_INSTANCE = MallAftersaleVolumeSummaryView.builder().build();
    private static final List<List<String>> HEADERS = List.of(List.of("商城售后汇总", "商户名")
            , List.of("商城售后汇总", "商户号")
            , List.of("商城售后汇总", "门店名")
            , List.of("商城售后汇总", "门店号")
            , List.of("商城售后汇总", "商城名称")
            , List.of("商城售后汇总", "售后笔数汇总")
            , List.of("商城售后汇总", "退款笔数汇总")
            , List.of("商城售后汇总", "退款金额汇总"));
    private static final List<String> TAIL_LINE_SUMMARY_PREFIX = List.of("汇总", "", "", "", "");

    private final List<List<?>> contents;


    public static MallAftersaleVolumeSummaryView newInstance(List<MallAftersaleVolumeSummaryItem> rowData) {
        if (CollectionUtils.isEmpty(rowData)) {
            return DEFAULT_EMPTY_INSTANCE;
        }
        List<List<?>> contents = Lists.newArrayListWithCapacity(rowData.size());

        long aftersaleTotalCount = 0L;
        long refundTotalCount = 0L;
        long refundTotalAmount = 0L;
        for (MallAftersaleVolumeSummaryItem summaryItem : rowData) {
            aftersaleTotalCount += summaryItem.getMallAftersaleTotalCount();
            refundTotalCount += summaryItem.getMallRefundTotalCount();
            refundTotalAmount += summaryItem.getMallRefundTotalAmount();
            contents.add(List.of(summaryItem.getMerchantName()
                    , summaryItem.getMerchantSn()
                    , summaryItem.getStoreName()
                    , summaryItem.getStoreSn()
                    , summaryItem.getMallName()
                    , summaryItem.getMallAftersaleTotalCount()
                    , summaryItem.getMallRefundTotalCount()
                    , ExportNumberUtils.centToYuan(summaryItem.getMallRefundTotalAmount())));
        }

        List<Object> tailLine = Lists.newArrayList(TAIL_LINE_SUMMARY_PREFIX);
        tailLine.add(aftersaleTotalCount);
        tailLine.add(refundTotalCount);
        tailLine.add(ExportNumberUtils.centToYuan(refundTotalAmount));

        contents.add(tailLine);

        return MallAftersaleVolumeSummaryView.builder()
                .contents(contents)
                .build();
    }

    public static List<List<String>> getHeaders() {
        return HEADERS;
    }

    public boolean isEmpty() {
        return this == DEFAULT_EMPTY_INSTANCE || CollectionUtils.isEmpty(contents);
    }

    public List<List<?>> getContents() {
        if (isEmpty()) {
            return List.of();
        }
        return contents;
    }


    @Getter
    @Builder
    public static class MallAftersaleVolumeSummaryItem {
        private final String merchantName;
        private final String merchantSn;
        private final String storeName;
        private final String storeSn;
        private final String mallName;
        private final long mallAftersaleTotalCount;
        private final long mallRefundTotalCount;
        private final long mallRefundTotalAmount;
    }
    
}
