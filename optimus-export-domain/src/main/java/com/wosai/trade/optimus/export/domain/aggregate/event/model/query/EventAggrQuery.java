package com.wosai.trade.optimus.export.domain.aggregate.event.model.query;

import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventStateEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Date: 2022/12/29 Time: 2:03 PM
 */
@Getter
@Builder
public class EventAggrQuery {

    private Long id;
    private EventStateEnum state;
    private EventTypeEnum type;
    private String associatedSn;
    private LocalDateTime nextProcessTime;
    private Integer count;
    private List<Long> idList;


}
