package com.wosai.trade.optimus.export.domain.aggregate.event.model;

import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.util.SecurityUtils;
import com.wosai.trade.optimus.export.domain.aggregate.BaseEntity;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventStateEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.vo.DelayRuleVO;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.vo.EventContentVO;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.vo.EventExtVO;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/6/16 Time: 6:53 PM
 */
@Getter
@Setter(AccessLevel.PACKAGE)
public class EventAggrRoot extends BaseEntity {
    private static final String AES_KEY = "Q5H1J1h8K9D2fhEJ";
    private static final String DEFAULT_MQ_KEY = "2718281828459";
    private static final String CHANGED_TO_PENDING_MANUAL_PROCESSING_RESULT = "变更为手动待处理";

    @NotNull(message = "事件类型不能为空")
    private EventTypeEnum type;
    @NotNull(message = "事件状态不能为空")
    private EventStateEnum state;
    @NotNull(message = "事件结果不能为空")
    private String result;
    @NotNull(message = "下次处理时间不能为空")
    private LocalDateTime nextProcessTime;
    @NotNull(message = "事件关联单号不能为空")
    private String associatedSn;

    @Valid
    @NotNull(message = "事件内容不能为空")
    private EventContentVO content;
    @Valid
    @NotNull(message = "延迟规则不能为空")
    private DelayRuleVO delayRule;
    @Valid
    @NotNull(message = "事件扩展字段不能为空")
    private EventExtVO ext;

    protected EventAggrRoot() {
    }


    public static EventAggrRoot newEmptyInstance() {
        return new EventAggrRoot();
    }


    public void checkExist() {
        if (isNotExist()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.EVENT_NOT_EXIST);
        }
    }

    public boolean isProcessed() {
        return isExist() && Objects.equals(state, EventStateEnum.PROCESSED);
    }


    public Long getAssociatedSnNum() {
        return Long.parseLong(associatedSn);
    }

    public void processSuccess(String result) {
        this.result = result;
        state = EventStateEnum.PROCESSED;
        delayRule = delayRule.increaseProcessedCount();
        markForModify();
    }

    public void processFailure(String result) {
        this.result = result;
        nextProcessTime = nextProcessTime.plusSeconds(delayRule.getNextProcessIntervalSecond());
        delayRule = delayRule.increaseProcessedCount();
        if (delayRule.isReachProcessedMax()) {
            state = EventStateEnum.EXCEEDED_PROCESSING_LIMIT;
        }
        markForModify();
    }

    public void processStageCompleted(String result) {
        this.state = EventStateEnum.STAGE_COMPLETED;
        this.result = result;
        delayRule = delayRule.increaseProcessedCount();
        markForModify();
    }

    public void updateToPendingManualProcessing() {
        this.state = EventStateEnum.PENDING_MANUAL_PROCESSING;
        this.result = CHANGED_TO_PENDING_MANUAL_PROCESSING_RESULT;
        markForModify();
    }

    public boolean isNeedAlert() {
        return !Objects.equals(state, EventStateEnum.PROCESSED)
                && delayRule.getProcessedCount() >= delayRule.getAlarmThreshold();
    }

    public String getMqKey() {
        return ext.getMqKey();
    }

    public String getMqKeyNullDefault() {
        String mqKey = getMqKey();
        if (Objects.isNull(mqKey)) {
            return DEFAULT_MQ_KEY;
        }
        return mqKey;
    }

    public String getCiphertextId() {
        return SecurityUtils.encryptWithAES(getIdStr(), AES_KEY);
    }

    public static String getPlaintextId(String ciphertextId) {
        return SecurityUtils.decryptWithAES(ciphertextId, AES_KEY);
    }

    @Override
    protected void setId(Long id) {
        super.id = id;
    }

    @Override
    protected void setCtime(LocalDateTime ctime) {
        super.ctime = ctime;
    }

    @Override
    protected void setMtime(LocalDateTime mtime) {
        super.mtime = mtime;
    }

    @Override
    protected void setVersion(Long version) {
        super.version = version;
    }

}
