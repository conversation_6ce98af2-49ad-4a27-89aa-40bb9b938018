package com.wosai.trade.optimus.export.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wosai.trade.optimus.export.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;


/**
 * <AUTHOR> Date: 2022/12/29 Time: 11:38 AM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventContentVO extends BaseVO<EventContentVO> {

    private String bizContent;

    public static EventContentVO newEmptyInstance() {
        return EventContentVO.builder().build();
    }

    @Override
    protected EventContentVO doReplaceNotNull(EventContentVO vo) {
        return null;
    }
}
