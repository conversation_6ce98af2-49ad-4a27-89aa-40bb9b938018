package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view;

import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR> Date: 2024/8/6 Time: 11:34
 */
@Builder(access = AccessLevel.PRIVATE)
public class MallOrderProductSummaryView {
    private static final MallOrderProductSummaryView DEFAULT_EMPTY_INSTANCE = MallOrderProductSummaryView.builder().build();
    private static final List<List<String>> HEADERS = List.of(List.of("商城商品销售汇总", "商户名")
            , List.of("商城商品销售汇总", "商户号")
            , List.of("商城商品销售汇总", "门店名")
            , List.of("商城商品销售汇总", "门店号")
            , List.of("商城商品销售汇总", "商城名称")
            , List.of("商城商品销售汇总", "商品名称")
            , List.of("商城商品销售汇总", "数量"));
    private static final List<String> TAIL_LINE_SUMMARY_PREFIX = List.of("汇总", "", "", "", "", "");

    private final List<List<?>> contents;

    public static MallOrderProductSummaryView newInstance(List<MallOrderProductSummaryItem> rowData) {
        if (CollectionUtils.isEmpty(rowData)) {
            return DEFAULT_EMPTY_INSTANCE;
        }
        List<List<?>> contents = Lists.newArrayListWithCapacity(rowData.size());

        BigDecimal totalQuantity = new BigDecimal("0");
        for (MallOrderProductSummaryItem summaryItem : rowData) {
            totalQuantity = totalQuantity.add(summaryItem.getProductQuantity());

            contents.add(List.of(summaryItem.getMerchantName()
                    , summaryItem.getMerchantSn()
                    , summaryItem.getStoreName()
                    , summaryItem.getStoreSn()
                    , summaryItem.getMallName()
                    , summaryItem.getProductName()
                    , summaryItem.getQuantityStringValue()));
        }

        List<Object> tailLine = Lists.newArrayList(TAIL_LINE_SUMMARY_PREFIX);
        tailLine.add(totalQuantity.setScale(2, RoundingMode.HALF_UP).toString());

        contents.add(tailLine);

        return MallOrderProductSummaryView.builder()
                .contents(contents)
                .build();
    }


    public static List<List<String>> getHeaders() {
        return HEADERS;
    }

    public boolean isEmpty() {
        return this == DEFAULT_EMPTY_INSTANCE || CollectionUtils.isEmpty(contents);
    }

    public List<List<?>> getContents() {
        if (isEmpty()) {
            return List.of();
        }
        return contents;
    }



    @Getter
    @Builder
    public static class MallOrderProductSummaryItem {
        private final String merchantName;
        private final String merchantSn;
        private final String storeName;
        private final String storeSn;
        private final String mallName;
        private final String productName;
        private final BigDecimal productQuantity;

        public String getQuantityStringValue() {
            return productQuantity.setScale(2, RoundingMode.HALF_UP).toString();
        }
    }
}
