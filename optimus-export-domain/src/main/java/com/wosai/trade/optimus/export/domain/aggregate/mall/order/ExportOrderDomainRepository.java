package com.wosai.trade.optimus.export.domain.aggregate.mall.order;


import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.query.ExportMallOrderAggrQuery;

import java.util.List;

/**
 * <AUTHOR> Date: 2023/7/16 Time: 17:39
 */
public interface ExportOrderDomainRepository {

    void save(ExportOrderAggrRoot aggrRoot);

    ExportOrderAggrRoot query(ExportMallOrderAggrQuery aggrQuery);

    List<ExportOrderAggrRoot> queryList(ExportMallOrderAggrQuery aggrQuery);

    long count(ExportMallOrderAggrQuery aggrQuery);
}
