package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.query;

import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.enums.ReceiveMethodEnum;
import lombok.Builder;
import lombok.Getter;


/**
 * <AUTHOR> Date: 2023/7/16 Time: 17:41
 */
@Getter
@Builder
public class ExportMallOrderAggrQuery {

    private Long id;
    private String ownerId;
    private String ownerUserId;
    private String exportInfoDigest;
    private ReceiveMethodEnum receiveMethod;

    private Integer offset;
    private Integer querySize;

    private String sortField;
    private boolean isDesc;

}
