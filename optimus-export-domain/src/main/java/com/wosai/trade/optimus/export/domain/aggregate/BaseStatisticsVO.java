package com.wosai.trade.optimus.export.domain.aggregate;

import com.wosai.general.util.ValidationUtils;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/8/7 Time: 09:35
 */
public abstract class BaseStatisticsVO<E> extends BaseVO<E> {


    public E accumulateStatistics(E vo) {
        if (Objects.isNull(vo)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.BIZ_PARAMS_MISSING);
        }
        ValidationUtils.ValidationResult result = ValidationUtils.validate(vo);
        if (result.isInvalid()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.ILLEGAL_ARGUMENT, result.getMsg());
        }
        E newVO = doAccumulateStatistics(vo);
        checkValid(newVO);
        return newVO;
    }

    protected abstract E doAccumulateStatistics(E vo);

}
