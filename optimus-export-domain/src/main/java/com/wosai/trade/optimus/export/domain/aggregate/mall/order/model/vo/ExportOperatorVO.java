package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo;

import com.wosai.trade.optimus.export.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR> Date: 2023/7/17 Time: 09:06
 */
@Getter
@Builder(toBuilder = true)
@Jacksonized
public class ExportOperatorVO extends BaseVO<ExportOperatorVO> {


    private String ownerId;
    private String ownerUserId;

    @Override
    protected ExportOperatorVO doReplaceNotNull(ExportOperatorVO vo) {
        return null;
    }
}
