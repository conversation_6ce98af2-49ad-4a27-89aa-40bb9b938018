package com.wosai.trade.optimus.export.domain.aggregate.event.model.enums;

import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;

import java.time.Period;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2022/12/29 Time: 1:48 PM
 */
public enum EventStateEnum {
    PENDING_MANUAL_PROCESSING((byte) -1, "待手动处理", null),
    PENDING((byte) 0, "待处理", null),
    PROCESSED((byte) 1, "已处理", Period.ofDays(2)),
    EXCEEDED_PROCESSING_LIMIT((byte) 2, "处理超限", Period.ofDays(5)),
    STAGE_COMPLETED((byte) 3, "阶段性处理完成", Period.ofDays(7)),
    PENDING_EXTERNAL_TRIGGER((byte) 4, "待外部触发", null),


    ;

    @Getter
    private final byte code;
    @Getter
    private final String desc;
    @Getter
    private final Period retention;

    EventStateEnum(byte code, String desc, Period retention) {
        this.code = code;
        this.desc = desc;
        this.retention = retention;
    }

    public static EventStateEnum ofCode(Byte code) {
        if (Objects.isNull(code)) {
            return null;
        }
        EventStateEnum[] enums = EventStateEnum.values();
        for (EventStateEnum stateEnums : enums) {
            if (stateEnums.getCode() == code) {
                return stateEnums;
            }
        }
        return null;
    }

    public static List<Pair<Byte, Period>> listClearableEventPairs() {
        return Arrays.stream(EventStateEnum.values())
                .filter(EventStateEnum::isClearable)
                .map(item -> Pair.of(item.getCode(), item.getRetention())).toList();
    }

    public boolean isClearable() {
        return Objects.nonNull(retention);
    }

}
