package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.util.FileUtils;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2023/7/19 Time: 09:26
 */
@Getter
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class MallOrderDetailView {

    private static final List<List<String>> FIXED_HEADERS = List.of(List.of("交易时间"), List.of("订单号")
            , List.of("支付流水号"), List.of("门店名")
            , List.of("商城名称"), List.of("订单金额")
            , List.of("优惠金额"), List.of("实收金额")
            , List.of("是否退款"), List.of("订单状态"), List.of("所售商品信息"));

    private static final String SUMMARY_TEMPLATE = "交易笔数汇总：%s笔，交易金额汇总：%s元，商户优惠金额汇总：%s元，实收金额汇总：%s元";
    private static final String SHEET_NAME_PREFIX_FORMATTER = "%s-%s";
    private static final String SHEET_NAME_SUFFIX_FORMATTER = "%s-%s";
    private static final int SHEET_NAME_PREFIX_MAX_LENGTH = 27;


    private final List<MallOrderDetailItemView> fixedColumnRowContents;
    private final Map<String/*mallSn*/, MallOrderDetailItemView> mallSnItemMap;

    private final Map<Integer, String> sceneFileHeaders;
    private final List<Map<Integer, String>> sceneFileContents;

    private final Map<Integer, String> floatFileHeaders;
    private final List<Map<Integer, String>> floatFileContents;

    private final Map<String, String> merchantSnNameMap;
    private final Map<String, String> storeSnNameMap;
    private final Map<String, String> mallSnNameMap;

    //
    private final long totalCount;
    private final long totalAmount;

    // 商城维度的汇总
    private final Map<String, Long> mallTotalCount;
    private final Map<String, Long> mallTotalAmount;
    private final Map<String, Long> mallDiscountTotalAmount;
    private final Map<String, Long> mallReceivedTotalAmount;
    private final Map<String, Map<String, BigDecimal>> mallProductQuantity;

    public static OrderFileViewBuilder builder() {
        return OrderFileViewBuilder.newDefaultInstance();
    }


    public static String genSheetName(String mallName, String storeName, Integer num) {
        String prefix = String.format(SHEET_NAME_PREFIX_FORMATTER, mallName, storeName);
        prefix = FileUtils.sanitizeSheetName(prefix);
        if (prefix.length() > SHEET_NAME_PREFIX_MAX_LENGTH) {
            prefix = prefix.substring(0, SHEET_NAME_PREFIX_MAX_LENGTH);
        }
        return String.format(SHEET_NAME_SUFFIX_FORMATTER, prefix, num);
    }

    public List<List<String>> genHeaderValues() {
        List<List<String>> headers = Lists.newArrayList();
        // 固定头
        headers.addAll(FIXED_HEADERS);

        // 场景头
        if (MapUtils.isNotEmpty(sceneFileHeaders)) {
            List<Integer> keys = sceneFileHeaders.keySet().stream().sorted(Integer::compare).toList();
            for (Integer key : keys) {
                headers.add(List.of(sceneFileHeaders.getOrDefault(key, StringUtils.EMPTY)));
            }
        }

        // 特殊浮动头
        if (MapUtils.isNotEmpty(floatFileHeaders)) {
            List<Integer> keys = floatFileHeaders.keySet().stream().sorted(Integer::compare).toList();
            for (Integer key : keys) {
                headers.add(List.of(floatFileHeaders.getOrDefault(key, StringUtils.EMPTY)));
            }
        }
        return headers;
    }


    public List<List<?>> genContentValues() {
        if (CollectionUtils.isEmpty(fixedColumnRowContents)) {
            return List.of();
        }

        boolean isExistSceneColumn = MapUtils.isNotEmpty(sceneFileHeaders);
        boolean isExistFloatColumn = MapUtils.isNotEmpty(floatFileHeaders);
        List<List<?>> result = Lists.newArrayList();
        for (int idx = 0; idx < fixedColumnRowContents.size(); idx++) {
            List<Object> currentRow = Lists.newArrayList();
            currentRow.addAll(fixedColumnRowContents.get(idx).genContentList());

            if (isExistSceneColumn) {
                Map<Integer, String> currentRowScene = sceneFileContents.get(idx);
                processNonFixedColumnContents(currentRow, currentRowScene, sceneFileHeaders);
            }

            if (isExistFloatColumn) {
                Map<Integer, String> currentRowFloat = floatFileContents.get(idx);
                processNonFixedColumnContents(currentRow, currentRowFloat, floatFileHeaders);
            }

            result.add(currentRow);
        }

        return result;
    }

    private void processNonFixedColumnContents(List<Object> currentRow
            , Map<Integer, String> currentRowData
            , Map<Integer, String> nonFixedHeaders) {
        List<Integer> sortedKeys = nonFixedHeaders.keySet().stream().sorted(Integer::compareTo).toList();
        for (Integer key : sortedKeys) {
            currentRow.add(currentRowData.getOrDefault(key, StringUtils.EMPTY));
        }
    }

    public boolean isEmptyView() {
        return totalCount == 0;
    }

    public String getMerchantSn(String mallSn) {
        return Optional.ofNullable(mallSnItemMap.get(mallSn)).map(MallOrderDetailItemView::getMerchantSn).orElse(null);
    }

    public String getStoreSn(String mallSn) {
        return Optional.ofNullable(mallSnItemMap.get(mallSn)).map(MallOrderDetailItemView::getStoreSn).orElse(null);
    }


    @Getter(AccessLevel.PRIVATE)
    @Setter(AccessLevel.PRIVATE)
    public static class OrderFileViewBuilder {
        private final List<MallOrderDetailItemView> fixedColumnRowContents;

        private Map<Integer, String> sceneFileHeaders;
        private List<Map<Integer, String>> sceneFileContents;

        private Map<Integer, String> floatFileHeaders;
        private List<Map<Integer, String>> floatFileContents;

        private Map<String, String> merchantSnNameMap;
        private Map<String, String> storeSnNameMap;
        private Map<String, String> mallSnNameMap;

        private long totalCount;
        private long totalAmount;

        private Map<String, Long> mallTotalCount;
        private Map<String, Long> mallTotalAmount;
        private Map<String, Long> mallDiscountTotalAmount;
        private Map<String, Long> mallReceivedTotalAmount;
        private Map<String, Map<String, BigDecimal>> mallProductQuantity;

        private OrderFileViewBuilder() {
            this.fixedColumnRowContents = Lists.newArrayList();
            this.sceneFileContents = Lists.newArrayList();
            this.floatFileContents = Lists.newArrayList();
            this.merchantSnNameMap = Maps.newHashMap();
            this.storeSnNameMap = Maps.newHashMap();
            this.mallSnNameMap = Maps.newHashMap();
            this.mallTotalCount = Maps.newHashMap();
            this.mallTotalAmount = Maps.newHashMap();
            this.mallDiscountTotalAmount = Maps.newHashMap();
            this.mallReceivedTotalAmount = Maps.newHashMap();
            this.mallProductQuantity = Maps.newHashMap();
        }

        private static OrderFileViewBuilder newDefaultInstance() {
            return new OrderFileViewBuilder();
        }

        public void sceneFileHeaders(Map<Integer, String> sceneFileHeaders) {
            this.sceneFileHeaders = sceneFileHeaders;
        }

        public void floatFileHeaders(Map<Integer, String> floatFileHeaders) {
            this.floatFileHeaders = floatFileHeaders;
        }

        public OrderFileViewBuilder item(MallOrderDetailItemView itemView) {
            totalCount++;
            totalAmount += itemView.getReceivedAmount();
            fixedColumnRowContents.add(itemView);
            merchantSnNameMap.put(itemView.getMerchantSn(), itemView.getMerchantName());
            storeSnNameMap.put(itemView.getStoreSn(), itemView.getStoreName());
            mallSnNameMap.put(itemView.getMallSn(), itemView.getMallName());
            mallTotalCount.merge(itemView.getMallSn(), 1L, Long::sum);
            mallTotalAmount.merge(itemView.getMallSn(), itemView.getOrderAmount(), Long::sum);
            mallDiscountTotalAmount.merge(itemView.getMallSn(), itemView.getDiscountAmount(), Long::sum);
            mallReceivedTotalAmount.merge(itemView.getMallSn(), itemView.getReceivedAmount(), Long::sum);

            Map<String, Map<String, BigDecimal>> mergedMap = new HashMap<>();
            mallProductQuantity.forEach((mallKey, productQuantityMap) -> {
                productQuantityMap.forEach((productKey, quantity) -> {
                    mergedMap.computeIfAbsent(mallKey, k -> new HashMap<>())
                            .merge(productKey, quantity, BigDecimal::add);
                });
            });

            mallProductQuantity.merge(itemView.getMallSn(), itemView.calculateSameProductQuantity(),
                    (existingQuantities, newQuantities) -> {
                        newQuantities.forEach((key, value) ->
                                existingQuantities.merge(key, value, BigDecimal::add));
                        return existingQuantities;
                    });
            return this;
        }

        public OrderFileViewBuilder sceneContentItem(Map<Integer, String> sceneContent) {
            if (Objects.nonNull(sceneContent)) {
                sceneFileContents.add(sceneContent);
            } else {
                sceneFileContents.add(Map.of());
            }
            return this;
        }

        public void floatContentItem(Map<Integer, String> floatContent) {
            if (Objects.nonNull(floatContent)) {
                floatFileContents.add(floatContent);
            } else {
                floatFileContents.add(Map.of());
            }
        }

        private boolean isAllContentEmpty() {
            return CollectionUtils.isEmpty(fixedColumnRowContents)
                    && CollectionUtils.isEmpty(sceneFileContents)
                    && CollectionUtils.isEmpty(floatFileContents);
        }

        private boolean isAllContentNotEmpty() {
            return CollectionUtils.isNotEmpty(fixedColumnRowContents)
                    && CollectionUtils.isNotEmpty(sceneFileContents)
                    && CollectionUtils.isNotEmpty(floatFileContents);
        }

        private boolean isAllContentSameSize() {
            return isAllContentEmpty() || (isAllContentNotEmpty()
                    && fixedColumnRowContents.size() == sceneFileContents.size()
                    && fixedColumnRowContents.size() == floatFileContents.size());
        }



        public MallOrderDetailView build() {
            if (!isAllContentSameSize()) {
                // 保证所有content的size相同，如果不容，则是缺少相应赋值方法的调用。
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.MISSING_PRE_OPERATION);
            }
            Map<String/*mallSn*/, MallOrderDetailItemView> mallSnItemMap = Map.of();
            if (CollectionUtils.isNotEmpty(fixedColumnRowContents)) {
                mallSnItemMap = fixedColumnRowContents.stream()
                        .collect(Collectors.toMap(MallOrderDetailItemView::getMallSn, Function.identity(), (k1, k2) -> k1));
            }

            return new MallOrderDetailView(fixedColumnRowContents, mallSnItemMap
                    , sceneFileHeaders, sceneFileContents
                    , floatFileHeaders, floatFileContents
                    , merchantSnNameMap, storeSnNameMap, mallSnNameMap
                    , totalCount, totalAmount
                    , mallTotalCount, mallTotalAmount
                    , mallDiscountTotalAmount, mallReceivedTotalAmount, mallProductQuantity);
        }

    }

}
