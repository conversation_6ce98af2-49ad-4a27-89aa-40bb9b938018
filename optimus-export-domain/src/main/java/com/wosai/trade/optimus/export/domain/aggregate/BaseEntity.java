package com.wosai.trade.optimus.export.domain.aggregate;


import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2022/11/21 Time: 6:28 下午
 */
@Getter
@Setter(AccessLevel.PROTECTED)
public abstract class BaseEntity {
    private List<ActionTypeEnum> actions = Lists.newArrayList();

    @NotNull(message = "实体ID不能为空")
    protected Long id;
    @NotNull(message = "实体创建时间不能为空")
    protected LocalDateTime ctime;
    @NotNull(message = "实体修改时间不能为空")
    protected LocalDateTime mtime;
    @NotNull(message = "实体版本号不能为空")
    protected Long version;


    protected abstract void setId(Long id);
    protected abstract void setCtime(LocalDateTime ctime);
    protected abstract void setMtime(LocalDateTime mtime);
    protected abstract void setVersion(Long version);

    public boolean isExist() {
        return Objects.nonNull(id);
    }

    public boolean isNotExist() {
        return !isExist();
    }

    public boolean isNeedAdd() {
        return actions.contains(ActionTypeEnum.ADD);
    }

    public boolean isNeedRemove() {
        return actions.contains(ActionTypeEnum.REMOVE);
    }

    public boolean isNeedModify() {
        return actions.contains(ActionTypeEnum.MODIFY)
                && !actions.contains(ActionTypeEnum.ADD)
                && !actions.contains(ActionTypeEnum.REMOVE);
    }

    public void clearMark() {
        actions.clear();
    }

    public String getIdStr() {
        return Long.toString(id);
    }


    protected void markForAdd() {
        actions.add(ActionTypeEnum.ADD);
    }

    protected void markForModify() {
        if (!actions.contains(ActionTypeEnum.MODIFY)) {
            actions.add(ActionTypeEnum.MODIFY);
            mtime = LocalDateTime.now();
        }
    }

    protected void markForRemove() {
        actions.add(ActionTypeEnum.REMOVE);
    }

    public enum ActionTypeEnum {
        NO_ACTION,
        ADD,
        REMOVE,
        MODIFY,

        ;
    }
}
