package com.wosai.trade.optimus.export.domain.aggregate;

import com.wosai.general.util.ValidationUtils;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2022/11/21 Time: 2:05 下午
 */
public abstract class BaseFactory {

    public abstract static class BaseBuilder<AggrRoot extends BaseEntity, CoreBuilder extends BaseCoreBuilder
            , OptionalBuilder extends BaseOptionalBuilder> {
        protected final AggrRoot aggrRoot;
        protected BaseBuilder(AggrRoot aggrRoot) {
            this.aggrRoot = aggrRoot;
        }

        public abstract  CoreBuilder coreBuilder();
        public abstract OptionalBuilder optionalBuilder();

        public AggrRoot build() {
            //可选项初始化
            optionalBuilder().initOptional();
            //初始化
            init();
            //校验聚合完整性
            checkParams();
            //标记为新增
            aggrRoot.markForAdd();
            //生成聚合根
            return generate();
        }

        public AggrRoot rebuild() {
            //校验参数
            checkParams();
            //生成聚合根
            return generate();
        }

        protected AggrRoot generate() {
            return aggrRoot;
        }
        protected void init() {}
        protected abstract void checkParams();

        protected void initBaseOptional() {
            LocalDateTime ctime = aggrRoot.getCtime();
            LocalDateTime mtime = aggrRoot.getMtime();
            Long version = aggrRoot.getVersion();
            LocalDateTime currentDateTime = LocalDateTime.now();
            if (Objects.isNull(ctime)) {
                aggrRoot.setCtime(currentDateTime);
            }
            if (Objects.isNull(mtime)) {
                aggrRoot.setMtime(currentDateTime);
            }
            if (Objects.isNull(version)) {
                aggrRoot.setVersion(0L);
            }
        }

        protected void checkBaseParams() {
            ValidationUtils.ValidationResult result = ValidationUtils.validate(aggrRoot);
            if (result.isInvalid()) {
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.ILLEGAL_ARGUMENT.getCode()
                        , result.getMsg());
            }
        }

    }

    public interface BaseCoreBuilder {}

    public interface BaseOptionalBuilder {
        void initOptional();
    }
}