package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view;

import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.trade.optimus.export.common.util.ExportNumberUtils;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2023/7/19 Time: 09:27
 */
@Getter
@Builder(toBuilder = true)
@Jacksonized
public class MallOrderDetailItemView {
    private static final String ITEM_NAME_FORMATTER = "%s-%sx%s%s";
    private static final WriteCellData<String> REFUNDED_TEXT = new WriteCellData<>("是");
    private static final String NOT_REFUNDED_TEXT = "否";
    static {
        WriteFont writeFont = new WriteFont();
        writeFont.setColor(IndexedColors.RED.getIndex());

        WriteCellStyle writeCellStyle = new WriteCellStyle();
        writeCellStyle.setWriteFont(writeFont);

        REFUNDED_TEXT.setWriteCellStyle(writeCellStyle);
    }

    private final String tradeTime;
    private final String orderSn;
    private final List<String> payTsnList;
    private final String merchantSn;
    private final String storeSn;
    private final String merchantName;
    private final String storeName;
    private final String mallSn;
    private final String mallName;
    private final long orderAmount;
    private final long discountAmount;
    private final long receivedAmount;
    private final boolean isReturned;
    private final String orderStateDesc;
    private final List<ExportOrderItem> orderItems;


    public List<Object> genContentList() {
        Object isRefundedString  = isReturned ? REFUNDED_TEXT : NOT_REFUNDED_TEXT;
        return Lists.newArrayList(tradeTime
                , orderSn
                , genFormatPayTsn()
                , storeName
                , mallName
                , ExportNumberUtils.centToYuan(orderAmount)
                , ExportNumberUtils.centToYuan(discountAmount)
                , ExportNumberUtils.centToYuan(receivedAmount)
                , isRefundedString
                , orderStateDesc
                , genFormatOrderItems());
    }

    private String genFormatPayTsn() {
        if (CollectionUtils.isEmpty(payTsnList)) {
            return StringUtils.EMPTY;
        }
        return String.join(",\n", payTsnList);
    }

    private String genFormatOrderItems() {
        if (CollectionUtils.isEmpty(orderItems)) {
            return StringUtils.EMPTY;
        }

        List<String> formatterItems = orderItems.stream()
                .map(item -> genItemNameString(item.getTitle(), item.getSkuDesc()
                        , item.getQuantity(), item.getUnit())).toList();

        return String.join(";\n", formatterItems);
    }

    public String genItemNameString(String title, String skuDesc, String quantity, String unit) {
        return String.format(ITEM_NAME_FORMATTER
                , Optional.of(title)
                        .orElse(StringUtils.EMPTY)
                , Optional.ofNullable(skuDesc)
                        .orElse(StringUtils.EMPTY)
                , Optional.ofNullable(quantity)
                        .orElse(StringUtils.EMPTY)
                , Optional.ofNullable(unit)
                        .orElse(StringUtils.EMPTY));
    }

    public Map<String, BigDecimal> calculateSameProductQuantity() {
        if (CollectionUtils.isEmpty(orderItems)) {
            return Maps.newHashMap();
        }
        return orderItems.stream()
                .collect(Collectors.groupingBy(ExportOrderItem::getTitle,
                        Collectors.mapping(item -> new BigDecimal(item.getQuantity())
                                , Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }

    @Getter
    @Builder
    public static class ExportOrderItem {
        private final String title;
        private final String skuDesc;
        private final String quantity;
        private final String unit;
    }

}
