package com.wosai.trade.optimus.export.domain.aggregate.event.model;

import com.wosai.general.util.ValidationUtils;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.BaseFactory;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventStateEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.vo.DelayRuleVO;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.vo.EventContentVO;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.vo.EventExtVO;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/6/16 Time: 6:53 PM
 */
public class EventAggrRootFactory extends BaseFactory {
    public static EventAggrRootBuilder builder() {
        return new EventAggrRootBuilder(EventAggrRoot.newEmptyInstance());
    }


    public static class EventAggrRootBuilder extends BaseBuilder<EventAggrRoot, EventAggrRootCoreBuilder, EventAggrRootOptionalBuilder> {
        private EventAggrRootCoreBuilder eventAggrRootCoreBuilder;
        private EventAggrRootOptionalBuilder eventAggrRootOptionalBuilder;

        protected EventAggrRootBuilder(EventAggrRoot eventAggrRoot) {
            super(eventAggrRoot);
        }

        @Override
        public EventAggrRootCoreBuilder coreBuilder() {
            if (Objects.isNull(eventAggrRootCoreBuilder)) {
                eventAggrRootCoreBuilder = new EventAggrRootCoreBuilder(aggrRoot);
            }
            return eventAggrRootCoreBuilder;
        }

        @Override
        public EventAggrRootOptionalBuilder optionalBuilder() {
            if (Objects.isNull(eventAggrRootOptionalBuilder)) {
                eventAggrRootOptionalBuilder = new EventAggrRootOptionalBuilder(aggrRoot);
            }
            return eventAggrRootOptionalBuilder;
        }

        @Override
        protected void checkParams() {
            ValidationUtils.ValidationResult result = ValidationUtils.validate(aggrRoot);
            if (result.isInvalid()) {
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.ILLEGAL_ARGUMENT
                        , result.getMsg());
            }
        }
    }

    public static class EventAggrRootCoreBuilder extends EventAggrRootBuilder implements BaseCoreBuilder {

        protected EventAggrRootCoreBuilder(EventAggrRoot eventAggrRoot) {
            super(eventAggrRoot);
        }

        public EventAggrRootCoreBuilder id(Long id) {
            aggrRoot.setId(id);
            return this;
        }

        public EventAggrRootCoreBuilder type(EventTypeEnum type) {
            aggrRoot.setType(type);
            return this;
        }

        public EventAggrRootCoreBuilder associatedSn(String associatedSn) {
            aggrRoot.setAssociatedSn(associatedSn);
            return this;
        }


    }

    public static class EventAggrRootOptionalBuilder extends EventAggrRootBuilder implements BaseOptionalBuilder {

        protected EventAggrRootOptionalBuilder(EventAggrRoot eventAggrRoot) {
            super(eventAggrRoot);
        }

        public EventAggrRootOptionalBuilder state(EventStateEnum state) {
            aggrRoot.setState(state);
            return this;
        }

        public EventAggrRootOptionalBuilder result(String result) {
            aggrRoot.setResult(result);
            return this;
        }

        public EventAggrRootOptionalBuilder content(EventContentVO content) {
            aggrRoot.setContent(content);
            return this;
        }

        public EventAggrRootOptionalBuilder delayRule(DelayRuleVO delayRule) {
            aggrRoot.setDelayRule(delayRule);
            return this;
        }

        public EventAggrRootOptionalBuilder nextProcessTime(LocalDateTime nextProcessTime) {
            aggrRoot.setNextProcessTime(nextProcessTime);
            return this;
        }

        public EventAggrRootOptionalBuilder ext(EventExtVO ext) {
            aggrRoot.setExt(ext);
            return this;
        }

        public EventAggrRootOptionalBuilder ctime(LocalDateTime ctime) {
            aggrRoot.setCtime(ctime);
            return this;
        }

        public EventAggrRootOptionalBuilder mtime(LocalDateTime mtime) {
            aggrRoot.setMtime(mtime);
            return this;
        }

        public EventAggrRootOptionalBuilder version(Long version) {
            aggrRoot.setVersion(version);
            return this;
        }

        @Override
        public void initOptional() {
            EventStateEnum state = aggrRoot.getState();
            String result = aggrRoot.getResult();
            EventContentVO eventContentVO = aggrRoot.getContent();
            DelayRuleVO delayRuleVO = aggrRoot.getDelayRule();
            LocalDateTime nextProcessTime = aggrRoot.getNextProcessTime();
            EventExtVO eventExtVO = aggrRoot.getExt();
            LocalDateTime ctime = aggrRoot.getCtime();
            LocalDateTime mtime = aggrRoot.getMtime();
            Long version = aggrRoot.getVersion();

            LocalDateTime currentDateTime = LocalDateTime.now();
            if (Objects.isNull(state)) {
                aggrRoot.setState(EventStateEnum.PENDING);
            }
            if (Objects.isNull(result)) {
                aggrRoot.setResult(StringUtils.EMPTY);
            }
            if (Objects.isNull(eventContentVO)) {
                aggrRoot.setContent(EventContentVO.newEmptyInstance());
            }
            if (Objects.isNull(delayRuleVO)) {
                aggrRoot.setDelayRule(DelayRuleVO.newDefaultInstance());
            }
            if (Objects.isNull(nextProcessTime)) {
                aggrRoot.setNextProcessTime(currentDateTime);
            }
            if (Objects.isNull(eventExtVO)) {
                aggrRoot.setExt(EventExtVO.newEmptyInstance());
            }
            if (Objects.isNull(ctime)) {
                aggrRoot.setCtime(currentDateTime);
            }
            if (Objects.isNull(mtime)) {
                aggrRoot.setMtime(currentDateTime);
            }
            if (Objects.isNull(version)) {
                aggrRoot.setVersion(0L);
            }

        }
    }
}
