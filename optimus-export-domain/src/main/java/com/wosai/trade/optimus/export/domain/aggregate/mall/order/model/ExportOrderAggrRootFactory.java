package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model;

import com.wosai.general.util.ValidationUtils;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.BaseFactory;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.enums.ExportStateEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportInfoVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportOperatorVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportResultVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.OrderExportExtVO;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/7/16 Time: 17:41
 */
public class ExportOrderAggrRootFactory extends BaseFactory {


    public static ExportMallOrderAggrRootBuilder builder() {
        return new ExportMallOrderAggrRootBuilder(new ExportOrderAggrRoot());
    }




    public static class ExportMallOrderAggrRootBuilder extends BaseBuilder<ExportOrderAggrRoot, ExportMallOrderAggrRootCoreBuilder, ExportMallOrderAggrRootOptionalBuilder> {
        private ExportMallOrderAggrRootCoreBuilder mallExportOrderAggrRootCoreBuilder;
        private ExportMallOrderAggrRootOptionalBuilder mallExportOrderAggrRootOptionalBuilder;



        protected ExportMallOrderAggrRootBuilder(ExportOrderAggrRoot exportOrderAggrRoot) {
            super(exportOrderAggrRoot);
        }

        @Override
        public ExportMallOrderAggrRootCoreBuilder coreBuilder() {
            if (Objects.isNull(mallExportOrderAggrRootCoreBuilder)) {
                mallExportOrderAggrRootCoreBuilder = new ExportMallOrderAggrRootCoreBuilder(aggrRoot);
            }
            return mallExportOrderAggrRootCoreBuilder;
        }

        @Override
        public ExportMallOrderAggrRootOptionalBuilder optionalBuilder() {
            if (Objects.isNull(mallExportOrderAggrRootOptionalBuilder)) {
                mallExportOrderAggrRootOptionalBuilder = new ExportMallOrderAggrRootOptionalBuilder(aggrRoot);
            }
            return mallExportOrderAggrRootOptionalBuilder;
        }

        @Override
        protected void init() {
            aggrRoot.getExportInfo().init();
        }

        @Override
        protected void checkParams() {
            ValidationUtils.ValidationResult result = ValidationUtils.validate(aggrRoot);
            if (result.isInvalid()) {
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.ILLEGAL_ARGUMENT, result.getMsg());
            }
        }
    }



    public static class ExportMallOrderAggrRootCoreBuilder extends ExportMallOrderAggrRootBuilder implements BaseCoreBuilder {

        protected ExportMallOrderAggrRootCoreBuilder(ExportOrderAggrRoot exportOrderAggrRoot) {
            super(exportOrderAggrRoot);
        }


        public ExportMallOrderAggrRootCoreBuilder id(Long id) {
            aggrRoot.setId(id);
            return this;
        }

        public ExportMallOrderAggrRootCoreBuilder state(ExportStateEnum state) {
            aggrRoot.setState(state);
            return this;
        }

        public ExportMallOrderAggrRootCoreBuilder operator(ExportOperatorVO operator) {
            aggrRoot.setOperator(operator);
            return this;
        }

        public ExportMallOrderAggrRootCoreBuilder exportInfo(ExportInfoVO exportInfo) {
            aggrRoot.setExportInfo(exportInfo);
            return this;
        }


    }

    public static class ExportMallOrderAggrRootOptionalBuilder extends ExportMallOrderAggrRootBuilder implements BaseOptionalBuilder {

        protected ExportMallOrderAggrRootOptionalBuilder(ExportOrderAggrRoot exportOrderAggrRoot) {
            super(exportOrderAggrRoot);
        }

        public ExportMallOrderAggrRootOptionalBuilder exportResult(ExportResultVO exportResult) {
            aggrRoot.setExportResult(exportResult);
            return this;
        }

        public ExportMallOrderAggrRootOptionalBuilder ext(OrderExportExtVO ext) {
            aggrRoot.setExt(ext);
            return this;
        }

        public ExportMallOrderAggrRootOptionalBuilder expireAt(LocalDateTime expireAt) {
            aggrRoot.setExpiredAt(expireAt);
            return this;
        }

        public ExportMallOrderAggrRootOptionalBuilder ctime(LocalDateTime ctime) {
            aggrRoot.setCtime(ctime);
            return this;
        }

        public ExportMallOrderAggrRootOptionalBuilder mtime(LocalDateTime mtime) {
            aggrRoot.setMtime(mtime);
            return this;
        }

        public ExportMallOrderAggrRootOptionalBuilder version(Long version) {
            aggrRoot.setVersion(version);
            return this;
        }


        @Override
        public void initOptional() {
            ExportResultVO exportResult = aggrRoot.getExportResult();
            OrderExportExtVO ext = aggrRoot.getExt();
            LocalDateTime expireAt = aggrRoot.getExpiredAt();
            LocalDateTime ctime = aggrRoot.getCtime();
            LocalDateTime mtime = aggrRoot.getMtime();
            Long version = aggrRoot.getVersion();
            LocalDateTime currentDateTime = LocalDateTime.now();
            if (Objects.isNull(exportResult)) {
                aggrRoot.setExportResult(ExportResultVO.newEmptyInstance());
            }
            if (Objects.isNull(ext)) {
                aggrRoot.setExt(OrderExportExtVO.newEmptyInstance());
            }
            if (Objects.isNull(expireAt)) {
                aggrRoot.setExpiredAt(currentDateTime.plusDays(7));
            }
            if (Objects.isNull(ctime)) {
                aggrRoot.setCtime(currentDateTime);
            }
            if (Objects.isNull(mtime)) {
                aggrRoot.setMtime(currentDateTime);
            }
            if (Objects.isNull(version)) {
                aggrRoot.setVersion(0L);
            }
        }
    }
}
