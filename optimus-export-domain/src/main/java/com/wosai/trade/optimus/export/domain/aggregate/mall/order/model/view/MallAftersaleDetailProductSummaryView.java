package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view;

import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.Builder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Date: 2024/8/12 Time: 11:24
 */
@Builder(access = AccessLevel.PRIVATE)
public class MallAftersaleDetailProductSummaryView {
    private static final MallAftersaleDetailProductSummaryView DEFAULT_EMPTY_INSTANCE
            = MallAftersaleDetailProductSummaryView.builder().build();
    private static final List<List<String>> HEADERS = List.of(List.of("商品名称"), List.of("数量"));

    private final List<List<?>> contents;

    public static MallAftersaleDetailProductSummaryView newInstance(Map<String, BigDecimal> rowData) {
        if (MapUtils.isEmpty(rowData)) {
            return DEFAULT_EMPTY_INSTANCE;
        }
        List<List<?>> contents = Lists.newArrayListWithCapacity(rowData.size());
        rowData.forEach((k, v) -> {
            contents.add(List.of(k, v.setScale(2, RoundingMode.HALF_UP).toString()));
        });
        return MallAftersaleDetailProductSummaryView.builder().contents(contents).build();
    }

    public static List<List<String>> getHeaders() {
        return HEADERS;
    }

    public boolean isEmpty() {
        return this == DEFAULT_EMPTY_INSTANCE || CollectionUtils.isEmpty(contents);
    }

    public boolean isNotEmpty() {
        return !isEmpty();
    }

    public List<List<?>> getContents() {
        if (isEmpty()) {
            return List.of();
        }
        return contents;
    }

}
