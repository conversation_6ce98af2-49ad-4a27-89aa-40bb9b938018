package com.wosai.trade.optimus.export.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR> Date: 2022/12/29 Time: 11:39 AM
 */
@Getter
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventExtVO extends BaseVO<EventExtVO> {

    private final String mqKey;

    public static EventExtVO newEmptyInstance() {
        return EventExtVO.builder().build();
    }

    @Override
    protected EventExtVO doReplaceNotNull(EventExtVO vo) {
        throw new OptimusExportBizException(OptimusExportRespCodeEnum.UNSUPPORTED_OPERATION_EXCEPTION);
    }
}
