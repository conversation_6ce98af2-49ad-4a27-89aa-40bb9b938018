package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.enums;

import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> Date: 2023/7/17 Time: 09:06
 */
@Getter
public enum ExportStateEnum {

    IN_GENERATING((byte) 0, "生成中", "生成中"),
    GENERATE_SUCCESS((byte) 1, "生成成功", "生成成功"),
    GENERATE_FAILURE((byte) 2, "生成失败", "生成失败"),
    SEND_SUCCESS((byte) 3, "发送成功", "发送成功"),
    SEND_FAILURE((byte) 4, "发送失败", "发送失败"),
    EXPIRED((byte) 5, "已过期", ""),

    ;

    private static final Set<ExportStateEnum> GENERATE_SUCCESS_SET
            = Sets.newHashSet(GENERATE_SUCCESS, SEND_SUCCESS, SEND_FAILURE);

    private final byte code;
    private final String desc;
    private final String showDesc;

    ExportStateEnum(byte code, String desc, String showDesc) {
        this.code = code;
        this.desc = desc;
        this.showDesc = showDesc;
    }

    public static ExportStateEnum ofCode(Byte code) {
        if (Objects.isNull(code)) {
            return null;
        }
        ExportStateEnum[] enums = ExportStateEnum.values();
        for (ExportStateEnum anEnum : enums) {
            if (anEnum.getCode() == code) {
                return anEnum;
            }
        }

        return null;
    }

    public boolean isGenerateSuccess() {
        return GENERATE_SUCCESS_SET.contains(this);
    }

}
