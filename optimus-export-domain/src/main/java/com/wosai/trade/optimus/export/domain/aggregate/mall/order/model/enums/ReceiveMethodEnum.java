package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/7/17 Time: 20:47
 */
public enum ReceiveMethodEnum {

    EMAIL((byte) 0, "邮箱"),
    LINK((byte) 1, "链接"),

    TASK_CENTER((byte) 2, "下载中心"),

    ;


    private final byte code;
    @Getter
    private final String desc;


    ReceiveMethodEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static ReceiveMethodEnum ofCode(Byte code) {
        if (Objects.isNull(code)) {
            return null;
        }
        ReceiveMethodEnum[] enums = ReceiveMethodEnum.values();
        for (ReceiveMethodEnum anEnum : enums) {
            if (anEnum.getCode() == code) {
                return anEnum;
            }
        }
        return null;
    }


    @JsonValue
    public byte getCode() {
        return code;
    }



}
