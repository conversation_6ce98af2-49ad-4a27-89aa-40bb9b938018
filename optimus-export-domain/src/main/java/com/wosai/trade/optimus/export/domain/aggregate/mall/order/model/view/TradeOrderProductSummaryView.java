package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view;

import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR> Date: 2024/8/6 Time: 16:52
 */
@Builder(access = AccessLevel.PRIVATE)
public class TradeOrderProductSummaryView {
    private static final TradeOrderProductSummaryView DEFAULT_EMPTY_INSTANCE = TradeOrderProductSummaryView.builder().build();
    private static final List<List<String>> HEADERS = List.of(List.of("商品销售汇总", "商品名称"), List.of("商品销售汇总", "数量"));
    private static final List<String> TAIL_LINE_SUMMARY_PREFIX = List.of("汇总");

    private final List<List<?>> contents;

    public static TradeOrderProductSummaryView newInstance(List<TradeOrderProductSummaryItem> rowData) {
        if (CollectionUtils.isEmpty(rowData)) {
            return DEFAULT_EMPTY_INSTANCE;
        }

        List<List<?>> contents = Lists.newArrayListWithCapacity(rowData.size());

        BigDecimal totalQuantity = new BigDecimal("0");
        for (TradeOrderProductSummaryItem summaryItem : rowData) {
            totalQuantity = totalQuantity.add(summaryItem.getProductQuantity());

            contents.add(List.of(summaryItem.getProductName()
                    , summaryItem.getQuantityStringValue()));
        }

        List<Object> tailLine = Lists.newArrayList(TAIL_LINE_SUMMARY_PREFIX);
        tailLine.add(totalQuantity.setScale(2, RoundingMode.HALF_UP).toString());

        contents.add(tailLine);

        return TradeOrderProductSummaryView.builder()
                .contents(contents)
                .build();
    }

    public static List<List<String>> getHeaders() {
        return HEADERS;
    }

    public boolean isEmpty() {
        return this == DEFAULT_EMPTY_INSTANCE || CollectionUtils.isEmpty(contents);
    }

    public List<List<?>> getContents() {
        if (isEmpty()) {
            return List.of();
        }
        return contents;
    }


    @Getter
    @Builder
    public static class TradeOrderProductSummaryItem {
        private final String productName;
        private final BigDecimal productQuantity;

        public String getQuantityStringValue() {
            return productQuantity.setScale(2, RoundingMode.HALF_UP).toString();
        }
    }
}
