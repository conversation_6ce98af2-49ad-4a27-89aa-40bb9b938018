package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view;

import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.merge.OnceAbsoluteMergeStrategy;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.trade.optimus.export.common.util.ExportNumberUtils;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR> Date: 2024/8/6 Time: 11:24
 */
@Getter
@Builder(toBuilder = true)
@Jacksonized
public class TradeDataSummaryView {
    private static final long MAX_SUMMARY_SIZE = 10000 * 10L;
//    private static final long MAX_SUMMARY_SIZE = 15L;
    private static final WriteCellData<String> DESCRIBE_ROW_1 = new WriteCellData<>("以下为订单汇总数据，如需查看每笔订单详情，请选择下方其他工作表");
    private static final String DESCRIBE_ROW_2_FORMATTER = "起止时间：%s 至 %s";
    private static final String DESCRIBE_ROW_3 = "单次最大导出10万条数据，当次已超限制，请分批导出";
    private static final String SHEET_NAME = "汇总数据";
    private static final String MALL_ORDER_DETAIL_SUMMARY_TEMPLATE = "交易笔数汇总：%s笔，交易金额汇总：%s元，商户优惠金额汇总：%s元，实收金额汇总：%s";
    private static final String MALL_TICKET_DETAIL_SUMMARY_TEMPLATE = "退款笔数汇总：%s笔，退款金额汇总：%s元";
    private static final List<OnceAbsoluteMergeStrategy> NOT_LIMITED_OF_DATA_STRATEGIES = List.of(
            new OnceAbsoluteMergeStrategy(0, 0, 0, 6)
            , new OnceAbsoluteMergeStrategy(1, 1, 0, 6));
    private static final List<OnceAbsoluteMergeStrategy> LIMITED_OF_DATA_STRATEGIES = List.of(
            new OnceAbsoluteMergeStrategy(0, 0, 0, 6)
            , new OnceAbsoluteMergeStrategy(1, 1, 0, 6)
            , new OnceAbsoluteMergeStrategy(2, 2, 0, 6));
    private static final DateTimeFormatter EXPORT_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    static {
        WriteCellStyle writeCellStyle = new WriteCellStyle();
        DESCRIBE_ROW_1.setWriteCellStyle(writeCellStyle);

        WriteFont writeFont = new WriteFont();
        writeFont.setColor(IndexedColors.RED.getIndex());
        writeCellStyle.setWriteFont(writeFont);
    }

    private final LocalDateTime exportBeginDateTime;
    private final LocalDateTime exportEndDateTime;

    private long totalOrderSize;
    private long totalTicketSize;
    private Map<String, MallSummary> mallOrderSummaryMap;
    private Map<String, MallSummary> mallTicketSummaryMap;
    private Map<String, String> merchantSnNameMap;
    private Map<String, String> storeSnNameMap;
    private Map<String, String> mallSnNameMap;

    private MallOrderVolumeSummaryView mallOrderVolumeSummaryView;
    private MallOrderProductSummaryView mallOrderProductSummaryView;
    private TradeOrderProductSummaryView tradeOrderProductSummaryView;

    private MallAftersaleVolumeSummaryView mallAftersaleVolumeSummaryView;
    private MallAftersaleProductSummaryView mallAftersaleProductSummaryView;
    private TradeAftersaleProductSummaryView tradeAftersaleProductSummaryView;


    public static TradeDataSummaryView create(LocalDateTime exportBeginDateTime, LocalDateTime exportEndDateTime) {
        return TradeDataSummaryView.builder()
                .exportBeginDateTime(exportBeginDateTime)
                .exportEndDateTime(exportEndDateTime)
                .totalOrderSize(0L)
                .mallOrderSummaryMap(Maps.newLinkedHashMap())
                .mallTicketSummaryMap(Maps.newLinkedHashMap())
                .merchantSnNameMap(Maps.newHashMap())
                .storeSnNameMap(Maps.newHashMap())
                .mallSnNameMap(Maps.newHashMap())
                .build();
    }

    public static String getSheetName() {
        return SHEET_NAME;
    }

    public void calculateOrderSummary(MallOrderDetailView mallOrderDetailView) {
        if (mallOrderDetailView.isEmptyView()) {
            return;
        }

        totalOrderSize += mallOrderDetailView.getTotalCount();

        sumMallSummary(mallOrderDetailView);
    }

    public void calculateTicketSummary(MallTicketDetailView mallTicketDetailView) {
        if (mallTicketDetailView.isEmptyView()) {
            return;
        }

        totalTicketSize += mallTicketDetailView.getTotalCount();

        sumMallSummary(mallTicketDetailView);
    }

    public boolean isReachedSummaryLimit() {
//        return totalOrderSize > MAX_SUMMARY_SIZE;
        return totalOrderSize + totalTicketSize > MAX_SUMMARY_SIZE;
    }

    public List<List<?>> genSummaryHeaderRow() {
        String describeRow2 = String.format(DESCRIBE_ROW_2_FORMATTER
                , exportBeginDateTime.format(EXPORT_DATE_TIME_FORMATTER)
                , exportEndDateTime.format(EXPORT_DATE_TIME_FORMATTER));

        if (isReachedSummaryLimit()) {
            return List.of(List.of(DESCRIBE_ROW_1), List.of(describeRow2), List.of(DESCRIBE_ROW_3));
        }
        return List.of(List.of(DESCRIBE_ROW_1), List.of(describeRow2));
    }

    public List<? extends WriteHandler> getHomepageWriteHandler() {
        return isReachedSummaryLimit() ? NOT_LIMITED_OF_DATA_STRATEGIES : LIMITED_OF_DATA_STRATEGIES;
    }

    public void statisticsSummary() {
        List<MallOrderVolumeSummaryView.MallOrderVolumeSummaryItem> mallOrderVolumeItems = Lists.newArrayList();
        List<MallOrderProductSummaryView.MallOrderProductSummaryItem> mallOrderProductItems = Lists.newArrayList();
        List<TradeOrderProductSummaryView.TradeOrderProductSummaryItem> tradeOrderProductItems = Lists.newArrayList();

        if (MapUtils.isEmpty(mallOrderSummaryMap)) {
            return;
        }

        // 订单同名商品汇总
        Map<String, BigDecimal> orderProductSummary = Maps.newHashMap();

        mallOrderSummaryMap.forEach((k, v) -> {
            String merchantSn = v.getMerchantSn();
            String storeSn = v.getStoreSn();

            // 生成商城订单交易金额笔数汇总
            MallOrderVolumeSummaryView.MallOrderVolumeSummaryItem mallOrderVolumeSummaryItem = MallOrderVolumeSummaryView
                    .MallOrderVolumeSummaryItem.builder()
                    .merchantName(merchantSnNameMap.get(merchantSn))
                    .merchantSn(merchantSn)
                    .storeName(storeSnNameMap.get(storeSn))
                    .storeSn(storeSn)
                    .mallName(mallSnNameMap.get(k))
                    .mallTotalCount(v.getTotalTradeCount())
                    .mallTotalAmount(v.getTotalTradeAmount())
                    .mallDiscountTotalAmount(v.getTotalTradeDiscountAmount())
                    .mallReceiveTotalAmount(v.getTotalTradeReceivedAmount())
                    .build();
            mallOrderVolumeItems.add(mallOrderVolumeSummaryItem);

            // 生成商城订单商品汇总
            Map<String, BigDecimal> totalSameProductMap = v.getTotalSameProduct();
            if (MapUtils.isNotEmpty(totalSameProductMap)) {
                totalSameProductMap.forEach((productName, quantity) -> {
                    MallOrderProductSummaryView.MallOrderProductSummaryItem mallOrderProductSummaryItem = MallOrderProductSummaryView
                            .MallOrderProductSummaryItem.builder()
                            .merchantName(merchantSnNameMap.get(merchantSn))
                            .merchantSn(merchantSn)
                            .storeName(storeSnNameMap.get(storeSn))
                            .storeSn(storeSn)
                            .mallName(mallSnNameMap.get(k))
                            .productName(productName)
                            .productQuantity(quantity)
                            .build();

                    // 全量同名商品数量汇总累加
                    orderProductSummary.merge(productName, quantity, BigDecimal::add);

                    mallOrderProductItems.add(mallOrderProductSummaryItem);
                });
            }

        });

        if (MapUtils.isNotEmpty(orderProductSummary)) {
            orderProductSummary.forEach((k, v) ->
                    tradeOrderProductItems.add(TradeOrderProductSummaryView.TradeOrderProductSummaryItem.builder()
                            .productName(k)
                            .productQuantity(v)
                            .build()));
        }

        if (CollectionUtils.isNotEmpty(mallOrderVolumeItems)) {
            this.mallOrderVolumeSummaryView = MallOrderVolumeSummaryView.newInstance(mallOrderVolumeItems);
        }

        if (CollectionUtils.isNotEmpty(mallOrderProductItems)) {
            this.mallOrderProductSummaryView = MallOrderProductSummaryView.newInstance(mallOrderProductItems);
        }

        if (CollectionUtils.isNotEmpty(tradeOrderProductItems)) {
            this.tradeOrderProductSummaryView = TradeOrderProductSummaryView.newInstance(tradeOrderProductItems);
        }

    }

    public void statisticsAftersaleSummary() {
        List<MallAftersaleVolumeSummaryView.MallAftersaleVolumeSummaryItem> mallAftersaleVolumeItems = Lists.newArrayList();
        List<MallAftersaleProductSummaryView.MallAftersaleProductSummaryItem> mallAftersaleProductItems = Lists.newArrayList();
        List<TradeAftersaleProductSummaryView.TradeAftersaleProductSummaryItem> tradeAftersaleProductItems = Lists.newArrayList();

        if (MapUtils.isEmpty(mallTicketSummaryMap)) {
            return;
        }

        // 售后单同名商品汇总
        Map<String, BigDecimal> aftersaleProductSummary = Maps.newHashMap();

        mallTicketSummaryMap.forEach((k, v) -> {
            String merchantSn = v.getMerchantSn();
            String storeSn = v.getStoreSn();

            // 生成商城售后单交易金额笔数汇总
            MallAftersaleVolumeSummaryView.MallAftersaleVolumeSummaryItem mallAftersaleVolumeSummaryItem = MallAftersaleVolumeSummaryView
                    .MallAftersaleVolumeSummaryItem.builder()
                    .merchantName(merchantSnNameMap.get(merchantSn))
                    .merchantSn(merchantSn)
                    .storeName(storeSnNameMap.get(storeSn))
                    .storeSn(storeSn)
                    .mallName(mallSnNameMap.get(k))
                    .mallAftersaleTotalCount(v.getTotalAftersaleCount())
                    .mallRefundTotalCount(v.getTotalRefundCount())
                    .mallRefundTotalAmount(v.getTotalRefundAmount())
                    .build();
            mallAftersaleVolumeItems.add(mallAftersaleVolumeSummaryItem);

            // 生成商城售后单商品汇总
            Map<String, BigDecimal> totalSameProductMap = v.getTotalSameProduct();
            if (MapUtils.isNotEmpty(totalSameProductMap)) {
                totalSameProductMap.forEach((productName, quantity) -> {
                    MallAftersaleProductSummaryView.MallAftersaleProductSummaryItem mallAftersaleProductSummaryItem = MallAftersaleProductSummaryView
                            .MallAftersaleProductSummaryItem.builder()
                            .merchantName(merchantSnNameMap.get(merchantSn))
                            .merchantSn(merchantSn)
                            .storeName(storeSnNameMap.get(storeSn))
                            .storeSn(storeSn)
                            .mallName(mallSnNameMap.get(k))
                            .productName(productName)
                            .productQuantity(quantity)
                            .build();

                    // 全量同名商品数量汇总累加
                    aftersaleProductSummary.merge(productName, quantity, BigDecimal::add);

                    mallAftersaleProductItems.add(mallAftersaleProductSummaryItem);
                });
            }

        });

        if (MapUtils.isNotEmpty(aftersaleProductSummary)) {
            aftersaleProductSummary.forEach((k, v) ->
                    tradeAftersaleProductItems.add(TradeAftersaleProductSummaryView.TradeAftersaleProductSummaryItem.builder()
                            .productName(k)
                            .productQuantity(v)
                            .build()));
        }

        if (CollectionUtils.isNotEmpty(mallAftersaleVolumeItems)) {
            this.mallAftersaleVolumeSummaryView = MallAftersaleVolumeSummaryView.newInstance(mallAftersaleVolumeItems);
        }

        if (CollectionUtils.isNotEmpty(mallAftersaleProductItems)) {
            this.mallAftersaleProductSummaryView = MallAftersaleProductSummaryView.newInstance(mallAftersaleProductItems);
        }

        if (CollectionUtils.isNotEmpty(tradeAftersaleProductItems)) {
            this.tradeAftersaleProductSummaryView = TradeAftersaleProductSummaryView.newInstance(tradeAftersaleProductItems);
        }
    }

    public boolean isExistMallOrderVolumeSummary() {
        return Objects.nonNull(mallOrderVolumeSummaryView);
    }

    public boolean isExistMallAftersaleVolumeSummary() {
        return Objects.nonNull(mallAftersaleVolumeSummaryView);
    }

    public boolean isExistMallOrderProductSummary() {
        return Objects.nonNull(mallOrderProductSummaryView);
    }

    public boolean isExistMallAftersaleProductSummary() {
        return Objects.nonNull(mallAftersaleProductSummaryView);
    }

    public boolean isExistTradeOrderProductSummary() {
        return Objects.nonNull(tradeOrderProductSummaryView);
    }

    public boolean isExistTradeAftersaleProductSummary() {
        return Objects.nonNull(tradeAftersaleProductSummaryView);
    }


    private void sumMallSummary(MallOrderDetailView mallOrderDetailView) {
        // 本次商城集合
        Set<String> mallSnSet = mallOrderDetailView.getMallSnNameMap().keySet();

        // 基于商城的统计数据
        Map<String, Long> mallTotalCount = mallOrderDetailView.getMallTotalCount();
        Map<String, Long> mallTotalAmount = mallOrderDetailView.getMallTotalAmount();
        Map<String, Long> mallDiscountTotalAmount = mallOrderDetailView.getMallDiscountTotalAmount();
        Map<String, Long> mallReceivedTotalAmount = mallOrderDetailView.getMallReceivedTotalAmount();
        Map<String, Map<String, BigDecimal>> mallProductQuantity = mallOrderDetailView.getMallProductQuantity();

        // 和已存在的累加
        for (String mallSn : mallSnSet) {
            MallSummary mallSummary = mallOrderSummaryMap.get(mallSn);
            if (Objects.isNull(mallSummary)) {
                mallSummary = MallSummary.newDefaultInstance(mallOrderDetailView.getMerchantSn(mallSn)
                        , mallOrderDetailView.getStoreSn(mallSn));
                mallOrderSummaryMap.put(mallSn, mallSummary);
            }

            mallSummary.sumTradeCount(mallTotalCount.getOrDefault(mallSn, 0L))
                    .sumTradeAmount(mallTotalAmount.getOrDefault(mallSn, 0L))
                    .sumSameProduct(mallProductQuantity.getOrDefault(mallSn, Map.of()))
                    .sumTradeDiscountAmount(mallDiscountTotalAmount.getOrDefault(mallSn, 0L))
                    .sumTradeReceivedAmount(mallReceivedTotalAmount.getOrDefault(mallSn, 0L))
                    .over();

        }

        // 合并各类编号名称映射
        merchantSnNameMap.putAll(Optional.ofNullable(mallOrderDetailView.getMerchantSnNameMap()).orElse(Map.of()));
        storeSnNameMap.putAll(Optional.ofNullable(mallOrderDetailView.getStoreSnNameMap()).orElse(Map.of()));
        mallSnNameMap.putAll(Optional.ofNullable(mallOrderDetailView.getMallSnNameMap()).orElse(Map.of()));
    }

    private void sumMallSummary(MallTicketDetailView mallTicketDetailView) {
        // 本次商城集合
        Set<String> mallSnSet = mallTicketDetailView.getMallSnNameMap().keySet();

        // 基于商城的统计数据
        Map<String, Long> mallAftersaleTotalCount = mallTicketDetailView.getMallAftersaleTotalCount();
        Map<String, Long> mallRefundTotalCount = mallTicketDetailView.getMallRefundTotalCount();
        Map<String, Long> mallRefundTotalAmount = mallTicketDetailView.getMallRefundTotalAmount();
        Map<String, Map<String, BigDecimal>> mallProductQuantity = mallTicketDetailView.getMallProductQuantity();

        // 和已存在的累加
        for (String mallSn : mallSnSet) {
            MallSummary mallSummary = mallTicketSummaryMap.get(mallSn);
            if (Objects.isNull(mallSummary)) {
                mallSummary = MallSummary.newDefaultInstance(mallTicketDetailView.getMerchantSn(mallSn)
                        , mallTicketDetailView.getStoreSn(mallSn));
                mallTicketSummaryMap.put(mallSn, mallSummary);
            }

            mallSummary.sumAftersaleCount(mallAftersaleTotalCount.getOrDefault(mallSn, 0L))
                    .sumRefundCount(mallRefundTotalCount.getOrDefault(mallSn, 0L))
                    .sumRefundAmount(mallRefundTotalAmount.getOrDefault(mallSn, 0L))
                    .sumSameProduct(mallProductQuantity.getOrDefault(mallSn, Map.of()))
                    .over();
        }

        // 合并各类编号名称映射
        merchantSnNameMap.putAll(Optional.ofNullable(mallTicketDetailView.getMerchantSnNameMap()).orElse(Map.of()));
        storeSnNameMap.putAll(Optional.ofNullable(mallTicketDetailView.getStoreSnNameMap()).orElse(Map.of()));
        mallSnNameMap.putAll(Optional.ofNullable(mallTicketDetailView.getMallSnNameMap()).orElse(Map.of()));
    }

    public long getMallOrderTotalCount(String mallSn) {
        return Optional.ofNullable(mallOrderSummaryMap.get(mallSn)).map(MallSummary::getTotalTradeCount).orElse(0L);
    }

    public long getMallAftersaleTotalCount(String mallSn) {
        return Optional.ofNullable(mallTicketSummaryMap.get(mallSn)).map(MallSummary::getTotalAftersaleCount).orElse(0L);
    }

    public long getMallRefundTotalCount(String mallSn) {
        return Optional.ofNullable(mallTicketSummaryMap.get(mallSn)).map(MallSummary::getTotalRefundCount).orElse(0L);
    }

    public long getMallOrderTotalAmount(String mallSn) {
        return Optional.ofNullable(mallOrderSummaryMap.get(mallSn)).map(MallSummary::getTotalTradeAmount).orElse(0L);
    }

    public long getMallOrderDiscountTotalAmount(String mallSn) {
        return Optional.ofNullable(mallOrderSummaryMap.get(mallSn)).map(MallSummary::getTotalTradeDiscountAmount).orElse(0L);
    }

    public long getMallOrderReceiveTotalAmount(String mallSn) {
        return Optional.ofNullable(mallOrderSummaryMap.get(mallSn)).map(MallSummary::getTotalTradeReceivedAmount).orElse(0L);
    }

    public long getMallRefundTotalAmount(String mallSn) {
        return Optional.ofNullable(mallTicketSummaryMap.get(mallSn)).map(MallSummary::getTotalRefundAmount).orElse(0L);
    }

    public Map<String, BigDecimal> getMallOrderSameProduct(String mallSn) {
        return Optional.ofNullable(mallOrderSummaryMap.get(mallSn)).map(MallSummary::getTotalSameProduct).orElse(Map.of());
    }

    public Map<String, BigDecimal> getMallTicketSameProduct(String mallSn) {
        return Optional.ofNullable(mallTicketSummaryMap.get(mallSn)).map(MallSummary::getTotalSameProduct).orElse(Map.of());
    }

    public List<String> genMallOrderDetailSummary(String mallSn) {
        long mallOrderTotalCount = getMallOrderTotalCount(mallSn);
        long mallOrderTotalAmount = getMallOrderTotalAmount(mallSn);
        long mallOrderDiscountTotalAmount = getMallOrderDiscountTotalAmount(mallSn);
        long mallOrderReceiveTotalAmount = getMallOrderReceiveTotalAmount(mallSn);
        return List.of(String.format(MALL_ORDER_DETAIL_SUMMARY_TEMPLATE, mallOrderTotalCount
                , ExportNumberUtils.centToYuan(mallOrderTotalAmount), ExportNumberUtils.centToYuan(mallOrderDiscountTotalAmount)
                , ExportNumberUtils.centToYuan(mallOrderReceiveTotalAmount)));
    }

    public List<String> genMallTicketDetailSummary(String mallSn) {
        long mallRefundTotalCount = getMallRefundTotalCount(mallSn);
        long mallRefundTotalAmount = getMallRefundTotalAmount(mallSn);
        return List.of(String.format(MALL_TICKET_DETAIL_SUMMARY_TEMPLATE, mallRefundTotalCount
                , ExportNumberUtils.centToYuan(mallRefundTotalAmount)));
    }


    public MallOrderDetailProductSummaryView genMallOrderDetailProductSummary(String mallSn) {
        Map<String, BigDecimal> sameProduct = getMallOrderSameProduct(mallSn);
        return MallOrderDetailProductSummaryView.newInstance(sameProduct);
    }

    public MallAftersaleDetailProductSummaryView genMallAftersaleDetailProductSummary(String mallSn) {
        Map<String, BigDecimal> sameProduct = getMallTicketSameProduct(mallSn);
        return MallAftersaleDetailProductSummaryView.newInstance(sameProduct);
    }


    @Getter
    @Builder(toBuilder = true)
    private static final class MallSummary {
        private final String merchantSn;
        private final String storeSn;
        private long totalTradeCount;
        private long totalTradeAmount;
        private long totalTradeDiscountAmount;
        private long totalTradeReceivedAmount;
        private Map<String, BigDecimal> totalSameProduct;

        private long totalAftersaleCount;
        private long totalRefundCount;
        private long totalRefundAmount;

        public static MallSummary newDefaultInstance(String merchantSn, String storeSn) {
            return MallSummary.builder()
                    .merchantSn(merchantSn)
                    .storeSn(storeSn)
                    .totalTradeCount(0L)
                    .totalTradeAmount(0L)
                    .totalTradeDiscountAmount(0L)
                    .totalTradeReceivedAmount(0L)
                    .totalSameProduct(Maps.newHashMap())
                    .totalAftersaleCount(0L)
                    .totalRefundCount(0L)
                    .totalRefundAmount(0L)
                    .build();
        }

        public MallSummary sumTradeCount(long tradeCount) {
            totalTradeCount += tradeCount;
            return self();
        }

        public MallSummary sumAftersaleCount(long aftersaleCount) {
            totalAftersaleCount += aftersaleCount;
            return self();
        }

        public MallSummary sumRefundCount(long refundCount) {
            totalRefundCount += refundCount;
            return self();
        }

        public MallSummary sumTradeAmount(long tradeAmount) {
            totalTradeAmount += tradeAmount;
            return self();
        }

        public MallSummary sumTradeDiscountAmount(long tradeDiscountAmount) {
            totalTradeDiscountAmount += tradeDiscountAmount;
            return self();
        }

        public MallSummary sumTradeReceivedAmount(long tradeReceivedAmount) {
            totalTradeReceivedAmount += tradeReceivedAmount;
            return self();
        }

        public MallSummary sumRefundAmount(long refundAmount) {
            totalRefundAmount += refundAmount;
            return self();
        }

        public MallSummary sumSameProduct(Map<String, BigDecimal> productQuantity) {
            if (MapUtils.isNotEmpty(productQuantity)) {
                productQuantity.forEach(this::sumSameProduct);
            }
            return self();
        }

        public void over() {}

        public void sumSameProduct(String productName, BigDecimal productQuantity) {
            if (Objects.nonNull(productName) && Objects.nonNull(productQuantity)) {
                BigDecimal currentTotal = totalSameProduct.get(productName);
                if (Objects.isNull(currentTotal)) {
                    totalSameProduct.put(productName, productQuantity);
                } else {
                    totalSameProduct.put(productName, productQuantity.add(currentTotal));
                }
            }
        }


        private MallSummary self() {
            return this;
        }
    }
}
