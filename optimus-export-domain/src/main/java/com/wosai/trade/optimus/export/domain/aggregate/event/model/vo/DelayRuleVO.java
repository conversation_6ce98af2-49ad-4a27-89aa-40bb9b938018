package com.wosai.trade.optimus.export.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wosai.trade.optimus.export.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Date: 2022/12/29 Time: 11:39 AM
 */
@Getter
@Builder(toBuilder = true)
@Jacksonized
public class DelayRuleVO extends BaseVO<DelayRuleVO> {
    public static final int[] DEFAULT_INTERVAL_SCE = {10, 30, 60, 120, 300, 600, 1800, 3600, 3600, 43200};
    public static final int DEFAULT_PROCESSED_COUNT_THRESHOLD = 10;
    public static final int DEFAULT_ALARM_THRESHOLD = DEFAULT_PROCESSED_COUNT_THRESHOLD - 2;
    public static final int DEFAULT_PROCESSED_COUNT = 0;


    @NotNull(message = "处理次数不能为空")
    private final Integer processedCount;
    @NotNull(message = "处理次数阈值不能为空")
    private final Integer processCountThreshold;
    @NotNull(message = "告警阈值不能为空")
    private final Integer alarmThreshold;
    @NotNull(message = "处理间隔不能为空")
    private final int[] intervalSecond;

    public static DelayRuleVO newDefaultInstance() {
        return DelayRuleVO.builder()
                .processedCount(DEFAULT_PROCESSED_COUNT)
                .processCountThreshold(DEFAULT_PROCESSED_COUNT_THRESHOLD)
                .alarmThreshold(DEFAULT_ALARM_THRESHOLD)
                .intervalSecond(DEFAULT_INTERVAL_SCE)
                .build();
    }

    public DelayRuleVO increaseProcessedCount() {
        return this.toBuilder().processedCount(processedCount + 1).build();
    }

    @JsonIgnore
    public int getNextProcessIntervalSecond() {
        int processCountDelayArrMaxPos = intervalSecond.length - 1;
        int pos = processedCount;
        if (processedCount > processCountDelayArrMaxPos) {
            pos = processCountDelayArrMaxPos;
        }
        return intervalSecond[pos];
    }

    @JsonIgnore
    public boolean isReachProcessedMax() {
        return processedCount >= processCountThreshold;
    }

    @Override
    protected DelayRuleVO doReplaceNotNull(DelayRuleVO vo) {
        return null;
    }
}
