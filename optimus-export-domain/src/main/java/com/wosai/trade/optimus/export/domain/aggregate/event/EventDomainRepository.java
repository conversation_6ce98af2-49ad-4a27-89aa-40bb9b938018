package com.wosai.trade.optimus.export.domain.aggregate.event;

import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.query.EventAggrQuery;

import java.util.List;

/**
 * <AUTHOR> Date: 2023/6/16 Time: 6:52 PM
 */
public interface EventDomainRepository {

    void save(EventAggrRoot aggrRoot);

    void batchSave(List<EventAggrRoot> aggrRoots);

    void saveWithoutMQ(EventAggrRoot aggrRoot);

    EventAggrRoot query(EventAggrQuery aggrQuery);

    EventAggrRoot queryWithLockSkipLocked(EventAggrQuery aggrQuery);

    List<EventAggrRoot> batchQuery(EventAggrQuery aggrQuery);

    void batchSend(List<EventAggrRoot> eventAggrRoots);

}
