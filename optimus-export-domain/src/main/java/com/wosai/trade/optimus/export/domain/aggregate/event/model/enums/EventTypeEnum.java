package com.wosai.trade.optimus.export.domain.aggregate.event.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/6/16 Time: 6:57 PM
 */
@Getter
public enum EventTypeEnum {

    MALL_ORDER_EXPORT((byte) 0, "商城订单导出");


    private final byte code;
    private final String desc;


    EventTypeEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static EventTypeEnum ofCode(Byte code) {
        if (Objects.isNull(code)) {
            return null;
        }
        EventTypeEnum[] typeEnums = EventTypeEnum.values();
        for (EventTypeEnum type : typeEnums) {
            if (Objects.equals(type.getCode(), code)) {
                return type;
            }
        }

        return null;
    }

}
