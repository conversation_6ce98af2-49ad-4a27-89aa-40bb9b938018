package com.wosai.trade.optimus.export.domain.aggregate;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.wosai.general.util.ValidationUtils;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.util.JsonUtils;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2022/11/22 Time: 2:33 PM
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class BaseVO<E> {

    public static <T> T genFromJsonString(String jsonString, Class<T> tClass) {
        return JsonUtils.parseObject(jsonString, tClass);
    }

    public static <T> T genFromJsonObject(Object jsonObject, Class<T> tClass) {
        return JsonUtils.convertToObject(jsonObject, tClass);
    }


    public E replaceNotNull(E vo) {
        if (Objects.isNull(vo)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.BIZ_PARAMS_MISSING);
        }
        E e = doReplaceNotNull(vo);
        checkValid(e);
        return e;
    }


    protected void checkValid(E vo) {
        if (Objects.isNull(vo)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.BIZ_OBJECT_MISSING);
        }
        ValidationUtils.ValidationResult result = ValidationUtils.validate(vo);
        if (result.isInvalid()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.ILLEGAL_ARGUMENT, result.getMsg());
        }
    }

    protected abstract E doReplaceNotNull(E vo);

    public String toJsonString() {
        return JsonUtils.toJsonString(this);
    }

    public JsonNode toJsonNode() {
        return JsonUtils.toJsonNode(this);
    }


}
