package com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.trade.optimus.export.common.util.ExportNumberUtils;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2024/8/7 Time: 16:18
 */
@Getter
@Builder(toBuilder = true)
@Jacksonized
public class MallTicketDetailItemView {

    private static final String ITEM_NAME_FORMATTER = "%s-%sx%s%s";

    private final String tradeTime;
    private final String orderSn;
    private final List<String> refundTsnList;
    private final String merchantSn;
    private final String storeSn;
    private final String merchantName;
    private final String storeName;
    private final String mallSn;
    private final String mallName;
    private final String aftersaleTypeDesc;
    private final long aftersaleAmount;
    private final long refundAmount;
    private final String aftersaleReason;
    private final String ticketStateDesc;
    private final List<ExportAftersaleItem> aftersaleItems;

    public List<Object> genContentList() {
        return Lists.newArrayList(tradeTime
                , orderSn
                , genFormatRefundTsn()
                , storeName
                , mallName
                , aftersaleTypeDesc
                , ExportNumberUtils.centToYuan(aftersaleAmount)
                , ExportNumberUtils.centToYuan(refundAmount)
                , aftersaleReason
                , ticketStateDesc
                , genFormatAftersaleItems());
    }

    private String genFormatRefundTsn() {
        if (CollectionUtils.isEmpty(refundTsnList)) {
            return StringUtils.EMPTY;
        }
        return String.join(",\n", refundTsnList);
    }

    private String genFormatAftersaleItems() {
        if (CollectionUtils.isEmpty(aftersaleItems)) {
            return StringUtils.EMPTY;
        }

        List<String> formatterItems = aftersaleItems.stream()
                .map(item -> genItemNameString(item.getTitle(), item.getSkuDesc()
                        , item.getQuantity(), item.getUnit())).toList();

        return String.join(";\n", formatterItems);
    }

    public String genItemNameString(String title, String skuDesc, String quantity, String unit) {
        return String.format(ITEM_NAME_FORMATTER
                , Optional.of(title)
                        .orElse(StringUtils.EMPTY)
                , Optional.ofNullable(skuDesc)
                        .orElse(StringUtils.EMPTY)
                , Optional.ofNullable(quantity)
                        .orElse(StringUtils.EMPTY)
                , Optional.ofNullable(unit)
                        .orElse(StringUtils.EMPTY));
    }

    public Map<String, BigDecimal> calculateSameProductQuantity() {
        if (CollectionUtils.isEmpty(aftersaleItems)) {
            return Maps.newHashMap();
        }
        return aftersaleItems.stream()
                .collect(Collectors.groupingBy(MallTicketDetailItemView.ExportAftersaleItem::getTitle,
                        Collectors.mapping(item -> new BigDecimal(item.getQuantity())
                        , Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }

    public boolean isRefunded() {
        return refundAmount > 0;
    }

    @Getter
    @Builder
    public static class ExportAftersaleItem {
        private final String title;
        private final String skuDesc;
        private final String quantity;
        private final String unit;
    }

}
