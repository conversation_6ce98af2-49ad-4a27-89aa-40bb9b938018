package com.wosai.trade.optimus.export.infrastructure.support.excel;

import com.alibaba.excel.write.metadata.WriteTable;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR> Date: 2024/8/5 Time: 10:53
 */
@Getter
@SuperBuilder(toBuilder = true)
public class ExportWriteTable extends ExportWriteUnit {
    private final WriteTable writeTable;

    private ExportWriteTable(WriteTable writeTable, List<List<?>> data) {
        super(data);
        this.writeTable = writeTable;
    }

    public static ExportWriteTable create(WriteTable writeTable) {
        return new ExportWriteTable(writeTable, Lists.newArrayList());
    }
}
