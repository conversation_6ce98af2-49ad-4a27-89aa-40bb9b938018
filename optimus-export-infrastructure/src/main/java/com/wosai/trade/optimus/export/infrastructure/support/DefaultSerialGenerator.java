package com.wosai.trade.optimus.export.infrastructure.support;

import com.wosai.trade.optimus.export.common.constant.SequenceNamespaceEnum;
import com.wosai.trade.optimus.export.common.support.SerialGenerator;
import com.wosai.trade.optimus.export.common.util.ApplicationContextUtils;
import com.wosai.trade.optimus.export.infrastructure.config.db.SequenceConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> Date: 2023/6/8 Time: 5:56 PM
 */
@Component
public class DefaultSerialGenerator implements SerialGenerator {

    private static final long ID_SUFFIX_MOD_BASE = 100000L;

    private static final long BEGIN_OF_DATE_ID_SUFFIX_MOD_BASE = ID_SUFFIX_MOD_BASE;
    private static final long END_OF_DATE_ID_SUFFIX_MOD_BASE = 99999L;
    private static final DateTimeFormatter ID_PREFIX_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Resource
    private SequenceConfig sequenceConfig;

    public static DefaultSerialGenerator getInstance() {
        return ApplicationContextUtils.getBean(DefaultSerialGenerator.class);
    }


    @Override
    public long genEventId() {
        long seq = sequenceConfig.genSequence(SequenceNamespaceEnum.EVENT.getNamespace());
        return genId(genIdPrefix(), seq);
    }

    @Override
    public String genCommonSn() {
        long seq = sequenceConfig.genSequence(SequenceNamespaceEnum.COMMON_SN.getNamespace());
        return Long.toString(genId(genIdPrefix(), seq));
    }

    public long getDateBeginId(LocalDate date) {
        return 0l;

    }

    public long getDateEndId(LocalDate date) {
        return 0l;

    }

    private String genIdPrefix() {
        return LocalDateTime.now().format(ID_PREFIX_DATE_TIME_FORMATTER);
    }

    private long genId(String prefix, long seq) {
        return Long.parseLong(prefix + String.format("%05d", seq % ID_SUFFIX_MOD_BASE));
    }

    public long genExportMallOrderId() {
        long seq = sequenceConfig.genSequence(SequenceNamespaceEnum.MALL_EXPORT_ORDER.getNamespace());
        return genId(genIdPrefix(), seq);
    }
}
