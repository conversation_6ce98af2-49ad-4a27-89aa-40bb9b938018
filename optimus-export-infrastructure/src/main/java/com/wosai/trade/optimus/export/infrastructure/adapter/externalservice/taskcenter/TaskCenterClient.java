package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.taskcenter;

import com.wosai.trade.optimus.export.common.template.InvokeProcessor;
import com.wosai.trade.optimus.export.common.template.InvokeTemplate;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.taskcenter.req.TaskCenterTaskModifyRequest;
import com.wosai.upay.task.center.model.dto.TaskResultDTO;
import com.wosai.upay.task.center.service.TaskLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2024/7/22 Time: 10:33
 */
@Slf4j
@Component
public class TaskCenterClient {

    @Resource
    private TaskLogService taskLogService;

    public boolean updateTaskResult(TaskCenterTaskModifyRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected Boolean invoke(TaskCenterTaskModifyRequest request) {

                TaskResultDTO taskResultDTO = request.genTaskResultDTO();
                log.info("[下载中心同步任务处理结果]>>>>>>入参: {}"
                        , JsonUtils.toJsonStringIgnoreException(taskResultDTO));
                taskLogService.updateTaskResult(taskResultDTO);
                log.info("[下载中心同步任务处理结果]>>>>>>成功");
                return true;
            }

            @Override
            protected Boolean onFailure(TaskCenterTaskModifyRequest request, Throwable throwable) {
                log.warn("[下载中心同步任务处理结果]>>>>>>上传异常, 异常栈: ", throwable);
                return false;
            }
        });
    }
}