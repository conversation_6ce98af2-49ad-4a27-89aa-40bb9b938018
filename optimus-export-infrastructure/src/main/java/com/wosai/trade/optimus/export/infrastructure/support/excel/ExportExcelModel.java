package com.wosai.trade.optimus.export.infrastructure.support.excel;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import lombok.Builder;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2024/8/5 Time: 10:54
 */
@Builder
public class ExportExcelModel implements ExcelOperations {
    private WriteSheet sheet;
    private List<ExportWriteUnit> writeUnits;

    public static ExportExcelModel create(WriteSheet sheet) {
        if (Objects.isNull(sheet)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.BIZ_OBJECT_MISSING);
        }
        return ExportExcelModel.builder()
                .sheet(sheet)
                .writeUnits(Lists.newArrayList())
                .build();
    }

    public ExportExcelModel append(ExportWriteUnit writeUnit) {
        writeUnits.add(writeUnit);
        return self();
    }

    @Override
    public void write(ExcelWriter writer) {
        if (CollectionUtils.isEmpty(writeUnits)) {
            writer.write(List.of(), sheet);
        } else {
            for (ExportWriteUnit writeUnit : writeUnits) {
                if (writeUnit instanceof ExportWriteTable table) {
                    writer.write(table.getData(), sheet, table.getWriteTable());
                }
                else if (writeUnit instanceof ExportWriteData data) {
                    writer.write(data.getData(), sheet);
                }
            }
        }
    }

    @Override
    public void clear() {
        if (CollectionUtils.isNotEmpty(writeUnits)) {
            writeUnits.stream().filter(Objects::nonNull).forEach(ExportWriteUnit::clear);
            writeUnits.clear();
        }
    }

    @Override
    public void writeWithClear(ExcelWriter writer) {
        write(writer);
        clear();
    }

    private ExportExcelModel self() {
        return this;
    }
}
