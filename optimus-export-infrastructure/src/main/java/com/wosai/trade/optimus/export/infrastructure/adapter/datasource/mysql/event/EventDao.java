package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event;

import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.model.EventDelete;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.model.EventQuery;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.po.EventPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/15
 */
@Mapper
public interface EventDao {

    int insert(EventPO po);

    int batchInsert(List<EventPO> pos);

    int update(EventPO po);

    EventPO select(EventQuery query);

    EventPO selectForUpdate(EventQuery query);

    EventPO selectForUpdateSkipLocked(EventQuery query);

    List<EventPO> batchSelect(EventQuery query);

    int delete(EventDelete delete);
}
