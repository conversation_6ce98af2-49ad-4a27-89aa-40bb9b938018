package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model;

import lombok.Builder;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2024/2/21 Time: 09:59
 */
@Getter
@Builder
public class OssUrlUploadAndReturnResult {
    public static final OssUrlUploadAndReturnResult DEFAULT_INSTANCE = OssUrlUploadAndReturnResult.builder().build();

    private final String url;

    public static OssUrlUploadAndReturnResult newInstance(String url) {
        return new OssUrlUploadAndReturnResult(url);
    }


    public boolean isNotInvokeSucceed() {
        return Objects.equals(this, DEFAULT_INSTANCE) || StringUtils.isEmpty(url);
    }

    public boolean isInvokeSucceed() {
        return !isNotInvokeSucceed();
    }


}
