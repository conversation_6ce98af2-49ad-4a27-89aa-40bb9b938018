package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req;

import com.wosai.trade.optimus.api.model.req.MallIDModel;
import com.wosai.trade.optimus.api.request.mall.MallOrdersForInnerQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.trade.order.api.request.model.AdminOrderFilterModel;
import com.wosai.trade.order.api.request.model.CursorModel;
import com.wosai.trade.order.api.request.model.SortModel;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2024/8/5 Time: 15:57
 */
@SuperBuilder(toBuilder = true)
public class ExportMallOrdersQueryRequest extends BaseRequest {
    private final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final CursorModel.CursorFieldEnum CURSOR_FIELD = CursorModel.CursorFieldEnum.ORDER_ID;
    private static final SortModel SORT = SortModel.builder()
            .sort(SortModel.SortEnum.DESC)
            .sortField(SortModel.SortFieldEnum.ORDER_ID)
            .build();

    private final String mallSn;
    private final String mallSignature;
    private final List<Integer> orderStates;
    private final LocalDateTime beginDateTime;
    private final LocalDateTime endDateTime;
    private final String payTsn;
    private final String orderSn;
    private final String endCursor;
    private final Integer count;


    public MallOrdersForInnerQueryRequest genMallOrdersForInnerQueryRequest() {
        CursorModel cursor = CursorModel.builder()
                .cursorField(CURSOR_FIELD)
                .endCursor(endCursor)
                .count(count)
                .build();

        AdminOrderFilterModel filter = AdminOrderFilterModel.builder()
                .orderStates(orderStates)
                .beginDateTime(DATE_FORMATTER.format(beginDateTime))
                .endDateTime(DATE_FORMATTER.format(endDateTime))
                .payTsn(payTsn)
                .orderSn(orderSn)
                .build();

        MallIDModel mallID = null;
        if (Objects.nonNull(mallSn) && Objects.nonNull(mallSignature)) {
            mallID = MallIDModel.builder()
                    .mallSn(mallSn)
                    .signature(mallSignature)
                    .build();
        }

        return MallOrdersForInnerQueryRequest.builder()
                .mallID(mallID)
                .cursor(cursor)
                .sort(SORT)
                .filter(filter)
                .build();
    }
}
