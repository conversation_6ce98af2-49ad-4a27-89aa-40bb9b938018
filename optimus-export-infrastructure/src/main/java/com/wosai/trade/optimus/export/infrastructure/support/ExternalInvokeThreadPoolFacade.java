package com.wosai.trade.optimus.export.infrastructure.support;

import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.threadpoolconfig.ThreadPoolConfigDao;
import com.wosai.trade.optimus.export.infrastructure.config.support.DataSourceContextHolder;
import com.wosai.trade.optimus.export.infrastructure.util.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR> Date: 2023/5/5 Time: 6:01 PM
 */
@Slf4j
@Component
public class ExternalInvokeThreadPoolFacade implements ThreadPoolConfigDao {
    private static final String DEFAULT_EXECUTOR_KEY = "default";
    private static final double SCENE_DYNAMIC_BALANCE_FACTOR = 0.75;
    private final ThreadPoolConfigDao threadPoolConfigDao;

    private ExternalInvokeThreadPoolFacade(ThreadPoolConfigDao threadPoolConfigDao) {
        this.threadPoolConfigDao = threadPoolConfigDao;
    }

    @Override
    public ThreadPoolExecutor query(String scene) {
        //场景线程池
        ThreadPoolExecutor sceneThreadPool = threadPoolConfigDao.query(scene);
        //默认线程池
        ThreadPoolExecutor defaultPoolExecutor = threadPoolConfigDao.query(DEFAULT_EXECUTOR_KEY);
        if (Objects.isNull(sceneThreadPool) && Objects.isNull(defaultPoolExecutor)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum
                    .MISSING_THREAD_POOL);
        }
        if (Objects.isNull(sceneThreadPool)) {
            return defaultPoolExecutor;
        }
        if (ThreadPoolUtils.isThreadPoolBusy(sceneThreadPool)) {
            log.warn("[获取外部调用线程池]>>>>>>场景线程池繁忙, 场景值: {}", scene);
            return ThreadPoolUtils.getThreadPoolBusyLevel(sceneThreadPool) * SCENE_DYNAMIC_BALANCE_FACTOR
                    > ThreadPoolUtils.getThreadPoolBusyLevel(defaultPoolExecutor)
                    ? defaultPoolExecutor : sceneThreadPool;
        }
        return sceneThreadPool;
    }

    public void execute(String scene, Runnable runnable) {
        DataSourceContextHolder.DataSourceTypeEnum dataSourceType = DataSourceContextHolder.getDataSourceKey();
        query(scene).execute(() -> {
            try {
                DataSourceContextHolder.setDataSource(dataSourceType);
                runnable.run();
            } finally {
                DataSourceContextHolder.clearDataSourceKey();
            }
        });
    }
}
