package com.wosai.trade.optimus.export.infrastructure.config.support;


/**
 * <AUTHOR> Date: 2023/4/27 Time: 3:59 PM
 */
public class DataSourceContextHolder {
    private static final ThreadLocal<DataSourceTypeEnum> CONTEXT_HOLDER = new ThreadLocal<>();


    public static void markAsPeripheryDataSource() {
        CONTEXT_HOLDER.set(DataSourceTypeEnum.PERIPHERY);
    }

    public static void markAsCoreDataSource() {
        CONTEXT_HOLDER.set(DataSourceTypeEnum.CORE);
    }

    public static void setDataSource(DataSourceTypeEnum dataSource) {
        CONTEXT_HOLDER.set(dataSource);
    }

    public static DataSourceTypeEnum getDataSourceKey() {
        return CONTEXT_HOLDER.get();
    }

    public static void clearDataSourceKey() {
        CONTEXT_HOLDER.remove();
    }



    public enum DataSourceTypeEnum {
        CORE,
        PERIPHERY,

        ;
    }

}
