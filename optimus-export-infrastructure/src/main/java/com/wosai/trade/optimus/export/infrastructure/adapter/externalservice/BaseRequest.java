package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice;


import com.wosai.trade.optimus.export.common.util.JsonUtils;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR> Date: 2022/12/12 Time: 4:08 PM
 */
@SuperBuilder(toBuilder = true)
public abstract class BaseRequest {

    public String toJsonString() {
        return JsonUtils.toJsonStringIgnoreException(this);
    }



    @Getter
    protected static class JsonRpcFormatterModel {
        private static final Integer DEFAULT_ID = 1;
        private static final String DEFAULT_JSONRPC = "2.0";

        private final Integer id;
        private final String jsonrpc;
        private final String method;
        private final List<?> params;

        private JsonRpcFormatterModel(Integer id, String method, String jsonrpc, List<?> params) {
            this.id = id;
            this.method = method;
            this.jsonrpc = jsonrpc;
            this.params = params;
        }

        public static JsonRpcFormatterModel newInstance(String method, List<?> params) {
            return new JsonRpcFormatterModel(DEFAULT_ID, method, DEFAULT_JSONRPC, params);
        }

        public String toJsonBody() {
            return JsonUtils.toJsonString(this);
        }

    }

}
