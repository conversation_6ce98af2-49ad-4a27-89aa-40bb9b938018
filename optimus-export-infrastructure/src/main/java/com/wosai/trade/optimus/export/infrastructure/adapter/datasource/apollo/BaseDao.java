package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> Date: 2022/12/7 Time: 11:03 AM
 */
@Slf4j
public abstract class BaseDao {

    protected <T> T loadConfig(TypeReference<T> typeReference) {
        Config config = getApolloConfig();
        if (Objects.isNull(config)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.APOLLO_CONFIG_NOT_EXIST);
        }
        String configName = getConfigName();
        return loadConfig(typeReference, config, configName);
    }

    protected <T> T loadConfig(TypeReference<T> typeReference, String configName) {
        Config config = getApolloConfig();
        if (Objects.isNull(config)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.APOLLO_CONFIG_NOT_EXIST);
        }
        return loadConfig(typeReference, config, configName);
    }


    protected Config getApolloConfig(String namespace) {
        if (Objects.nonNull(namespace)) {
            return ConfigService.getConfig(namespace);
        }
        return null;
    }

    protected Config getApolloConfig() {
        String namespace = getNameSpace();
        if (Objects.nonNull(namespace)) {
            return ConfigService.getConfig(namespace);
        }
        return null;
    }

    protected String getConfigValue(String configName) {
        Config config = getApolloConfig();
        if (Objects.isNull(config)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.APOLLO_CONFIG_NOT_EXIST);
        }
        return config.getProperty(configName, null);
    }

    private <T> T loadConfig(TypeReference<T> typeReference, Config config, String configName) {
        String configStr = config.getProperty(configName, null);
        log.info("[Apollo配置文件加载]>>>>>>" + configName + ": {}", configStr);
        if (StringUtils.isBlank(configStr)) {
            log.error("[Apollo配置文件加载]>>>>>>" + configName + "配置不存在");
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.APOLLO_CONFIG_NOT_EXIST);
        }
        return JsonUtils.parseObject(configStr, typeReference);
    }

    protected abstract void load();

    protected abstract String getNameSpace();

    protected abstract String getConfigName();

    protected void reload(Set<String> changedKeys) {
    }

    @PostConstruct
    private void init() {
        load();
        registerChangeListener();
        registerChangesWatcher();
    }


    private void registerChangeListener() {
        Config config = getApolloConfig(getNameSpace());
        if (Objects.nonNull(config)) {
            config.addChangeListener(changeEvent -> {
                if (changeEvent.isChanged(getConfigName())) {
                    load();
                }
            });
        }
    }

    private void registerChangesWatcher() {
        Config config = getApolloConfig(getNameSpace());
        if (Objects.nonNull(config)) {
            config.addChangeListener(changeEvent -> {
                reload(changeEvent.changedKeys());
            });
        }
    }

}
