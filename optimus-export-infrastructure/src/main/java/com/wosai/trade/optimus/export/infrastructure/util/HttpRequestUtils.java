package com.wosai.trade.optimus.export.infrastructure.util;


import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2019-10-10 Time: 15:37
 */
public class HttpRequestUtils {

    private static final String HTTP_HEADER_X_FORWARDED_FOR = "x-forwarded-for";
    private static final String HTTP_HEADER_REMOTEIP = "remoteip";

    private HttpRequestUtils() {}

    public static HttpServletRequest getHttpServletRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(requestAttributes)) {
            return null;
        }
        return ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
    }

    public static String getRemoteRealIP() {
        HttpServletRequest request = getHttpServletRequest();
        if (Objects.nonNull(request)) {
            String realIp = request.getHeader(HTTP_HEADER_X_FORWARDED_FOR);
            if (StringUtils.isNotBlank(realIp)) {
                return realIp.split(",")[0];
            }
            realIp = request.getHeader(HTTP_HEADER_REMOTEIP);
            if (StringUtils.isNotBlank(realIp)) {
                return realIp;
            }
        }
        return IPUtils.getLocalAddress();
    }

}
