package com.wosai.trade.optimus.export.infrastructure.support;

import lombok.Builder;
import lombok.Getter;
import okhttp3.*;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/4/26 Time: 9:46 AM
 */
@Component
public class HttpClientFacade {
    private final CoreRequestFaced coreRequestFaced;
    private final PeripheryRequestFaced peripheryRequestFaced;


    private HttpClientFacade(OkHttpClient okHttpCoreClient, OkHttpClient okHttpPeripheryClient) {
        coreRequestFaced = new CoreRequestFaced(okHttpCoreClient);
        peripheryRequestFaced = new PeripheryRequestFaced(okHttpPeripheryClient);
    }

    public RequestFacade core() {
        return coreRequestFaced;
    }

    public RequestFacade periphery() {
        return peripheryRequestFaced;
    }

    private HttpResult sendRequest(OkHttpClient client, Request request) throws Throwable {
        long beginTime = System.currentTimeMillis();
        try (Response response = client.newCall(request).execute()) {
            long timeConsuming = System.currentTimeMillis() - beginTime;
            ResponseBody responseBody = response.body();
            String body = null;
            if (Objects.nonNull(responseBody)) {
                body = responseBody.string();
            }
            if (response.isSuccessful()) {
                return HttpResult.builder()
                        .isSuccessful(true)
                        .code(response.code())
                        .body(body)
                        .timeConsuming(timeConsuming)
                        .build();
            }
            return HttpResult.builder()
                    .isSuccessful(false)
                    .code(response.code())
                    .body(body)
                    .timeConsuming(timeConsuming)
                    .build();
        }
    }

    private class CoreRequestFaced extends RequestFacade {
        protected CoreRequestFaced(OkHttpClient okHttpClient) {
            super(okHttpClient);
        }
    }

    private class PeripheryRequestFaced extends RequestFacade {
        protected PeripheryRequestFaced(OkHttpClient okHttpClient) {
            super(okHttpClient);
        }
    }

    public abstract class RequestFacade {
        private final OkHttpClient okHttpClient;

        protected RequestFacade(OkHttpClient okHttpClient) {
            this.okHttpClient = okHttpClient;
        }

        public HttpResult post(String url, String body, String mediaType, String... headers) throws Throwable {
            Request.Builder builder = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(body, MediaType.parse(mediaType)));
            if (ArrayUtils.isNotEmpty(headers)) {
                builder.headers(Headers.of(headers));
            }
            return sendRequest(okHttpClient, builder.build());
        }

        public HttpResult post(String url, String body, String mediaType, Duration callTimeout
                , String... headers) throws Throwable {
            Request.Builder builder = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(body, MediaType.parse(mediaType)));
            if (ArrayUtils.isNotEmpty(headers)) {
                builder.headers(Headers.of(headers));
            }
            return sendRequest(okHttpClient.newBuilder().callTimeout(callTimeout).build(), builder.build());
        }

    }

    @Builder
    @Getter
    public static class HttpResult {
        private boolean isSuccessful;
        private int code;
        private String body;
        private Long timeConsuming;
    }

}
