package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/7/6
 */
@Data
@Accessors(chain = true)
public class ActivatedTerminalCreateResult {

    @JsonProperty("sn")
    private String sn;
    @JsonProperty("merchant_id")
    private String merchantId;
    @JsonProperty("merchant_sn")
    private String merchantSn;
    @JsonProperty("merchant_name")
    private String merchantName;
    @JsonProperty("store_id")
    private String storeId;
    @JsonProperty("store_sn")
    private String storeSn;
    @JsonProperty("store_name")
    private String storeName;

    @JsonIgnore
    public boolean isNotExist() {
        return StringUtils.isEmpty(sn);
    }

    public void checkExist() {
        if (isNotExist()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.TERMINAL_CREATE_FAILURE);
        }
    }

}
