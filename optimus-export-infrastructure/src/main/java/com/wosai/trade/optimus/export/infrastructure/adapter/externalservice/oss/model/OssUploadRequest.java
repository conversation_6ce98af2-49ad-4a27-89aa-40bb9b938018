package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.springframework.web.util.UriUtils;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> Date: 2021/9/12 Time: 5:51 下午
 */
@Getter
@SuperBuilder(toBuilder = true)
public class OssUploadRequest extends BaseOssRequest {

    private static final String ATTACHMENT_FILENAME_S_FILENAME_UTF_8 = "attachment; filename=\"%s\"; filename*=UTF-8''%s";

    @JsonIgnore
    private byte[] content;

    private String fileName;

    public String genAttachmentDisposition() {
        String fileNameSanitized = UriUtils.encode(fileName, StandardCharsets.UTF_8);
        return String.format(ATTACHMENT_FILENAME_S_FILENAME_UTF_8, fileNameSanitized, fileNameSanitized);
    }

    public OssUrlGenerateRequest genOssUrlGenerateRequest() {
        return OssUrlGenerateRequest.builder().path(this.getPath()).build();
    }
}
