package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

/**
 * <AUTHOR> Date: 2023/1/11 Time: 3:51 PM
 */
@Getter
public class MerchantBaseInfoQueryResult {

    @JsonProperty("merchant_id")
    private String merchantId;
    @JsonProperty("merchant_sn")
    private String merchantSn;
    @JsonProperty("merchant_name")
    private String merchantName;
    @JsonProperty("store_id")
    private String storeId;
    @JsonProperty("store_sn")
    private String storeSn;
    @JsonProperty("store_name")
    private String storeName;
    @JsonProperty("terminal_id")
    private String terminalId;
    @JsonProperty("terminal_sn")
    private String terminalSn;
    @JsonProperty("terminal_name")
    private String terminalName;


}
