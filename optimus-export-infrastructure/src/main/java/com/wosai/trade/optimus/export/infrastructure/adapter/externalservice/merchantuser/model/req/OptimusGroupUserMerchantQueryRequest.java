package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.req;

import com.wosai.app.dto.QueryGroupMerchantAuthReq;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR> Date: 2024/7/10 Time: 10:12
 */
@Getter
@Builder
public class OptimusGroupUserMerchantQueryRequest {

    private final String groupId;

    public QueryGroupMerchantAuthReq genQueryMerchantUserReq() {
        return new QueryGroupMerchantAuthReq()
                .setGroup_id(groupId);
    }
}
