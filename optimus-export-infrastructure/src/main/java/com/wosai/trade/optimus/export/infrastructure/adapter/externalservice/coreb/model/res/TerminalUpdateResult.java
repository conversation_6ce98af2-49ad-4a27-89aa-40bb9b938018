package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res;

import com.wosai.trade.optimus.export.common.util.JsonUtils;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/8/21 Time: 17:03
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class TerminalUpdateResult {
    public static final TerminalUpdateResult DEFAULT_RESULT =  TerminalUpdateResult.builder().build();

    private String id;
    private String sn;
    private String name;

    public static TerminalUpdateResult newInstance(Map<?, ?> result) {
        return JsonUtils.convertToObject(result, TerminalUpdateResult.class);
    }


    public boolean isInvokeSuccess() {
        return Objects.nonNull(id);
    }

    public boolean isInvokeFailure() {
        return !isInvokeSuccess();
    }

}
