package com.wosai.trade.optimus.export.infrastructure.support.excel;

import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> Date: 2024/8/5 Time: 10:52
 */
@Getter
@SuperBuilder(toBuilder = true)
public abstract class ExportWriteUnit {
    protected final List<List<?>> data;

    protected ExportWriteUnit(List<List<?>> data) {
        this.data = data;
    }

    public ExportWriteUnit writeRow(List<?> row) {
        data.add(row);
        return self();
    }

    public ExportWriteUnit writeRows(List<List<?>> rows) {
        data.addAll(rows);
        return self();
    }

    public void clear() {
        if (CollectionUtils.isNotEmpty(data)) {
            data.clear();
        }
    }

    private ExportWriteUnit self() {
        return this;
    }
}
