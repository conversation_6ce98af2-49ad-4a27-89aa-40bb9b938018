package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.feishu;

import com.wosai.trade.optimus.export.common.template.InvokeProcessor;
import com.wosai.trade.optimus.export.common.template.WithoutResultTemplate;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.feishu.model.FeishuSendRequest;
import com.wosai.trade.optimus.export.infrastructure.support.HttpClientFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2023/2/15 Time: 9:43 AM
 */
@Slf4j
@Component
public class FeishuClient {
    private static final String CONTENT_TYPE = "application/json;charset=utf-8";
    @Resource
    private HttpClientFacade httpClientFacade;


    public void sendNotice(FeishuSendRequest request) {
        InvokeProcessor.processWithoutResult(request, new WithoutResultTemplate<>() {
            @Override
            protected void invoke(FeishuSendRequest request) throws Throwable {
                String requestStr = request.getSendBodyString();
                log.debug("[飞书群任务结果通知]>>>>>>body: {}", requestStr);
                HttpClientFacade.HttpResult httpResult = httpClientFacade.periphery()
                        .post(request.getNotifyUrl(), requestStr, CONTENT_TYPE);
                if (!httpResult.isSuccessful()) {
                    log.warn("[飞书群任务结果通知]>>>>>>发送失败, http状态非2XX, code: {}, msg: {}"
                            , httpResult.getCode(), httpResult.getBody());
                }
            }

            @Override
            protected void onFailure(FeishuSendRequest feishuSendRequest, Throwable throwable) {
                log.error("[飞书群任务结果通知]>>>>>>发送异常, 异常栈: ", throwable);
            }

        });
    }
}
