package com.wosai.trade.optimus.export.infrastructure.util;

import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR> Date: 2023/5/6 Time: 9:54 AM
 */
public class ThreadPoolUtils {

    private static final int MAX_BUSY_LEVEL = 100;
    private static final int BUSY_THRESHOLD = 95;

    /**
     * 获取线程池繁忙程度
     *
     * @param executor 线程池
     * @return 繁忙程度，0~100
     */
    public static int getThreadPoolBusyLevel(ThreadPoolExecutor executor) {
        if (executor == null) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.MISSING_THREAD_POOL);
        }

        int activeCount = executor.getActiveCount();
        int queueSize = executor.getQueue().size();
        int maxPoolSize = executor.getMaximumPoolSize();

        int busyLevel;
        if (activeCount == 0 && queueSize == 0) {
            // 线程池中没有任何任务
            busyLevel = 0;
        } else {
            // 计算繁忙程度
            double busyRatio = (double) (activeCount + queueSize) / (double) (maxPoolSize + queueSize);
            busyLevel = (int) Math.round(busyRatio * MAX_BUSY_LEVEL);
        }

        return busyLevel;
    }

    public static boolean isThreadPoolBusy(ThreadPoolExecutor executor) {
        return getThreadPoolBusyLevel(executor) >= BUSY_THRESHOLD;
    }

    /**
     * 获取两个线程池中相对空闲的线程池
     *
     * @param executor1
     * @param executor2
     * @return
     */
    public static ThreadPoolExecutor getMoreIdleExecutor(ThreadPoolExecutor executor1, ThreadPoolExecutor executor2) {
        return getThreadPoolBusyLevel(executor1) > getThreadPoolBusyLevel(executor2) ? executor2 : executor1;
    }

}
