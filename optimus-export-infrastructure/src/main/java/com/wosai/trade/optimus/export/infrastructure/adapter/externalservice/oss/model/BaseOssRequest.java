package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model;

import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalUnit;
import java.util.Date;

/**
 * <AUTHOR> Date: 2021/9/12 Time: 6:18 下午
 */
@Data
@SuperBuilder(toBuilder = true)
public class BaseOssRequest {
    private static final long FILE_EXPIRE_DAY = 7L;

    private String bucketName;
    private String path;
    private Long expirationTime;


    public Date getExpirationTime(long offset, TemporalUnit temporalUnit) {
        if (offset <= 0) {
            return Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
        }
        return Date.from(LocalDateTime.now().plus(offset, temporalUnit).atZone(ZoneId.systemDefault()).toInstant());
    }

}
