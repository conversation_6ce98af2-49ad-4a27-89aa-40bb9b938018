package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.req;

import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.util.Map;

/**
 * <AUTHOR> Date: 2023/8/10 Time: 11:32
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class TerminalUpdateRequest extends BaseRequest {

    private String id;
    private String name;

    public Map<?, ?> genRequest() {
        return JsonUtils.convertToObject(this, Map.class);
    }

}
