package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.taskcenter.req;

import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.upay.task.center.model.dto.TaskResultDTO;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> Date: 2024/7/22 Time: 10:33
 */
@Getter
@SuperBuilder(toBuilder = true)
public class TaskCenterTaskModifyRequest extends BaseRequest {

    // 下载中心用于统计下载进度
    private static final String TOTAL_PROCESS_LENGTH = "100";

    private String filePath;
    private String taskLogId;

    public TaskResultDTO genTaskResultDTO() {
        TaskResultDTO taskResultDTO = new TaskResultDTO();
        taskResultDTO.setTaskLogId(taskLogId);
        taskResultDTO.setSuccess(true);
        taskResultDTO.setOssUrl(filePath); // 下载中心坚持用ossUrl这个名字
        taskResultDTO.setTotalProgressLength(TOTAL_PROCESS_LENGTH);
        return taskResultDTO;
    }
}
