package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.kvconfig;

import com.ctrip.framework.apollo.core.ConfigConsts;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.BaseDao;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.kvconfig.po.KVConfigPO;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2023/4/22 Time: 9:32 PM
 */
@Component
public class KVConfigDaoImpl extends BaseDao implements KVConfigDao {
    private static final String COMMON_KV_CONFIG = "common_kv_config";

    private volatile Map<String, KVConfigPO> kvConfigPOMap;

    @Override
    public KVConfigPO query(String key) {
        if (MapUtils.isNotEmpty(kvConfigPOMap)) {
            return kvConfigPOMap.get(key);
        }
        return null;
    }

    @Override
    public KVConfigPO queryNotNull(String key) {
        KVConfigPO kvConfigPO = query(key);
        if (Objects.isNull(kvConfigPO)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.CONFIG_NOT_EXIST);
        }
        return kvConfigPO;
    }

    @Override
    protected void load() {
        kvConfigPOMap = loadConfig(new TypeReference<List<KVConfigPO>>() {
        }).stream()
                .collect(Collectors.toMap(KVConfigPO::getKey, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    protected String getNameSpace() {
        return ConfigConsts.NAMESPACE_APPLICATION;
    }

    @Override
    protected String getConfigName() {
        return COMMON_KV_CONFIG;
    }

}
