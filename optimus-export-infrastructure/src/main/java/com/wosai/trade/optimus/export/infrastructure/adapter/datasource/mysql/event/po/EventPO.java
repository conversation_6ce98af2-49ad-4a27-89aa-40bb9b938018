package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.po;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/7/15
 */
@Data
@Accessors(chain = true)
public class EventPO {

    private Long id;
    private Byte type;
    private JsonNode content;
    private Byte state;
    private String result;
    private JsonNode delayRule;
    private LocalDateTime nextProcessTime;
    private String associatedSn;
    private JsonNode ext;
    private LocalDateTime ctime;
    private LocalDateTime mtime;
    private Long version;
}
