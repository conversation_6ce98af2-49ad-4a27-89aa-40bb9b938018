package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.po;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Date: 2023/7/16 Time: 17:21
 */
@Data
@Accessors(chain = true)
public class ExportMallOrderPO {

    private Long id;
    private Byte state;
    private String ownerId;
    private String ownerUserId;
    private JsonNode operator;
    private JsonNode exportInfo;
    private JsonNode exportResult;
    private JsonNode ext;
    private LocalDateTime expiredAt;
    private LocalDateTime ctime;
    private LocalDateTime mtime;
    private Long version;


}
