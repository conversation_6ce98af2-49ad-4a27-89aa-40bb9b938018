package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.res;

import com.wosai.app.dto.V2.UcMerchantUserInfo;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR> Date: 2024/5/16 Time: 10:16
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class OptimusMerchantUserQueryResult {

    public final static OptimusMerchantUserQueryResult DEFAULT = OptimusMerchantUserQueryResult.builder().build();


    private List<UcMerchantUserInfo> userInfos;

    public static OptimusMerchantUserQueryResult newInstance(List<UcMerchantUserInfo> userInfos) {
        return OptimusMerchantUserQueryResult.builder()
                .userInfos(userInfos)
                .build();
    }

}
