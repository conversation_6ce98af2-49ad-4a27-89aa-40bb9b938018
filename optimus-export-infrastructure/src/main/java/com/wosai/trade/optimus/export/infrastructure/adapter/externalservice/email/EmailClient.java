package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.email;

import com.wosai.trade.optimus.export.common.template.InvokeProcessor;
import com.wosai.trade.optimus.export.common.template.InvokeTemplate;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.email.model.EmailSendRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayInputStream;

/**
 * <AUTHOR> Date: 2021/9/10 Time: 4:14 下午
 */
@Slf4j
@Component
public class EmailClient {

    @Resource
    private JavaMailSender javaMailSender;

    public boolean sendWithAttachment(EmailSendRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected Boolean invoke(EmailSendRequest emailSendRequest) throws Throwable {
                log.info("[邮件发送]>>>>>>入参: {}", JsonUtils.toJsonString(request));
                MimeMessage mimeMessage = javaMailSender.createMimeMessage();
                MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessage, true);
                ByteArrayInputStream byteArrayInputStream
                        = new ByteArrayInputStream(request.getAttachmentContent());
                mimeMessageHelper.setFrom(request.getFrom());
                mimeMessageHelper.setTo(request.getTo());
                mimeMessageHelper.setSubject(request.getSubject());
                mimeMessageHelper.setText(request.getText());
                mimeMessageHelper.addAttachment(request.getAttachmentFileName()
                        , new ByteArrayDataSource(byteArrayInputStream, request.getAttachmentType()));
                javaMailSender.send(mimeMessage);
                return true;
            }

            @Override
            protected Boolean onFailure(EmailSendRequest emailSendRequest, Throwable throwable) throws Throwable {
                log.error("[邮件发送]>>>>>>发送失败, 异常栈: ", throwable);
                return false;
            }
        });
    }
}
