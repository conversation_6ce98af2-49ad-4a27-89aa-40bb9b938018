package com.wosai.trade.optimus.export.infrastructure.plugin.mybatis;

import com.zaxxer.hikari.pool.HikariProxyPreparedStatement;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.sql.Statement;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2021/10/30 Time: 10:00 下午
 */
@Slf4j
@Intercepts(
        {
                @Signature(type = StatementHandler.class, method = "update", args = {Statement.class}),
                @Signature(type = StatementHandler.class, method = "query", args = {Statement.class, ResultHandler.class})
        }
)
public class LogRecordPlugin implements Interceptor {
    private static final String FIELD_H = "h";
    private static final String FIELD_STATEMENT = "statement";
    private static final String FIELD_DELEGATE = "delegate";
    private static final String METHOD_UPDATE = "update";
    private static final String METHOD_QUERY = "query";
    private static final long NANO_TO_MICRO_DIVISOR = 1000;
    private static final long NANO_TO_MILLI_DIVISOR = 1000 * 1000;



    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long beginTime = System.nanoTime();
        Object result = null;
        try {
            result = invocation.proceed();
        } finally {
            long endTime = System.nanoTime();
            long timeConsuming = endTime - beginTime;
            //记录提交到DB的原样sql和影响行数
            recordLogIgnoreException(invocation.getArgs(), invocation.getMethod()
                    , result, timeConsuming);
        }
        return result;
    }

    private void recordLogIgnoreException(Object[] args, Method method, Object result, long timeConsuming) {
        try {
            recordLog(args, method.getName(), result, timeConsuming);
        } catch (Throwable throwable) {
            try {
                log.error("[输出执行SQL]>>>>>>处理异常, 异常栈: ", throwable);
            } catch (Throwable ignored) {}
        }
    }

    private void recordLog(Object[] args, String methodName, Object result, long timeConsuming) throws Throwable {
        Object target = args[0];
        Object preparedStatement = getPreparedStatement(target);
        if (Objects.nonNull(preparedStatement)) {
            String preparedStatementStr = preparedStatement.toString();
            String formatterSql = preparedStatementStr
                    .replaceAll("\\s+", " ");

            int affectedRows = 0;
            if (Objects.equals(methodName, METHOD_UPDATE)
                    && Objects.nonNull(result) && result instanceof Integer) {
                affectedRows = (Integer) result;
            }
            if (Objects.equals(methodName, METHOD_QUERY)
                    && Objects.nonNull(result) && result instanceof List) {
                affectedRows = ((List<?>) result).size();
            }

            if (timeConsuming >= NANO_TO_MILLI_DIVISOR) {
                log.info("[输出执行SQL]>>>>>>SQL: {}, 影响行数: [{}], 耗时: [{}]ms"
                        , formatterSql, affectedRows, timeConsuming / NANO_TO_MILLI_DIVISOR);
            } else if (timeConsuming >= NANO_TO_MICRO_DIVISOR) {
                log.info("[输出执行SQL]>>>>>>SQL: {}, 影响行数: [{}], 耗时: [{}]us"
                        , formatterSql, affectedRows, timeConsuming / NANO_TO_MICRO_DIVISOR);
            } else {
                log.info("[输出执行SQL]>>>>>>SQL: {}, 影响行数: [{}], 耗时: [{}]ns"
                        , formatterSql, affectedRows, timeConsuming);
            }
        }
    }

    private Object getPreparedStatement(Object target) throws Throwable {
        if (Objects.nonNull(target)) {
            //jdk代理
            if (target instanceof Proxy) {
                Field h = target.getClass().getSuperclass().getDeclaredField(FIELD_H);
                h.setAccessible(true);
                Object invocationHandler = h.get(target);

                Field fieldStatement = invocationHandler.getClass().getDeclaredField(FIELD_STATEMENT);
                fieldStatement.setAccessible(true);
                Object statement =  fieldStatement.get(invocationHandler);

                //hikari
                if (statement instanceof HikariProxyPreparedStatement) {
                    return getPreparedStatementFromHikariProxy(statement);
                }
            }
            //hikari
            if (target instanceof HikariProxyPreparedStatement) {
                return getPreparedStatementFromHikariProxy(target);
            }


        }
        return null;
    }

    private Object getPreparedStatementFromHikariProxy(Object target) throws Throwable {
        Field fieldDelegate = target.getClass().getSuperclass().getSuperclass()
                .getDeclaredField(FIELD_DELEGATE);
        fieldDelegate.setAccessible(true);
        return fieldDelegate.get(target);
    }
}
