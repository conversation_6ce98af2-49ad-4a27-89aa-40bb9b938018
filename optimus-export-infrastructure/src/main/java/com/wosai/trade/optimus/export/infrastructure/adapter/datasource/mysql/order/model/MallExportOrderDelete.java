package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.model;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> Date: 2023/7/19 Time: 18:43
 */
@Getter
@Builder(toBuilder = true)
public class MallExportOrderDelete {
    private static final DateTimeFormatter EXPIRED_AT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private LocalDateTime expiredAt;
    private Long count;

    public String getExpiredAt() {
        return EXPIRED_AT_FORMATTER.format(expiredAt);
    }

}
