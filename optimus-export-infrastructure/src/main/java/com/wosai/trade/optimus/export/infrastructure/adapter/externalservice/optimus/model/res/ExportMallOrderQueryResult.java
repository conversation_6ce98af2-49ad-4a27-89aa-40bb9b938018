package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res;

import com.wosai.trade.optimus.api.result.mall.MallOrderDetailForInnerQueryResult;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR> Date: 2024/5/9 Time: 18:24
 */
@Getter
@Setter
@ToString(callSuper = true)
@Accessors(chain = true)
public class ExportMallOrderQueryResult {

    public static final List<String> DEFAULT_LIST = List.of();

    private MallOrderDetailForInnerQueryResult mallOrderDetailForInnerQueryResult;

    public String getStoreSn() {
        return mallOrderDetailForInnerQueryResult.getSeller().getStoreSn();
    }

    public Integer getState() {
        return mallOrderDetailForInnerQueryResult.getState();
    }

    public String getMerchantId() {
        return mallOrderDetailForInnerQueryResult.getSeller().getMerchantId();
    }

    public static ExportMallOrderQueryResult newInstance(MallOrderDetailForInnerQueryResult data) {
        ExportMallOrderQueryResult exportMallOrderQueryResult = new ExportMallOrderQueryResult();
        exportMallOrderQueryResult.setMallOrderDetailForInnerQueryResult(data);
        return exportMallOrderQueryResult;
    }
}
