package com.wosai.trade.optimus.export.infrastructure.plugin.mybatis;

import com.fasterxml.jackson.databind.JsonNode;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR> Date: 2023/1/6 Time: 11:19 AM
 */
@MappedTypes(JsonNode.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class JsonTypeHandler extends BaseTypeHandler<JsonNode> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, JsonNode jsonNode, JdbcType jdbcType) throws SQLException {
        preparedStatement.setString(i, jsonNode.toString());
    }

    @Override
    public JsonNode getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String resultJsonString = resultSet.getString(s);
        if (StringUtils.isNotEmpty(resultJsonString)) {
            return JsonUtils.toJsonNode(resultJsonString);
        }
        return null;
    }

    @Override
    public JsonNode getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String resultJsonString = resultSet.getString(i);
        if (StringUtils.isNotEmpty(resultJsonString)) {
            return JsonUtils.toJsonNode(resultJsonString);
        }
        return null;
    }

    @Override
    public JsonNode getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String resultJsonString = callableStatement.getNString(i);
        if (StringUtils.isNotEmpty(resultJsonString)) {
            return JsonUtils.toJsonNode(resultJsonString);
        }
        return null;
    }
}
