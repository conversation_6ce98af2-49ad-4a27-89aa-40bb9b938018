package com.wosai.trade.optimus.export.infrastructure.config;

import com.wosai.trade.optimus.export.infrastructure.config.support.DataSourceContextHolder;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Date: 2022/12/29 Time: 10:50 AM
 */
@Configuration
public class DataSourceConfig {

    @Bean(destroyMethod = "close")
    @ConfigurationProperties(prefix = "spring.datasource.core.hikari")
    public HikariDataSource coreDataSource() {
        return new HikariDataSource();
    }

    @Bean(destroyMethod = "close")
    @ConfigurationProperties(prefix = "spring.datasource.periphery.hikari")
    public HikariDataSource peripheryDataSource() {
        return new HikariDataSource();
    }

    @Bean
    @Primary
    public DataSource dynamicDataSource() {
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DataSourceContextHolder.DataSourceTypeEnum.CORE, coreDataSource());
        targetDataSources.put(DataSourceContextHolder.DataSourceTypeEnum.PERIPHERY, peripheryDataSource());

        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        dynamicDataSource.setTargetDataSources(targetDataSources);
        dynamicDataSource.setDefaultTargetDataSource(coreDataSource());

        return dynamicDataSource;
    }

    @Bean
    public TransactionTemplate transactionTemplate(
            PlatformTransactionManager transactionManager) {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        return transactionTemplate;
    }

    @Bean
    public TransactionTemplate requiresNewTransactionTemplate(
            PlatformTransactionManager transactionManager) {
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        return transactionTemplate;
    }



    private static class DynamicDataSource extends AbstractRoutingDataSource {

        @Override
        protected Object determineCurrentLookupKey() {
            return DataSourceContextHolder.getDataSourceKey();
        }
    }

}
