package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.sequence.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/7/2
 */
@Data
@Accessors(chain = true)
public class SequencePO {

    private Long id;
    private String namespace;
    private Long serialNoBase;
    private Integer batchSize;
    private LocalDateTime ctime;
    private LocalDateTime mtime;
    private Long version;
}
