package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.upay.common.bean.PageInfo;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.util.List;
import java.util.Map;

@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class MerchantQueryListRequest extends BaseRequest {

    private final MerchantListQueryParams queryParams;

    private final PageInfo pageInfo;

    public Map<?, ?> getMap() {
        return queryParams.toMap();
    }
    
    @Getter
    @Builder
    public static final class MerchantListQueryParams {
        @JsonProperty("merchant_sns")
        private final List<String> merchantSns;

        public Map<?, ?> toMap() {
            return JsonUtils.convertToObject(this, new TypeReference<>() {});
        }
    }


}
