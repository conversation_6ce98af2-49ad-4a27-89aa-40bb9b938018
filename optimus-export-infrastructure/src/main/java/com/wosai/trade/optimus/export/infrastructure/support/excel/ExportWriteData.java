package com.wosai.trade.optimus.export.infrastructure.support.excel;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR> Date: 2024/8/5 Time: 10:53
 */
@Getter
@SuperBuilder(toBuilder = true)
public class ExportWriteData extends ExportWriteUnit {

    private ExportWriteData(List<List<?>> data) {
        super(data);
    }

    public static ExportWriteData create() {
        return new ExportWriteData(Lists.newArrayList());
    }

    public static ExportWriteData create(List<List<?>> data) {
        return new ExportWriteData(data);
    }

}