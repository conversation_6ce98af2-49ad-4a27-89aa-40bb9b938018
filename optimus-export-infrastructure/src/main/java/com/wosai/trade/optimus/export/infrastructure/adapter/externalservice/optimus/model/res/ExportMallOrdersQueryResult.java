package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.wosai.trade.optimus.api.model.res.MallInfoModel;
import com.wosai.trade.optimus.api.model.res.OptimusOrdersSellerModel;
import com.wosai.trade.optimus.api.result.mall.MallOrdersForInnerQueryResult;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view.MallOrderDetailItemView;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view.MallOrderDetailView;
import com.wosai.trade.order.api.result.model.OrderAmountModel;
import com.wosai.trade.order.api.result.model.OrderInvoiceSimpleModel;
import lombok.Builder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * <AUTHOR> Date: 2024/8/5 Time: 15:57
 */
@Builder
public class ExportMallOrdersQueryResult {
    public static final ExportMallOrdersQueryResult DEFAULT_EMPTY_INSTANCE = ExportMallOrdersQueryResult.builder()
            .orders(List.of())
            .build();


    private final Integer querySize;
    private final List<ExportOrdersModel> orders;


    public static ExportMallOrdersQueryResult newInstance(Integer querySize, List<MallOrdersForInnerQueryResult> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return DEFAULT_EMPTY_INSTANCE;
        }
        return ExportMallOrdersQueryResult.builder()
                .querySize(querySize)
                .orders(JsonUtils.convertToObject(orders, new TypeReference<>() {}))
                .build();
    }


    public boolean isOrderNotExist() {
        return this == DEFAULT_EMPTY_INSTANCE || CollectionUtils.isEmpty(orders);
    }

    public boolean isOrderExist() {
        return !isOrderNotExist();
    }

    public boolean isQueryEnd() {
       return (Objects.nonNull(querySize) && querySize > orders.size()) || isOrderNotExist();
    }

    public String getTailOrderSn() {
        return orders.get(orders.size() - 1).getOrderSn();
    }

    public MallOrderDetailView genOrderFileMallDetailView() {
        MallOrderDetailView.OrderFileViewBuilder builder = MallOrderDetailView.builder();
        if (CollectionUtils.isNotEmpty(orders)) {
            Map<Integer, String> sceneFileHeaders = null;
            Map<Integer, String> floatFileHeaders = null;
            for (ExportOrdersModel order : orders) {
                //改造后单次只能导出一个商城的订单，场景信息文件头是相同的，不重复生成
                if (MapUtils.isEmpty(sceneFileHeaders)) {
                    sceneFileHeaders = order.getSceneFieldExpandedNames();
                    builder.sceneFileHeaders(sceneFileHeaders);
                }
                if (MapUtils.isEmpty(floatFileHeaders)) {
                    floatFileHeaders = order.genFloatFileHeaders();
                    builder.floatFileHeaders(floatFileHeaders);
                }

                builder.item(order.genOrderFileItemView())
                        .sceneContentItem(order.getSceneFieldExpandedContents())
                        .floatContentItem(order.genFloatContent());
            }
        }
        return builder.build();
    }

    public static class ExportOrdersModel extends MallOrdersForInnerQueryResult {
        private static final Pair<Integer/*电子发票处于浮动头的位置*/, String> INVOICE_HEADER = Pair.of(0, "电子发票开具");

        public MallOrderDetailItemView genOrderFileItemView() {
            OrderAmountModel amount = getAmount();
            Long refundedAmount = amount.getRefundedAmount();
            OptimusOrdersSellerModel seller = getSeller();
            MallInfoModel mallInfo = getMall();
            return MallOrderDetailItemView.builder()
                    .tradeTime(getTime().getCreatedAt())
                    .orderSn(getOrderSn())
                    .payTsnList(getPayTsnList())
                    .merchantSn(seller.getMerchantSn())
                    .merchantName(seller.getMerchantName())
                    .storeSn(seller.getStoreSn())
                    .storeName(seller.getStoreName())
                    .mallSn(mallInfo.getMallSn())
                    .mallName(mallInfo.getMallName())
                    .orderAmount(amount.getOriAmount())
                    .discountAmount(getDiscountAmount(amount))
                    .receivedAmount(amount.getReceivedAmount())
                    .isReturned(Objects.nonNull(refundedAmount) && refundedAmount > 0)
                    .orderStateDesc(getStateDesc())
                    .orderItems(Optional.ofNullable(getItem().getItems())
                            .map(list -> list.stream()
                                    .map(item -> MallOrderDetailItemView.ExportOrderItem.builder()
                                            .title(item.getTitle())
                                            .skuDesc(item.getSkuDesc())
                                            .quantity(item.getQuantity())
                                            .unit(item.getUnit())
                                            .build())
                                    .toList())
                            .orElse(Collections.emptyList()))
                    .build();
        }

        private long getDiscountAmount(OrderAmountModel amount) {
            long result = 0;

            if (Objects.nonNull(amount.getOrderDiscAmount())) {
                result += amount.getOrderDiscAmount();
            }

            if (Objects.nonNull(amount.getPaymentDiscAmount())) {
                result += amount.getPaymentDiscAmount();
            }
            return result;
        }

        public Map<Integer, String> genFloatFileHeaders() {
            Map<Integer, String> floatHeaders = Maps.newHashMap();
            if (isExistInvoiceStatus()) {
                floatHeaders.put(INVOICE_HEADER.getKey(), INVOICE_HEADER.getValue());
            }
            return floatHeaders;
        }

        public Map<Integer, String> genFloatContent() {
            Map<Integer, String> floatContent = Maps.newHashMap();
            if (isExistInvoiceStatus()) {
                floatContent.put(INVOICE_HEADER.getKey(), getInvoiceStatusDesc());
            }
            return floatContent;
        }

        private boolean isExistInvoiceStatus() {
            return StringUtils.isNotEmpty(getInvoiceStatusDesc());
        }

        private String getInvoiceStatusDesc() {
            return Optional.ofNullable(getInvoice())
                    .map(OrderInvoiceSimpleModel::getInvoiceStatusDesc).orElse(StringUtils.EMPTY);
        }
    }

}
