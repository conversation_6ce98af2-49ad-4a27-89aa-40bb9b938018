package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.kafka;


import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.kafka.po.KafkaPO;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Date: 2023/1/3 Time: 11:05 AM
 */
public interface KafkaDao {

    void sendAsync(KafkaPO po);

    void batchSendAsync(List<KafkaPO> poList);

    void sendSync(KafkaPO po, Long timeout, TimeUnit timeUnit);

    void sendAsyncOutsideOfTransaction(KafkaPO po);

    void batchSendAsyncOutsideOfTransaction(List<KafkaPO> poList);

}
