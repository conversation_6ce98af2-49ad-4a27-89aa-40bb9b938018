package com.wosai.trade.optimus.export.infrastructure.support;

import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.kvconfig.KVConfigDao;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.kvconfig.po.KVConfigPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.ListConsumerGroupOffsetsResult;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2023/1/16 Time: 3:20 PM
 */
@Slf4j
@Component
public class KafkaRateLimit {

    private static final String CONFIG_KEY = "kafka_produce_limit_threshold";
    private static final long STOP_PRODUCE_THRESHOLD = 2000;
    private static final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE
            = Executors.newSingleThreadScheduledExecutor();

    private volatile boolean isCrowded;
    private volatile long totalLag;

    @Resource
    private KVConfigDao kvConfigDao;
    @Value("${kafka.monitor.enable}")
    private boolean enable;
    @Value("${spring.kafka.producer.topic.module}")
    private String eventTopic;


    @Resource
    private KafkaConsumer<?, ?> monitorKafkaConsumer;
    @Resource
    private AdminClient adminClient;
    @Value("${spring.kafka.consumer.group-id}")
    private String groupId;


    @PostConstruct
    public void init() {
        if (enable) {
            SCHEDULED_EXECUTOR_SERVICE.scheduleWithFixedDelay(() -> {
                        try {
                            try {
                                long limitThreshold;
                                KVConfigPO kvConfigPO = kvConfigDao.query(CONFIG_KEY);
                                if (Objects.nonNull(kvConfigPO)) {
                                    limitThreshold = Long.parseLong(kvConfigPO.getValue().toString());
                                } else {
                                    limitThreshold = STOP_PRODUCE_THRESHOLD;
                                }

                                ListConsumerGroupOffsetsResult result = adminClient
                                        .listConsumerGroupOffsets(groupId);
                                Map<TopicPartition, OffsetAndMetadata> consumedOffsets
                                        = result.partitionsToOffsetAndMetadata()
                                        .get(10, TimeUnit.SECONDS);
                                Map<TopicPartition, Long> endOffsets = monitorKafkaConsumer
                                        .endOffsets(consumedOffsets.keySet());
                                Map<TopicPartition, Long> partitionLagMap = endOffsets.entrySet().stream()
                                        .filter(entry -> Objects.equals(entry.getKey().topic(), eventTopic))
                                        .collect(Collectors.toMap(Map.Entry::getKey
                                                , entry -> entry.getValue() - consumedOffsets
                                                        .get(entry.getKey()).offset()));
                                if (MapUtils.isNotEmpty(partitionLagMap)) {
                                    long totalLag = partitionLagMap.values().stream()
                                            .mapToLong(Long::longValue).sum();
                                    this.totalLag = totalLag;
                                    isCrowded = totalLag > limitThreshold;
                                    log.debug("[MQ限流工具]>>>>>>获取分区Lag: {}, 汇总Lag: {}"
                                            , partitionLagMap, totalLag);
                                }
                            } catch (Throwable t) {
                                this.totalLag = 0L;
                                isCrowded = false;
                                log.error("[MQ限流工具]>>>>>>获取分区Lag异常, 异常栈: ", t);
                            }
                        } catch (Throwable ignored) {}
                    }
                    , 0, 1, TimeUnit.SECONDS);
        }
    }

    @PreDestroy
    private void destroy() {
        adminClient.close();
    }

    public boolean isCrowded() {
        return isCrowded;
    }

}
