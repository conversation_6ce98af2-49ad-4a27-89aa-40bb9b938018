package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.kvconfig.po;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import lombok.Data;

/**
 * <AUTHOR> Date: 2023/4/22 Time: 9:32 PM
 */
@Data
public class KVConfigPO {

    private Long id;
    private String key;
    private Object value;

    public <T> T genTypeValue(Class<T> tClass) {
        return JsonUtils.convertToObject(value, tClass);
    }

    public <T> T genTypeValue(TypeReference<T> reference) {
        return JsonUtils.convertToObject(value, reference);
    }
}
