package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.volcano;


import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.template.InvokeProcessor;
import com.wosai.trade.optimus.export.common.template.WithoutResultTemplate;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.kafka.KafkaDao;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.kafka.po.KafkaPO;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.volcano.mode.EventReportRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Date: 2023/8/25 Time: 11:31
 */
@Component
public class VolcanoClient {

    @Resource
    private KafkaDao kafkaDao;

    @Value("${spring.kafka.producer.topic.volcano}")
    private String volcanoTopic;


    public void reportEventAsync(EventReportRequest request) {
        InvokeProcessor.processWithoutResult(request, new WithoutResultTemplate<>() {
            @Override
            protected void invoke(EventReportRequest request) throws Throwable {
                kafkaDao.sendAsync(new KafkaPO().setTopic(volcanoTopic).setData(request.toJsonString()));
            }

            @Override
            protected void onBizFailure(EventReportRequest eventReportRequest, OptimusExportBizException e) {}

            @Override
            protected void onFailure(EventReportRequest eventReportRequest, Throwable throwable) throws Throwable {}
        });
    }

    public void reportEventSync(EventReportRequest request) {
        InvokeProcessor.processWithoutResult(request, new WithoutResultTemplate<>() {
            @Override
            protected void invoke(EventReportRequest request) throws Throwable {
                kafkaDao.sendSync(new KafkaPO().setTopic(volcanoTopic).setData(request.toJsonString())
                        , 5L, TimeUnit.SECONDS);
            }
        });
    }

}
