package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus;

import com.wosai.general.result.MultiResult;
import com.wosai.general.result.SingleResult;
import com.wosai.trade.optimus.api.OptimusAftersaleService;
import com.wosai.trade.optimus.api.OptimusMallService;
import com.wosai.trade.optimus.api.OptimusOrderService;
import com.wosai.trade.optimus.api.request.aftersale.MallTicketsForInnerQueryRequest;
import com.wosai.trade.optimus.api.request.mall.MallListQueryForInnerRequest;
import com.wosai.trade.optimus.api.request.mall.MallOrderDetailForInnerQueryRequest;
import com.wosai.trade.optimus.api.request.mall.MallOrdersForInnerQueryRequest;
import com.wosai.trade.optimus.api.result.aftersale.MallTicketsForInnerQueryResult;
import com.wosai.trade.optimus.api.result.mall.MallListQueryForInnerResult;
import com.wosai.trade.optimus.api.result.mall.MallOrderDetailForInnerQueryResult;
import com.wosai.trade.optimus.api.result.mall.MallOrdersForInnerQueryResult;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.template.InvokeProcessor;
import com.wosai.trade.optimus.export.common.template.InvokeTemplate;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req.ExportMallListQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req.ExportMallOrderQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req.ExportMallOrdersQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req.ExportMallTicketsQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallListQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallOrderQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallOrdersQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res.ExportMallTicketsQueryResult;
import com.wosai.trade.order.api.request.model.CursorModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

import static com.wosai.trade.optimus.export.common.util.JsonUtils.toJsonStringIgnoreException;

/**
 * <AUTHOR> Date: 2024/5/9 Time: 12:11
 */
@Slf4j
@Component
public class OptimusCoreClient {
    private static final String OPTIMUS_CORE_ERR_CODE_PREFIX = "optimus-";

    @Resource
    private OptimusOrderService optimusOrderService;
    @Resource
    private OptimusMallService optimusMallService;
    @Resource
    private OptimusAftersaleService optimusAftersaleService;

    public ExportMallOrderQueryResult queryMallOrder(ExportMallOrderQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected ExportMallOrderQueryResult invoke(ExportMallOrderQueryRequest request)  {
                MallOrderDetailForInnerQueryRequest MallOrderDetailForInnerQueryRequest = request
                        .genMallOrderDetailForInnerQueryRequest();
                log.info("[商城中心查询订单明细]>>>>>>入参: {}"
                        , toJsonStringIgnoreException(MallOrderDetailForInnerQueryRequest));
                SingleResult<MallOrderDetailForInnerQueryResult> singleResult = optimusOrderService
                        .queryOrderDetailForInner(MallOrderDetailForInnerQueryRequest);
                log.info("[商城中心查询订单明细]>>>>>>出参: {}"
                        , toJsonStringIgnoreException(singleResult));
                if (Objects.isNull(singleResult)) {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.EXTERNAL_SERVICE_RESULT_NOT_EXIST);
                }
                if (singleResult.isSuccess()) {
                    MallOrderDetailForInnerQueryResult data = singleResult.getData();
                    return ExportMallOrderQueryResult.newInstance(data);
                }
                throw new OptimusExportBizException(OPTIMUS_CORE_ERR_CODE_PREFIX + singleResult.getCode(), singleResult.getMsg());
            }
        });
    }

    public ExportMallListQueryResult queryMallList(ExportMallListQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected ExportMallListQueryResult invoke(ExportMallListQueryRequest request)  {
                MallListQueryForInnerRequest mallQueryRequest = request.genMallListQueryRequest();
                log.info("[商城中心查询商城信息]>>>>>>入参: {}"
                        , toJsonStringIgnoreException(mallQueryRequest));
                MultiResult<MallListQueryForInnerResult> resultMultiResult = optimusMallService
                        .queryMallListForInner(mallQueryRequest);
                log.info("[商城中心查询商城信息]>>>>>>出参: {}"
                        , toJsonStringIgnoreException(resultMultiResult));
                if (Objects.isNull(resultMultiResult)) {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.EXTERNAL_SERVICE_RESULT_NOT_EXIST);
                }
                if (resultMultiResult.isSuccess()) {
                    return ExportMallListQueryResult.newInstance(resultMultiResult.getData());
                }
                throw new OptimusExportBizException(OPTIMUS_CORE_ERR_CODE_PREFIX + resultMultiResult.getCode(), resultMultiResult.getMsg());
            }
        });
    }

    public ExportMallOrdersQueryResult queryMallOrders(ExportMallOrdersQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected ExportMallOrdersQueryResult invoke(ExportMallOrdersQueryRequest request) throws Throwable {
                MallOrdersForInnerQueryRequest queryRequest = request.genMallOrdersForInnerQueryRequest();
                log.info("[商城中心查询商城订单列表]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(queryRequest));
                MultiResult<MallOrdersForInnerQueryResult> result = optimusOrderService.queryMallOrdersForInner(queryRequest);
                log.info("[商城中心查询商城订单列表]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                if (Objects.isNull(result)) {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.EXTERNAL_SERVICE_RESULT_NOT_EXIST);
                }
                if (result.isSuccess()) {
                    return ExportMallOrdersQueryResult.newInstance(Optional.ofNullable(queryRequest.getCursor())
                                    .map(CursorModel::getCount).orElse(null)
                            , result.getData());
                }
                throw new OptimusExportBizException(OPTIMUS_CORE_ERR_CODE_PREFIX + result.getCode(), result.getMsg());
            }
        });
    }

    public ExportMallTicketsQueryResult queryMallTickets(ExportMallTicketsQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected ExportMallTicketsQueryResult invoke(ExportMallTicketsQueryRequest request) throws Throwable {
                MallTicketsForInnerQueryRequest queryRequest = request.genMallTicketsForInnerQueryRequest();
                log.info("[商城中心查询商城售后单列表]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(queryRequest));
                MultiResult<MallTicketsForInnerQueryResult> result = optimusAftersaleService.queryMallTicketsForInner(queryRequest);
                log.info("[商城中心查询商城售后单列表]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                if (Objects.isNull(result)) {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.EXTERNAL_SERVICE_RESULT_NOT_EXIST);
                }
                if (result.isSuccess()) {
                    return ExportMallTicketsQueryResult.newInstance(Optional.ofNullable(queryRequest.getCursor())
                            .map(com.shouqianba.trade.aftersale.api.request.model.CursorModel::getCount).orElse(null)
                    , result.getData());
                }
                throw new OptimusExportBizException(OPTIMUS_CORE_ERR_CODE_PREFIX + result.getCode(), result.getMsg());
            }
        });
    }

}
