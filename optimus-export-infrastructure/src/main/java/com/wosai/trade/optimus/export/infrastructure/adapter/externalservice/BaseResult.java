package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> Date: 2022/12/12 Time: 4:22 PM
 */
public abstract class BaseResult {

    public static <T> T genFromJsonString(String jsonString, TypeReference<T> typeReference) {
        if (StringUtils.isNotEmpty(jsonString)) {
            return JsonUtils.parseObject(jsonString, typeReference);
        }
        throw new OptimusExportBizException(OptimusExportRespCodeEnum.EXTERNAL_SERVICE_RESULT_NOT_EXIST);
    }

    @JsonIgnore
    public abstract boolean isInvokeSuccess();

    @JsonIgnore
    public abstract boolean isSuccess();

}
