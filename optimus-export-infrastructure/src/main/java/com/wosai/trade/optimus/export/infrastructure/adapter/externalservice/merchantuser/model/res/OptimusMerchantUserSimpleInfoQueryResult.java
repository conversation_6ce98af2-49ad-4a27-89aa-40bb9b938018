package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.res;

import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> Date: 2024/5/16 Time: 10:12
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class OptimusMerchantUserSimpleInfoQueryResult {

    public final static OptimusMerchantUserSimpleInfoQueryResult DEFAULT = OptimusMerchantUserSimpleInfoQueryResult.builder().build();


    private List<UcMerchantUserSimpleInfo> merchantUserSimpleInfo;

    public static OptimusMerchantUserSimpleInfoQueryResult newInstance(List<UcMerchantUserSimpleInfo> merchantUserSimpleInfo) {
        return OptimusMerchantUserSimpleInfoQueryResult.builder()
                .merchantUserSimpleInfo(merchantUserSimpleInfo)
                .build();
    }

    public List<String> getUUserIdList() {
        if (CollectionUtils.isNotEmpty(merchantUserSimpleInfo)) {

            return merchantUserSimpleInfo.stream()
                    .map(UcMerchantUserSimpleInfo::getUc_user_id).toList();
        }

        return List.of();
    }

}
