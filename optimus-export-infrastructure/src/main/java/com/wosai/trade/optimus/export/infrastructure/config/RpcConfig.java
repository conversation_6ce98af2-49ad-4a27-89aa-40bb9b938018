package com.wosai.trade.optimus.export.infrastructure.config;

import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.shouqianba.trade.aftersale.api.TicketQueryService;
import com.wosai.app.service.GroupService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.trade.optimus.api.OptimusAftersaleService;
import com.wosai.trade.optimus.api.OptimusMallService;
import com.wosai.trade.optimus.api.OptimusOrderService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import com.wosai.upay.task.center.service.TaskLogService;

/**
 * <AUTHOR> Date: 2023/6/14 Time: 11:05 AM
 */
@Configuration
public class RpcConfig {

    @Bean
    public JsonProxyFactoryBean optimusOrderService(@Value("${service.rpc.optimus-core}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/order");
        jsonProxyFactoryBean.setServiceInterface(OptimusOrderService.class);
        jsonProxyFactoryBean.setServerName("optimus-core");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean OptimusAftersaleService(@Value("${service.rpc.optimus-core}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/aftersale");
        jsonProxyFactoryBean.setServiceInterface(OptimusAftersaleService.class);
        jsonProxyFactoryBean.setServerName("optimus-core");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean merchantService(@Value("${service.rpc.core-business}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/merchant");
        jsonProxyFactoryBean.setServiceInterface(MerchantService.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean storeService(@Value("${service.rpc.core-business}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/store");
        jsonProxyFactoryBean.setServiceInterface(StoreService.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean supportService(@Value("${service.rpc.core-business}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/support");
        jsonProxyFactoryBean.setServiceInterface(SupportService.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean merchantUserServiceV2(@Value("${service.rpc.merchant-user-service}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/merchantuserV2");
        jsonProxyFactoryBean.setServiceInterface(MerchantUserServiceV2.class);
        jsonProxyFactoryBean.setServerName("merchant-user-service");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean optimusMallService(@Value("${service.rpc.optimus-core}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/mall");
        jsonProxyFactoryBean.setServiceInterface(OptimusMallService.class);
        jsonProxyFactoryBean.setServerName("optimus-core");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean terminalService(@Value("${service.rpc.core-business}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/terminal");
        jsonProxyFactoryBean.setServiceInterface(TerminalService.class);
        jsonProxyFactoryBean.setServerName("core-business");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean GroupService(@Value("${service.rpc.merchant-user-service}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/group");
        jsonProxyFactoryBean.setServiceInterface(GroupService.class);
        jsonProxyFactoryBean.setServerName("merchant-user-service");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean taskLogService(@Value("${service.rpc.upay-task-center}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/task");
        jsonProxyFactoryBean.setServiceInterface(TaskLogService.class);
        jsonProxyFactoryBean.setServerName("upay-task-center");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

    @Bean
    public JsonProxyFactoryBean ticketQueryService(@Value("${service.rpc.trade-aftersale}") String url) {
        JsonProxyFactoryBean jsonProxyFactoryBean = new JsonProxyFactoryBean();
        jsonProxyFactoryBean.setServiceUrl(url + "/rpc/query");
        jsonProxyFactoryBean.setServiceInterface(TicketQueryService.class);
        jsonProxyFactoryBean.setServerName("trade-aftersale-service");
        jsonProxyFactoryBean.setConnectionTimeoutMillis(1000);
        jsonProxyFactoryBean.setReadTimeoutMillis(3000);

        return jsonProxyFactoryBean;
    }

}
