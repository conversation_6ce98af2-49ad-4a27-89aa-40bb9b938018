package com.wosai.trade.optimus.export.infrastructure.config;

import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;
import okhttp3.internal.Util;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.concurrent.*;

/**
 * <AUTHOR> Date: 2023/4/25 Time: 4:52 PM
 */
@Slf4j
@Configuration
@EnableConfigurationProperties({OkHttpConfig.OkHttpPeripheryProperties.class
        , OkHttpConfig.OkHttpCoreProperties.class})
public class OkHttpConfig {
    private static final Pair<Long, TimeUnit> PERIPHERY_CONNECTION_KEEP_ALIVE_DURATION
            = Pair.of(5L, TimeUnit.MINUTES);
    private static final Pair<Long, TimeUnit> CORE_CONNECTION_KEEP_ALIVE_DURATION
            = Pair.of(5L, TimeUnit.MINUTES);

    private final ExecutorService peripheryExecutorService;
    private final ExecutorService coreExecutorService;

    private final OkHttpPeripheryProperties okHttpPeripheryProperties;
    private final OkHttpCoreProperties okHttpCoreProperties;

    public OkHttpConfig(OkHttpPeripheryProperties okHttpPeripheryProperties
            , OkHttpCoreProperties okHttpCoreProperties) {
        this.okHttpPeripheryProperties = okHttpPeripheryProperties;
        this.okHttpCoreProperties = okHttpCoreProperties;

        peripheryExecutorService = new ThreadPoolExecutor(okHttpPeripheryProperties.getCorePoolSize()
                , okHttpPeripheryProperties.getMaxPoolSize(), 1
                , TimeUnit.HOURS, new ArrayBlockingQueue<>(
                Math.round(okHttpPeripheryProperties.getCorePoolSize() * 0.75f), true)
                , Util.threadFactory("OkHttp-Periphery-Dispatcher", false));
        coreExecutorService = new ThreadPoolExecutor(okHttpCoreProperties.getCorePoolSize()
                , okHttpCoreProperties.getMaxPoolSize(), 1024
                , TimeUnit.DAYS, new SynchronousQueue<>(true)
                , Util.threadFactory("OkHttp-Core-Dispatcher", false));
    }

    @Bean
    public OkHttpClient okHttpPeripheryClient() {
        Dispatcher dispatcher = new Dispatcher(peripheryExecutorService);
        dispatcher.setMaxRequests(okHttpPeripheryProperties.getMaxPoolSize());
        dispatcher.setMaxRequestsPerHost(okHttpPeripheryProperties.getMaxPoolSize());

        return new OkHttpClient.Builder().dispatcher(dispatcher)
                .callTimeout(okHttpPeripheryProperties.getCallTimeout(), TimeUnit.MILLISECONDS)
                .connectionPool(peripheryConnectionPool())
                .hostnameVerifier(((s, sslSession) -> true))
                .sslSocketFactory(sslSocketFactory(), x509TrustManager())
                .build();
    }

    @Bean
    public OkHttpClient okHttpCoreClient() {
        Dispatcher dispatcher = new Dispatcher(coreExecutorService);
        dispatcher.setMaxRequests(okHttpCoreProperties.getMaxPoolSize());
        dispatcher.setMaxRequestsPerHost(okHttpCoreProperties.getMaxPoolSize());

        return new OkHttpClient.Builder().dispatcher(dispatcher)
                .callTimeout(okHttpCoreProperties.getCallTimeout(), TimeUnit.MILLISECONDS)
                .connectionPool(coreConnectionPool())
                .hostnameVerifier(((s, sslSession) -> true))
                .sslSocketFactory(sslSocketFactory(), x509TrustManager())
                .build();
    }

    @Bean
    public X509TrustManager x509TrustManager() {
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] x509Certificates
                    , String s) throws CertificateException {

            }

            @Override
            public void checkServerTrusted(X509Certificate[] x509Certificates
                    , String s) throws CertificateException {

            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[0];
            }
        };
    }

    @Bean
    public SSLSocketFactory sslSocketFactory() {
        SSLContext sslContext;
        try {
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{x509TrustManager()}, new SecureRandom());
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            log.error("[okhttp ssl上下文创建]>>>>>>密钥生成异常, 异常栈: ", e);
            throw new OptimusExportBizException(e);
        }
        return sslContext.getSocketFactory();
    }

    @Bean
    public ConnectionPool peripheryConnectionPool() {
        return new ConnectionPool(okHttpPeripheryProperties.getMaxPoolSize() * 2
                , PERIPHERY_CONNECTION_KEEP_ALIVE_DURATION.getLeft()
                , PERIPHERY_CONNECTION_KEEP_ALIVE_DURATION.getRight());
    }

    @Bean
    public ConnectionPool coreConnectionPool() {
        return new ConnectionPool(okHttpCoreProperties.getMaxPoolSize() * 2
                , CORE_CONNECTION_KEEP_ALIVE_DURATION.getLeft()
                , CORE_CONNECTION_KEEP_ALIVE_DURATION.getRight());
    }

    @Data
    @ConfigurationProperties(prefix = "ok.http.periphery")
    public static class OkHttpPeripheryProperties {
        private Integer corePoolSize;
        private Integer maxPoolSize;
        private Long callTimeout;
    }

    @Data
    @ConfigurationProperties(prefix = "ok.http.core")
    public static class OkHttpCoreProperties {
        private Integer corePoolSize;
        private Integer maxPoolSize;
        private Long callTimeout;
    }

}
