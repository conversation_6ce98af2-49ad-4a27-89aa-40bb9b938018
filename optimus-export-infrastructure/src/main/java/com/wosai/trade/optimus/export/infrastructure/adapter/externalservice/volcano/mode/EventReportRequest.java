package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.volcano.mode;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.trade.optimus.export.infrastructure.util.PropertiesUtils;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> Date: 2023/8/25 Time: 11:35
 */
@SuperBuilder(toBuilder = true)
public class EventReportRequest extends BaseRequest {
    private static final String MESSAGE_TYPE = "EVENT";

    @JsonProperty("message_type")
    private final String messageType;
    @JsonProperty("user_unique_id")
    private final String userUniqueId;
    @JsonProperty("app_id")
    private final Integer appId;
    @JsonProperty("event_name")
    private final String eventName;
    @JsonProperty("event_params")
    private final Object eventParams;

    public static EventReportRequest newInstance(String merchantId
            , String eventName
            , Object params) {
        return EventReportRequest.builder()
                .messageType(MESSAGE_TYPE)
                .userUniqueId(merchantId)
                .appId(PropertiesUtils.getInstance().getVolcanoAppId())
                .eventName(eventName)
                .eventParams(params)
                .build();
    }

}
