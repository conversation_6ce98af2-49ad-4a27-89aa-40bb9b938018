package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/14
 */
@Data
@Accessors(chain = true)
public class MerchantQueryResult {

    @JsonProperty("id")
    private String merchantId;
    @JsonProperty("sn")
    private String merchantSn;
    @JsonProperty("name")
    private String merchantName;
    @JsonProperty("business_name")
    private String businessName;
    @JsonProperty("industry")
    private String industry;
    @JsonProperty("contact_cellphone")
    private String contactCellphone;

    public boolean isNotExist() {
        return StringUtils.isEmpty(merchantSn) || StringUtils.isEmpty(merchantSn);
    }

    public void checkExist() {
        if (isNotExist()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.MERCHANT_INFO_NOT_FOUND);
        }
    }

    @JsonIgnore
    public static Map<String, String> genSnNameMap(List<MerchantQueryResult> results) {
        if(CollectionUtils.isEmpty(results)){
            return Map.of();
        }
        return results.stream()
                .collect(Collectors.toMap(
                        MerchantQueryResult::getMerchantSn,
                        MerchantQueryResult::getMerchantName,
                        (v1 ,v2) -> v2));

    }

}
