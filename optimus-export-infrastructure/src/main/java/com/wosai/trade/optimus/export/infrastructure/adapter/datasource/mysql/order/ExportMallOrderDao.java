package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order;


import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.model.MallExportOrderDelete;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.model.ExportMallOrderQuery;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.po.ExportMallOrderPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR> Date: 2023/7/16 Time: 17:21
 */
@Mapper
public interface ExportMallOrderDao {

    void insert(ExportMallOrderPO po);

    int update(ExportMallOrderPO po);

    ExportMallOrderPO select(ExportMallOrderQuery query);

    List<ExportMallOrderPO> batchSelect(ExportMallOrderQuery query);

    long count(ExportMallOrderQuery query);

    int delete(MallExportOrderDelete delete);

}
