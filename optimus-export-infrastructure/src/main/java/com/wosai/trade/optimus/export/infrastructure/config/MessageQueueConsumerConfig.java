package com.wosai.trade.optimus.export.infrastructure.config;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.listener.CommonErrorHandler;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.kafka.support.ExponentialBackOffWithMaxRetries;
import org.springframework.util.backoff.BackOff;


@Slf4j
@Configuration
@Import(KafkaAutoConfiguration.class)
@EnableConfigurationProperties({ KafkaProperties.class })
public class MessageQueueConsumerConfig {

    @Value("${spring.kafka.producer.topic.module}")
    private String optimusEventTopic;

    private final KafkaProperties kafkaProperties;

    public MessageQueueConsumerConfig(KafkaProperties kafkaProperties) {
        this.kafkaProperties = kafkaProperties;
    }

    @Bean
    public KafkaConsumer<?, ?> monitorKafkaConsumer() {
        KafkaConsumer<?, ?> consumer = new KafkaConsumer<>(kafkaProperties.buildConsumerProperties());
        consumer.subscribe(Lists.newArrayList(optimusEventTopic));
        return consumer;
    }

    @Bean
    public AdminClient adminClient() {
        return AdminClient.create(kafkaProperties.buildAdminProperties());
    }

    @Bean
    public CommonErrorHandler contractErrorHandler() {
        //消费异常重试，最多10次
        BackOff backOff = new ExponentialBackOffWithMaxRetries(6);
        return new DefaultErrorHandler(backOff);
    }


}
