package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.req;

import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> Date: 2023/12/1 Time: 17:56
 */
@Getter
@ToString
@SuperBuilder(toBuilder = true)
public class MerchantTradeParamsQueryRequest extends BaseRequest {

    private final String storeSn;
    private final Integer payToolCode;
    private final Integer payMode;
    private final String tradeApp;

}
