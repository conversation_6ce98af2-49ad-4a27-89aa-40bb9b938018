package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req;

import com.wosai.trade.optimus.api.request.mall.MallOrderDetailForInnerQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.trade.order.api.request.model.OrderIDModel;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR> Date: 2024/5/9 Time: 18:15
 */
@Getter
@SuperBuilder(toBuilder = true)
@ToString(callSuper = true)
@Jacksonized
public class ExportMallOrderQueryRequest extends BaseRequest {
    private final String orderSn;
    private final String orderSignature;



    public MallOrderDetailForInnerQueryRequest genMallOrderDetailForInnerQueryRequest() {
        return MallOrderDetailForInnerQueryRequest.builder()
                .orderID(OrderIDModel.builder()
                        .sn(orderSn)
                        .signature(orderSignature)
                        .build())
                .build();
    }
}
