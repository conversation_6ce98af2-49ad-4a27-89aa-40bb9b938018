package com.wosai.trade.optimus.export.infrastructure.repository.domain;


import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.ExportOrderDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.query.ExportMallOrderAggrQuery;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.ExportMallOrderDao;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.model.ExportMallOrderQuery;
import com.wosai.trade.optimus.export.infrastructure.repository.domain.converter.ExportMallOrderAggrRootConverter;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Date: 2023/7/18 Time: 13:43
 */
@Repository
public class ExportOrderDomainRepositoryImpl implements ExportOrderDomainRepository {

    @Resource
    private ExportMallOrderDao exportMallOrderDao;
    @Resource
    private ExportMallOrderAggrRootConverter converter;

    @Override
    public void save(ExportOrderAggrRoot aggrRoot) {
        if (aggrRoot.isNeedAdd()) {
            exportMallOrderDao.insert(converter.toMallExportOrderPO(aggrRoot));
        } else if (aggrRoot.isNeedModify()) {
            int affectRow = exportMallOrderDao.update(converter.toMallExportOrderPO(aggrRoot));
            if (affectRow <= 0) {
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.CONCURRENT_MODIFY_ERROR);
            }
        }
        aggrRoot.clearMark();
    }

    @Override
    public ExportOrderAggrRoot query(ExportMallOrderAggrQuery aggrQuery) {
        return converter.toMallExportOrderAggrRoot(exportMallOrderDao.select(ExportMallOrderQuery
                .genMallExportOrderQuery(aggrQuery)));
    }

    @Override
    public List<ExportOrderAggrRoot> queryList(ExportMallOrderAggrQuery aggrQuery) {
        return converter.toMallExportOrderAggrRoots(exportMallOrderDao
                .batchSelect(ExportMallOrderQuery.genMallExportOrderQuery(aggrQuery)));
    }

    @Override
    public long count(ExportMallOrderAggrQuery aggrQuery) {
        return exportMallOrderDao.count(ExportMallOrderQuery.genMallExportOrderQuery(aggrQuery));
    }
}
