package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req;

import com.wosai.trade.optimus.api.request.BaseMallRequest;
import com.wosai.trade.optimus.api.request.mall.MallQueryRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 12:06
 */
@Getter
@SuperBuilder(toBuilder = true)
@ToString
@Jacksonized
public class ExportMallQueryRequest extends BaseMallRequest {

    public MallQueryRequest genMallQueryRequest() {
        return MallQueryRequest.builder()
                .mallID(getMallID())
                .build();
    }
}