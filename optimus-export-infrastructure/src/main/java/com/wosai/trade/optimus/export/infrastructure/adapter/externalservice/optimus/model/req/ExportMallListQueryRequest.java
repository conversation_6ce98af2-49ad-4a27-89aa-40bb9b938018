package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req;

import com.wosai.trade.optimus.api.model.enums.SortEnum;
import com.wosai.trade.optimus.api.model.req.*;
import com.wosai.trade.optimus.api.request.mall.MallListQueryForInnerRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 12:06
 */
@Getter
@SuperBuilder(toBuilder = true)
@ToString
@Jacksonized
public class ExportMallListQueryRequest extends BaseRequest {

    private static final MallCursorModel DEFAULT_CURSOR_MODEL = MallCursorModel.builder().cursorField(MallCursorModel.MallCursorFieldEnum.ID).count(100).build();
    private static final MallSortModel DEFAULT_SORT_MODEL = MallSortModel.builder().sortField(MallSortModel.MallSortFieldEnum.ID).sort(SortEnum.ASC).build();

    private List<Map<String, String>> mallSnSignatures;

    public MallListQueryForInnerRequest genMallListQueryRequest() {
        return MallListQueryForInnerRequest.builder()
                .cursor(DEFAULT_CURSOR_MODEL)
                .sort(DEFAULT_SORT_MODEL)
                .filter(MallFilterByInnerModel.builder()
                        .mallSnList(genMallSnList(mallSnSignatures))
                        .seller(genDefaultSeller())
                        .build())
                .build();
    }

    private List<MallIDModel> genMallSnList(List<Map<String, String>> mallSnSignatures) {
        List<MallIDModel> mallIDModels = new ArrayList<>();
        for (Map<String, String> mallSnSignature : mallSnSignatures) {
            Iterator<Map.Entry<String, String>> iterator = mallSnSignature.entrySet().iterator();
            if (iterator.hasNext()) {
                Map.Entry<String, String> entry = iterator.next();
                mallIDModels.add(MallIDModel.builder().mallSn(entry.getKey()).signature(entry.getValue()).build());
            }
        }
        return mallIDModels;
    }

    private SellerIDForInnerModel genDefaultSeller() {
        SellerIDForInnerModel defaultSeller = new SellerIDForInnerModel();
        defaultSeller.setRole("super_admin");
        return defaultSeller;
    }

}