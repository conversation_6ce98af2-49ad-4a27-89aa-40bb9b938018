package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/24
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class StoreInfoQueryResult {
    private static StoreInfoQueryResult DEFAULT = StoreInfoQueryResult.builder().build();


    @JsonProperty("id")
    private String storeId;
    @JsonProperty("sn")
    private String storeSn;
    @JsonProperty("name")
    private String storeName;
    @JsonProperty("merchant_sn")
    private String merchantSn;
    @JsonProperty("merchant_id")
    private String merchantId;
    @JsonProperty("merchant_name")
    private String merchantName;
    @JsonProperty("city")
    private String city;
    @JsonProperty("contact_cellphone")
    private String contactCellphone;

    @JsonIgnore
    public boolean isExistCity(){
        return StringUtils.isNotEmpty(city);
    }

    @JsonIgnore
    public boolean isNotInvokeSucceed() {
        return Objects.isNull(storeId);
    }

    public void checkInvokeSucceed() {
        if (isNotInvokeSucceed()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.EXTERNAL_SERVICE_INVOKE_FAILURE);
        }
    }

    public static List<StoreInfoQueryResult> genStoreQueryResultList(List<Map> records) {
        if(CollectionUtils.isEmpty(records)){
            return List.of();
        }
        return records.stream()
                .map(map -> JsonUtils.convertToObject(map, new TypeReference<StoreInfoQueryResult>() {}))
                .collect(Collectors.toList());
    }

    public static StoreInfoQueryResult genStoreQueryResult(List<Map> records) {

        if(CollectionUtils.isEmpty(records)){
            return DEFAULT;
        }

        return records.stream()
                .findFirst()
                .map(map -> JsonUtils.convertToObject(map, new TypeReference<StoreInfoQueryResult>() {}))
                .orElse(StoreInfoQueryResult.builder().build());
    }

    public void checkExist() {
        if(isNotInvokeSucceed()){
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.STORE_NOT_EXIST);
        }
    }

    @JsonIgnore
    public static Map<String, String> genStoreSnNameMap(List<StoreInfoQueryResult> storeInfoQueryResults) {
        if (CollectionUtils.isEmpty(storeInfoQueryResults)) {
            return Map.of();
        }
        return storeInfoQueryResults.stream()
                .collect(Collectors.toMap(
                        StoreInfoQueryResult::getStoreSn,
                        StoreInfoQueryResult::getStoreName,
                        (v1 ,v2) -> v2));
    }

    @JsonIgnore
    public static Map<String, String> genMerchantSnNameMap(List<StoreInfoQueryResult> storeInfoQueryResults) {
        if (CollectionUtils.isEmpty(storeInfoQueryResults)) {
            return Map.of();
        }
        return storeInfoQueryResults.stream()
                .collect(Collectors.toMap(
                        StoreInfoQueryResult::getMerchantSn,
                        StoreInfoQueryResult::getMerchantName,
                        (v1 ,v2) -> v2));
    }

    public boolean isContactCellphoneExist() {
        return StringUtils.isNotBlank(contactCellphone);
    }
}
