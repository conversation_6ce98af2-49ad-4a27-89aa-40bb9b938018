package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.res;

import com.wosai.app.dto.GroupUserMerchantAuthInfo;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2024/7/10 Time: 10:12
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class OptimusGroupUserMerchantQueryResult {

    public final static OptimusGroupUserMerchantQueryResult DEFAULT = OptimusGroupUserMerchantQueryResult.builder().build();

    private List<GroupUserMerchantAuthInfo> merchantList;

    public static OptimusGroupUserMerchantQueryResult newInstance(List<GroupUserMerchantAuthInfo> groupUserMerchantAuthInfos) {
        return OptimusGroupUserMerchantQueryResult.builder()
                .merchantList(groupUserMerchantAuthInfos)
                .build();
    }

    public void checkMerchantListExist() {
        if (CollectionUtils.isEmpty(merchantList)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.GROUP_MERCHANT_NOT_EXIST);
        }
    }

    public List<String> getMerchantSnList() {
        return merchantList.stream().map(GroupUserMerchantAuthInfo::getMerchant_sn).collect(Collectors.toList());
    }

    public void checkMerchantPermission(List<String> inputMerchantSnList) {
        List<String> ownerMerchantSnList = merchantList.stream().map(GroupUserMerchantAuthInfo::getMerchant_sn).toList();
        if(CollectionUtils.isEmpty(ownerMerchantSnList)){
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.MALL_NO_PERMISSION_ERROR);
        }
        inputMerchantSnList.stream()
                .filter(m -> !ownerMerchantSnList.contains(m))
                .findAny()
                .ifPresent(m -> {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.MALL_NO_PERMISSION_ERROR);
                });
    }

}
