package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.email.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> Date: 2021/9/12 Time: 6:02 下午
 */
@Getter
@SuperBuilder(toBuilder = true)
public class EmailSendRequest extends BaseRequest {

    private String from;
    private String to;
    private String subject;
    private String text;
    private String attachmentFileName;
    @JsonIgnore
    private byte[] attachmentContent;
    private String attachmentType;

}
