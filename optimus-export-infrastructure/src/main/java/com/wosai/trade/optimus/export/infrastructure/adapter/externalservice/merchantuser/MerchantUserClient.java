package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser;

import com.wosai.app.dto.GroupUserMerchantAuthInfo;
import com.wosai.app.dto.QueryGroupMerchantAuthReq;
import com.wosai.app.dto.QueryMerchantUserReq;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.service.GroupService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.trade.optimus.export.common.template.InvokeProcessor;
import com.wosai.trade.optimus.export.common.template.InvokeTemplate;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.req.OptimusGroupUserMerchantQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.res.OptimusGroupUserMerchantQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.req.OptimusMerchantUserQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.req.OptimusMerchantUserSimpleInfoQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.res.OptimusMerchantUserQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.res.OptimusMerchantUserSimpleInfoQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/11
 */
@Slf4j
@Component
public class MerchantUserClient {

    @Resource
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Resource
    private GroupService groupService;


    public List<String> getOwnerStoreIdsByMerchantUserId(String merchantUserId) {
        return InvokeProcessor.process(merchantUserId, new InvokeTemplate<>() {
            @Override
            protected List<String> invoke(String s) throws Throwable {
                log.info("[商户用户查询管理的门店ID列表]>>>>>>入参: {}", merchantUserId);
                List<String> storeIds = merchantUserServiceV2.getStoreIdsByMerchantUserId(merchantUserId);
                log.info("[商户用户查询管理的门店ID列表]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(storeIds));
                return storeIds;
            }
        });
    }

    public OptimusMerchantUserQueryResult getMerchantUser(OptimusMerchantUserQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected OptimusMerchantUserQueryResult invoke(OptimusMerchantUserQueryRequest request) throws Throwable {
                QueryMerchantUserReq queryMerchantUserReq = request.genQueryMerchantUserReq();
                log.info("[根据条件查询用户]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(queryMerchantUserReq));
                List<UcMerchantUserInfo> userInfos = merchantUserServiceV2.getMerchantUser(queryMerchantUserReq);
                log.info("[商户用户查询管理的门店ID列表]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(userInfos));
                return OptimusMerchantUserQueryResult.newInstance(userInfos);
            }

            @Override
            protected OptimusMerchantUserQueryResult onFailure(OptimusMerchantUserQueryRequest request, Throwable throwable) throws Throwable {
                return OptimusMerchantUserQueryResult.DEFAULT;
            }
        });
    }

    public OptimusMerchantUserSimpleInfoQueryResult getMerchantUserSimpleInfo(OptimusMerchantUserSimpleInfoQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected OptimusMerchantUserSimpleInfoQueryResult invoke(OptimusMerchantUserSimpleInfoQueryRequest request) throws Throwable {
                QueryMerchantUserReq queryMerchantUserReq = request.genQueryMerchantUserReq();
                log.info("[商户用户查询管理的门店ID列表]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(queryMerchantUserReq));
                List<UcMerchantUserSimpleInfo> merchantUserSimpleInfo = merchantUserServiceV2.getMerchantUserSimpleInfo(queryMerchantUserReq);
                log.info("[商户用户查询管理的门店ID列表]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(merchantUserSimpleInfo));
                return OptimusMerchantUserSimpleInfoQueryResult.newInstance(merchantUserSimpleInfo);
            }

            @Override
            protected OptimusMerchantUserSimpleInfoQueryResult onFailure(OptimusMerchantUserSimpleInfoQueryRequest request, Throwable throwable) throws Throwable {
                return OptimusMerchantUserSimpleInfoQueryResult.DEFAULT;
            }
        });
    }


    public OptimusGroupUserMerchantQueryResult getGroupUserMerchants(OptimusGroupUserMerchantQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected OptimusGroupUserMerchantQueryResult invoke(OptimusGroupUserMerchantQueryRequest request) throws Throwable {
                QueryGroupMerchantAuthReq queryGroupMerchantAuthReq = request.genQueryMerchantUserReq();
                log.info("[集团用户查询集团下全部商户列表]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(queryGroupMerchantAuthReq));
                List<GroupUserMerchantAuthInfo> groupUserMerchantAuthInfos = groupService.getGroupUserMerchantAuths(queryGroupMerchantAuthReq);
                log.info("[集团用户查询集团下全部商户列表]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(groupUserMerchantAuthInfos));
                return OptimusGroupUserMerchantQueryResult.newInstance(groupUserMerchantAuthInfos);
            }

            @Override
            protected OptimusGroupUserMerchantQueryResult onFailure(OptimusGroupUserMerchantQueryRequest request, Throwable throwable) throws Throwable {
                return OptimusGroupUserMerchantQueryResult.DEFAULT;
            }
        });
    }

}
