package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo;

import lombok.Getter;

/**
 * <AUTHOR> Date: 2023/2/7 Time: 3:12 PM
 */
public enum NamespaceEnum {

    ERROR_MSG_OPTIMUS_MODULE("error_msg_optimus_export"),
    EXTERNAL_INVOKE_THREAD_POOL("ext_invoke_thread_pool"),


    ;

    @Getter
    private final String code;

    NamespaceEnum(String code) {
        this.code = code;
    }
}
