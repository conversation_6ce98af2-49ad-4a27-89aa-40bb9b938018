package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.kafka;

import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.kafka.po.KafkaPO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.NoTransactionException;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.concurrent.ListenableFutureCallback;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Date: 2023/1/3 Time: 11:05 AM
 */
@Slf4j
@Component
public class KafkaDaoImpl implements KafkaDao {

    @Resource
    private KafkaTemplate<Object, Object> stringKafkaTemplate;
    @Resource
    private KafkaTemplate<Object, Object> avroKafkaTemplate;


    @Override
    public void sendAsync(KafkaPO po) {
        if (Objects.isNull(po)) {
            return;
        }
        KafkaTemplate<Object, Object> kafkaTemplate = selectUsingTemplate(po);
        if (po.isExistKey()) {
            kafkaTemplate.send(po.getTopic(), po.getKey(), po.getData())
                    .addCallback(new ListenableFutureCallbackImpl());
            return;
        }
        kafkaTemplate.send(po.getTopic(), po.getData())
                .addCallback(new ListenableFutureCallbackImpl());
    }

    @Override
    public void batchSendAsync(List<KafkaPO> poList) {
        if (CollectionUtils.isNotEmpty(poList)) {
            poList.forEach(this::sendAsync);
        }
    }

    @SneakyThrows
    @Override
    public void sendSync(KafkaPO po, Long timeout, TimeUnit timeUnit) {
        if (Objects.isNull(po)) {
            return;
        }
        KafkaTemplate<Object, Object> kafkaTemplate = selectUsingTemplate(po);
        SendResult<Object, Object> sendResult;
        if (po.isExistKey()) {
            sendResult = kafkaTemplate.send(po.getTopic(), po.getKey(), po.getData()).get(timeout, timeUnit);
        } else {
            sendResult = kafkaTemplate.send(po.getTopic(), po.getData()).get(timeout, timeUnit);
        }
        log.info("[Kafka消息推送成功]>>>>>>result: {}", sendResult);
    }

    @Override
    public void sendAsyncOutsideOfTransaction(KafkaPO po) {
        if (isTransactionCompleted()) {
            sendAsync(po);
            return;
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCompletion(int status) {
                if (isTransactionCompleted()) {
                    sendAsync(po);
                }
            }
        });
    }

    @Override
    public void batchSendAsyncOutsideOfTransaction(List<KafkaPO> poList) {
        if (CollectionUtils.isNotEmpty(poList)) {
            poList.forEach(this::sendAsyncOutsideOfTransaction);
        }
    }

    private KafkaTemplate<Object, Object> selectUsingTemplate(KafkaPO po) {
        return po.isAvroSerialization() ? avroKafkaTemplate : stringKafkaTemplate;
    }

    private boolean isTransactionCompleted() {
        try {
            TransactionStatus transactionStatus =
                    TransactionAspectSupport.currentTransactionStatus();
            return transactionStatus.isCompleted();
        } catch (NoTransactionException e) {
            return true;
        } catch (Exception e) {
            log.error("[事件消息切面]>>>>>>判断事件状态异常, 异常栈: ", e);
            //抛了未知异常，先不发消息，人工诊断问题之后再做逻辑调整
            return false;
        }
    }

    public static class ListenableFutureCallbackImpl
            implements ListenableFutureCallback<SendResult<Object, Object>> {
        @Override
        public void onFailure(@NotNull Throwable ex) {
            log.error("[Kafka消息推送异常]>>>>>>异常栈: ", ex);
        }

        @Override
        public void onSuccess(@Nullable SendResult<Object, Object> result) {
            log.info("[Kafka消息推送成功]>>>>>>result: {}", result);
        }
    }
}
