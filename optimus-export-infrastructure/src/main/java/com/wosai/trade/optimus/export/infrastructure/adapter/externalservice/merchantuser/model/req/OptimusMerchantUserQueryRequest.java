package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.model.req;

import com.wosai.app.dto.QueryMerchantUserReq;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR> Date: 2024/5/16 Time: 10:12
 */
@Getter
@Builder
public class OptimusMerchantUserQueryRequest {

    private final String merchantId;

    public QueryMerchantUserReq genQueryMerchantUserReq() {
        return new QueryMerchantUserReq()
                .setMerchant_id(merchantId);
    }
}
