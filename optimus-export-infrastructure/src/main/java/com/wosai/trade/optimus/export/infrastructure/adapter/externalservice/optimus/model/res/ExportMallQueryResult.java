package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res;

import com.wosai.trade.optimus.api.result.mall.MallQueryResult;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 12:06
 */
@Getter
@Setter
@ToString(callSuper = true)
@Accessors(chain = true)
public class ExportMallQueryResult {

    private MallQueryResult mallQueryResult;

    public String getMerchantId() {
        return mallQueryResult.getMerchantId();
    }

    public String getStoreId() {
        return mallQueryResult.getStoreId();
    }

    public String getMallSn() {
        return mallQueryResult.getMallSn();
    }

    public String getMallName() {
        return mallQueryResult.getMallName();
    }

    public static ExportMallQueryResult newInstance(MallQueryResult data) {
        ExportMallQueryResult exportMallQueryResult =new ExportMallQueryResult();
        exportMallQueryResult.setMallQueryResult(data);
        return exportMallQueryResult;
    }
}
