package com.wosai.trade.optimus.export.infrastructure.config.db;

import com.wosai.trade.optimus.export.common.constant.SequenceNamespaceEnum;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.sequence.SequenceDao;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.sequence.po.SequencePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2023/7/2
 */
@Slf4j
@Component
public class SequenceConfig {

    private static final ConcurrentMap<String, Pair<AtomicLong, Long>> SERIAL_NO_MAP
            = new ConcurrentHashMap<>();

    @Resource
    private SequenceDao sequenceDao;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private TransactionTemplate requiresNewTransactionTemplate;


    /**
     * 生成序列
     *
     * @param namespace
     * @return
     */
    public long genSequence(String namespace) {
        Pair<AtomicLong, Long> pair = getNotNullPair(namespace);
        AtomicLong sequenceGenerator = pair.getLeft();
        Long stageMax = pair.getRight();
        long serialNo = sequenceGenerator.getAndIncrement();
        if (serialNo >= stageMax) {
            synchronized (this) {
                pair = getNotNullPair(namespace);
                sequenceGenerator = pair.getLeft();
                stageMax = pair.getRight();
                serialNo = sequenceGenerator.getAndIncrement();
                if (serialNo < stageMax) {
                    return serialNo;
                }

                return load(namespace);
            }
        }
        return serialNo;
    }

    private Pair<AtomicLong, Long> getNotNullPair(String namespace) {
        Pair<AtomicLong, Long> pair = SERIAL_NO_MAP.get(namespace);
        if (Objects.isNull(pair)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum
                    .SEQUENCE_GENERATOR_NOT_FOUND);
        }
        return pair;
    }

    /**
     * 这里一定要新建事务会话，不然和外层方法共用一个事务会话
     * 外层回滚，序列号的更新也一并回滚了，就会造成id重复
     * 批次跨度越大凉的越透
     *
     * @param namespace
     * @return
     */
    private Long load(String namespace) {
        return requiresNewTransactionTemplate.execute(status -> {
            //查询序列
            SequencePO sequenceConfigPO = sequenceDao.selectForUpdate(namespace);
            if (Objects.isNull(sequenceConfigPO)) {
                throw new OptimusExportBizException(OptimusExportRespCodeEnum
                        .SEQUENCE_GENERATOR_NOT_FOUND);
            }
            log.debug("[序列号生成器]>>>>>>加载配置: {}", sequenceConfigPO);

            long serialNoBase = sequenceConfigPO.getSerialNoBase();
            long newSerialNoBase = serialNoBase + sequenceConfigPO.getBatchSize();
            AtomicLong newSequenceGenerator = new AtomicLong(serialNoBase);
            long serialNo = newSequenceGenerator.getAndIncrement();
            Pair<AtomicLong, Long> newSequenceGeneratorPair
                    = Pair.of(newSequenceGenerator
                    , newSerialNoBase);

            //更新序列种子
            sequenceConfigPO.setMtime(LocalDateTime.now());
            sequenceConfigPO.setSerialNoBase(newSerialNoBase);
            sequenceDao.update(sequenceConfigPO);

            //缓存新的序列生成器
            SERIAL_NO_MAP.put(namespace, newSequenceGeneratorPair);
            log.debug("[序列号生成器]>>>>>>更新后容器: {}, 更新后返回{}", SERIAL_NO_MAP, serialNo);
            return serialNo;
        });
    }

    @PostConstruct
    private void init() {
        SequenceNamespaceEnum[] sequenceNamespaceEnums = SequenceNamespaceEnum.values();
        for (SequenceNamespaceEnum sequenceNamespaceEnum : sequenceNamespaceEnums) {
            transactionTemplate.executeWithoutResult(transactionStatus -> {
                //查询序列
                SequencePO sequenceConfigPO = sequenceDao
                        .selectForUpdate(sequenceNamespaceEnum.getNamespace());
                if (Objects.isNull(sequenceConfigPO)) {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum
                            .SEQUENCE_GENERATOR_NOT_FOUND);
                }

                long serialNoBase = sequenceConfigPO.getSerialNoBase();
                long newSerialNoBase = serialNoBase + sequenceConfigPO.getBatchSize();
                AtomicLong newSequenceGenerator = new AtomicLong(serialNoBase);
                Pair<AtomicLong, Long> newSequenceGeneratorPair
                        = Pair.of(newSequenceGenerator
                        , newSerialNoBase);

                //更新序列种子
                sequenceConfigPO.setMtime(LocalDateTime.now());
                sequenceConfigPO.setSerialNoBase(newSerialNoBase);
                sequenceDao.update(sequenceConfigPO);

                log.debug("[序列号生成器]>>>>>>初始化容器: {}", SERIAL_NO_MAP);

                //缓存新的序列生成器
                SERIAL_NO_MAP.put(sequenceNamespaceEnum.getNamespace(), newSequenceGeneratorPair);
            });
        }
    }
}
