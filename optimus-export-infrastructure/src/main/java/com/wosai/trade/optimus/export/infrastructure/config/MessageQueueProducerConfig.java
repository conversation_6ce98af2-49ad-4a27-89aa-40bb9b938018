package com.wosai.trade.optimus.export.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

/**
 * <AUTHOR> Date: 2024/3/14 Time: 13:57
 */
@Slf4j
@Configuration
@Import(KafkaAutoConfiguration.class)
@EnableConfigurationProperties({MessageQueueProducerConfig.DefaultProducerProperties.class})
public class MessageQueueProducerConfig {

    private final KafkaProperties defaultKafkaProperties;
    public MessageQueueProducerConfig(@Qualifier("kafkaProperties") KafkaProperties kafkaProperties) {
        this.defaultKafkaProperties = kafkaProperties;
    }


    @Primary
    @Bean(name = "stringKafkaTemplate")
    public KafkaTemplate<String, String> stringKafkaTemplate() {
        return new KafkaTemplate<>(stringProducerFactory());
    }


    @Bean(name = "stringProducerFactory")
    public ProducerFactory<String, String> stringProducerFactory() {
        return new DefaultKafkaProducerFactory<>(defaultKafkaProperties.buildProducerProperties()
                , new StringSerializer(), new StringSerializer());
    }

    @ConfigurationProperties(prefix = "spring.kafka")
    public static class DefaultProducerProperties extends KafkaProperties {

        @Primary
        @Bean(name = "kafkaProperties")
        public KafkaProperties kafkaProperties() {
            return this;
        }
    }

}
