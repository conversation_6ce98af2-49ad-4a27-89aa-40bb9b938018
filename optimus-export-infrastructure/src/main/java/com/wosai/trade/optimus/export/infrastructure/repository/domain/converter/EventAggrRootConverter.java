package com.wosai.trade.optimus.export.infrastructure.repository.domain.converter;

import com.google.common.collect.Lists;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRootFactory;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventStateEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.vo.DelayRuleVO;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.vo.EventContentVO;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.vo.EventExtVO;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.kafka.po.KafkaPO;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.po.EventPO;
import com.wosai.trade.optimus.export.infrastructure.util.PropertiesUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/1/3 Time: 3:26 PM
 */
@Component
public class EventAggrRootConverter {


    public EventPO toEventPO(EventAggrRoot aggrRoot) {
        if (Objects.isNull(aggrRoot) || aggrRoot.isNotExist()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.EVENT_NOT_EXIST);
        }
        return new EventPO()
                .setId(aggrRoot.getId())
                .setType(aggrRoot.getType().getCode())
                .setContent(aggrRoot.getContent().toJsonNode())
                .setState(aggrRoot.getState().getCode())
                .setResult(aggrRoot.getResult())
                .setDelayRule(aggrRoot.getDelayRule().toJsonNode())
                .setNextProcessTime(aggrRoot.getNextProcessTime())
                .setAssociatedSn(aggrRoot.getAssociatedSn())
                .setExt(aggrRoot.getExt().toJsonNode())
                .setCtime(aggrRoot.getCtime())
                .setMtime(aggrRoot.getMtime())
                .setVersion(aggrRoot.getVersion());
    }

    public List<EventPO> toEventPOList(List<EventAggrRoot> eventAggrRoots) {
        if (CollectionUtils.isEmpty(eventAggrRoots)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.EVENT_NOT_EXIST);
        }
        List<EventPO> eventPOS = Lists.newArrayListWithCapacity(eventAggrRoots.size());
        for (EventAggrRoot eventAggrRoot : eventAggrRoots) {
            EventPO po = toEventPO(eventAggrRoot);
            eventPOS.add(po);
        }
        return eventPOS;
    }

    public EventAggrRoot toEventAggrRoot(EventPO po) {
        if (Objects.isNull(po)) {
            return EventAggrRoot.newEmptyInstance();
        }
        return EventAggrRootFactory.builder()
                .coreBuilder()
                .id(po.getId())
                .type(EventTypeEnum.ofCode(po.getType()))
                .associatedSn(po.getAssociatedSn())
                .optionalBuilder()
                .state(EventStateEnum.ofCode(po.getState()))
                .result(po.getResult())
                .content(EventContentVO.genFromJsonObject(po.getContent(), EventContentVO.class))
                .delayRule(DelayRuleVO.genFromJsonObject(po.getDelayRule(), DelayRuleVO.class))
                .nextProcessTime(po.getNextProcessTime())
                .ext(EventExtVO.genFromJsonObject(po.getExt(), EventExtVO.class))
                .ctime(po.getCtime())
                .mtime(po.getMtime())
                .version(po.getVersion())
                .rebuild();
    }

    public List<EventAggrRoot> toEventAggrRootList(List<EventPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return Lists.newArrayListWithCapacity(0);
        }
        List<EventAggrRoot> aggrRoots = Lists.newArrayListWithCapacity(poList.size());
        EventAggrRoot aggrRoot;
        for (EventPO po : poList) {
            aggrRoot = toEventAggrRoot(po);
            if (aggrRoot.isExist()) {
                aggrRoots.add(aggrRoot);
            }
        }
        return aggrRoots;
    }

    public KafkaPO toKafkaPO(EventAggrRoot aggrRoot) {
        if (Objects.isNull(aggrRoot) || aggrRoot.isNotExist()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.EVENT_NOT_EXIST);
        }
        return new KafkaPO().setTopic(PropertiesUtils.getInstance().getTradeOrderEventTopic())
                .setKey(aggrRoot.getMqKey())
                .setData(aggrRoot.getId());
    }

    public List<KafkaPO> toKafkaPOList(List<EventAggrRoot> aggrRoots) {
        if (CollectionUtils.isEmpty(aggrRoots)) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.EVENT_NOT_EXIST);
        }
        List<KafkaPO> poList = Lists.newArrayListWithCapacity(aggrRoots.size());
        for (EventAggrRoot aggrRoot : aggrRoots) {
            poList.add(toKafkaPO(aggrRoot));
        }
        return poList;
    }

}
