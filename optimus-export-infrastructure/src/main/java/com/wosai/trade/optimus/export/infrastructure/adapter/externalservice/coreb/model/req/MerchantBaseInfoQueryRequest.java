package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.req;

import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR> Date: 2023/1/11 Time: 3:50 PM
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class MerchantBaseInfoQueryRequest extends BaseRequest {

    private String storeSn;
    private String terminalSn;

}
