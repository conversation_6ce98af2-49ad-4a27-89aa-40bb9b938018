package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.errormsg;

import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.BaseNamespaceDao;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.NamespaceEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Date: 2023/2/7 Time: 9:53 AM
 */
@Component
public class ErrorMsgDaoImpl extends BaseNamespaceDao implements ErrorMsgDao {

    @Override
    public String query(String key, NamespaceEnum namespace) {
        if (StringUtils.isNotBlank(key)) {
            return getApolloConfig(namespace.getCode()).getProperty(key, null);
        }
        return null;
    }

}
