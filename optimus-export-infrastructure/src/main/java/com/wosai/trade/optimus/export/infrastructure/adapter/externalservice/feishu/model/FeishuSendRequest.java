package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.feishu.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import com.wosai.trade.optimus.export.infrastructure.util.PropertiesUtils;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/2/15 Time: 9:44 AM
 */
@Getter
@SuperBuilder(toBuilder = true)
public class FeishuSendRequest extends BaseRequest {
    public static final String TYPE = "text";
    public static final String EVENT_PROCESS_ALARM_TEMPLATE = """
            当前环境: %s
            触发事件: 商城导出事件处理告警
            事件类型: %s
            事件标识: %s
            当前阈值: %s
            处理次数: %s
            """;

    private String notifyUrl;
    private FeishuSendModel feishuSendModel;


    public static FeishuSendRequest genEventProcessAlarmRequest(String eventType
            , Long eventId
            , Integer threshold
            , Integer processedCount) {
        PropertiesUtils propertiesUtils = PropertiesUtils.getInstance();
        String env = propertiesUtils.getEnv();
        String eventAlarmUrl = propertiesUtils.getEventAlarmUrl();
        List<String> eventAlarmUserIds = propertiesUtils.getEventAlarmUserIds();
        String alarmContent = String.format(EVENT_PROCESS_ALARM_TEMPLATE, env
                , eventType, eventId
                , threshold, processedCount);
        return FeishuSendRequest.builder()
                .notifyUrl(eventAlarmUrl)
                .feishuSendModel(FeishuSendModel.builder()
                        .msgtype(TYPE)
                        .content(MsgText.builder()
                                .text(alarmContent)
                                .build())
                        .at(MsgAt.builder()
                                .userIds(eventAlarmUserIds)
                                .build())
                        .build())
                .build();
    }


    public String getSendBodyString() {
        return feishuSendModel.toString();
    }

    @Getter
    @SuperBuilder(toBuilder = true)
    public static class FeishuSendModel extends BaseRequest {
        @JsonProperty("msg_type")
        private String msgtype;
        private MsgText content;

        @JsonIgnore
        private MsgAt at;

        @Override
        public String toString() {
            if (Objects.nonNull(content) && Objects.nonNull(at)) {
                content.appendAtMsg(at.genAtMsgBody());
            }
            return toJsonString();
        }
    }


    @Getter
    @Builder
    public static class MsgText {

        private String text;

        public void appendAtMsg(String body) {
            if (StringUtils.isEmpty(body)) {
                return;
            }
            if (Objects.isNull(text)) {
                text = body;
                return;
            }
            text += body;
        }
    }

    @Getter
    @Builder
    public static class MsgAt {
        private static final String TEMPLATE = """
                <at user_id = "%s"></at>
                """;
        private static final String USER_ID_FOR_AT_ALL = "all";
        private List<String> userIds;
        private Boolean isAtAll;

        public String genAtMsgBody() {
            if (CollectionUtils.isNotEmpty(userIds)) {
                StringBuilder atMsgBody = new StringBuilder();
                for (String userId : userIds) {
                    atMsgBody.append(String.format(TEMPLATE, userId));
                }
                return atMsgBody.toString();
            }
            if (Objects.equals(isAtAll, Boolean.TRUE)) {
                return String.format(TEMPLATE, USER_ID_FOR_AT_ALL);
            }
            return null;
        }
    }
}
