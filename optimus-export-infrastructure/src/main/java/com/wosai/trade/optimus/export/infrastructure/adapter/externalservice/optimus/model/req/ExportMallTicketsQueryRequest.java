package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.req;

import com.shouqianba.trade.aftersale.api.request.model.CursorModel;
import com.shouqianba.trade.aftersale.api.request.model.InnerTicketFilterModel;
import com.shouqianba.trade.aftersale.api.request.model.SortModel;
import com.wosai.trade.optimus.api.model.req.MallIDModel;
import com.wosai.trade.optimus.api.request.aftersale.MallTicketsForInnerQueryRequest;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2024/8/8 Time: 14:01
 */
@SuperBuilder(toBuilder = true)
public class ExportMallTicketsQueryRequest extends BaseRequest {
    private final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final CursorModel.TicketCursorFieldEnum CURSOR_FIELD = CursorModel.TicketCursorFieldEnum.TICKET_ID;
    private static final SortModel SORT = SortModel.builder()
            .sort(SortModel.SortEnum.DESC)
            .sortField(SortModel.SortFieldEnum.TICKET_ID)
            .build();

    private final String mallSn;
    private final String mallSignature;
    private final List<Integer> ticketStates;
    private final LocalDateTime beginDateTime;
    private final LocalDateTime endDateTime;
    private final String payTsn;
    private final String orderSn;
    private final String endCursor;
    private final Integer count;

    public MallTicketsForInnerQueryRequest genMallTicketsForInnerQueryRequest() {
        CursorModel cursor = CursorModel.builder()
                .cursorField(CURSOR_FIELD)
                .endCursor(endCursor)
                .count(count)
                .build();

        InnerTicketFilterModel filter = InnerTicketFilterModel.builder()
                .ticketStates(ticketStates)
                .beginDateTime(DATE_FORMATTER.format(beginDateTime))
                .endDateTime(DATE_FORMATTER.format(endDateTime))
                .payTsn(payTsn)
                .orderSn(orderSn)
                .build();

        MallIDModel mallID = null;
        if (Objects.nonNull(mallSn) && Objects.nonNull(mallSignature)) {
            mallID = MallIDModel.builder()
                    .mallSn(mallSn)
                    .signature(mallSignature)
                    .build();
        }

        return MallTicketsForInnerQueryRequest.builder()
                .mallID(mallID)
                .cursor(cursor)
                .sort(SORT)
                .filter(filter)
                .build();
    }
}
