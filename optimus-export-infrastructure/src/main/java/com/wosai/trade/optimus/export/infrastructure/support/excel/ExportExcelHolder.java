package com.wosai.trade.optimus.export.infrastructure.support.excel;

import com.alibaba.excel.ExcelWriter;
import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.Builder;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> Date: 2024/8/5 Time: 10:56
 */
@Builder(access = AccessLevel.PACKAGE)
public class ExportExcelHolder implements ExcelOperations {
    private final List<ExcelOperations> exportExcels;

    public static ExportExcelHolder create() {
        return ExportExcelHolder.builder()
                .exportExcels(Lists.newArrayList())
                .build();
    }

    public static ExportExcelHolder create(List<ExcelOperations> exportExcels) {
        ExportExcelHolder.ExportExcelHolderBuilder builder = ExportExcelHolder.builder();
        if (CollectionUtils.isEmpty(exportExcels)) {
            builder.exportExcels(Lists.newArrayList());
        } else {
            builder.exportExcels(exportExcels);
        }
        return builder.build();
    }


    public ExportExcelHolder append(ExcelOperations exportExcel) {
        exportExcels.add(exportExcel);
        return self();
    }


    @Override
    public void write(ExcelWriter writer) {
        if (CollectionUtils.isEmpty(exportExcels)) {
            return;
        }
        exportExcels.forEach(item -> item.write(writer));
    }

    @Override
    public void clear() {
        if (CollectionUtils.isEmpty(exportExcels)) {
            return;
        }
        exportExcels.forEach(ExcelOperations::clear);
    }

    @Override
    public void writeWithClear(ExcelWriter writer) {
        if (CollectionUtils.isEmpty(exportExcels)) {
            return;
        }
        exportExcels.forEach(item -> item.writeWithClear(writer));
    }

    private ExportExcelHolder self() {
        return this;
    }
}
