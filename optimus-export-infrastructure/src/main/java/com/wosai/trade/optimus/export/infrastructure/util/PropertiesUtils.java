package com.wosai.trade.optimus.export.infrastructure.util;

import com.wosai.trade.optimus.export.common.support.PropertiesSupporter;
import com.wosai.trade.optimus.export.common.util.ApplicationContextUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> Date: 2023/1/30 Time: 10:34 AM
 */
@Component
public class PropertiesUtils implements PropertiesSupporter {

    @Value("${spring.kafka.producer.topic.module}")
    private String optimusEventTopic;
    @Value("${spring.profiles.active}")
    private String env;
    @Value("${feishu.notify.url}")
    private String eventAlarmUrl;
    @Value("#{'${feishu.notify.userids}'.split(',')}")
    private List<String> eventAlarmUserIds;
    @Value("${volcano.app.id}")
    private Integer volcanoAppId;

    public static PropertiesUtils getInstance() {
        return ApplicationContextUtils.getBean(PropertiesUtils.class);
    }

    @Override
    public String getTradeOrderEventTopic() {
        return optimusEventTopic;
    }

    @Override
    public String getEnv() {
        return env;
    }

    @Override
    public String getEventAlarmUrl() {
        return eventAlarmUrl;
    }

    @Override
    public List<String> getEventAlarmUserIds() {
        return eventAlarmUserIds;
    }

    @Override
    public Integer getVolcanoAppId() {
        return volcanoAppId;
    }


}
