package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql;

import com.wosai.general.util.ValidationUtils;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/1/3 Time: 3:34 PM
 */
@Getter
@SuperBuilder(toBuilder = true)
public class BaseQuery {

    @Pattern(regexp = "^[a-zA-Z0-9_]*$", message = "游标字段格式错误")
    protected String cursorField;
    protected String endCursor;

    @Pattern(regexp = "^[a-zA-Z0-9_]*$", message = "排序字段格式错误")
    protected String sortField;
    protected boolean isDesc;

    @Min(value = 0, message = "偏移量格式错误")
    protected Integer offset;
    @Min(value = 1, message = "查询数量格式错误")
    protected Integer querySize;


    @AssertTrue(message = "游标字段和排序字段必须相同")
    private boolean isValidCursorAndSort() {
        if (Objects.nonNull(cursorField) && Objects.nonNull(sortField)) {
            return Objects.equals(cursorField, sortField);
        }
        return true;
    }

    protected void checkParams() {
        ValidationUtils.ValidationResult result = ValidationUtils.validate(this);
        if (result.isInvalid()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.ILLEGAL_ARGUMENT, result.getMsg());
        }
    }

}
