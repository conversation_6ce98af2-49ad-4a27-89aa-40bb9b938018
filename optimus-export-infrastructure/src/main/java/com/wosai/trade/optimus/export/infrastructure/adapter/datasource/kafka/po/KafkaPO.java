package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.kafka.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/1/3 Time: 11:06 AM
 */
@Data
@Accessors(chain = true)
public class KafkaPO {
    private String topic;
    private String key;
    private Object data;
    private SerializationType serializationType;

    public boolean isExistKey() {
        return Objects.nonNull(key);
    }

    public Object getData() {
        if (isAvroSerialization()) {
            return data;
        }
        return String.valueOf(data);
    }

    public boolean isAvroSerialization() {
        return Objects.equals(serializationType, SerializationType.AVRO);
    }

    public enum SerializationType {
        STRING,
        AVRO,

        ;
    }
}
