package com.wosai.trade.optimus.export.infrastructure.repository.domain;

import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.EventDomainRepository;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.EventAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.query.EventAggrQuery;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.kafka.KafkaDao;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.EventDao;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.model.EventQuery;
import com.wosai.trade.optimus.export.infrastructure.repository.domain.converter.EventAggrRootConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Date: 2023/6/21 Time: 11:10
 */
@Repository
public class EventDomainRepositoryImpl implements EventDomainRepository {

    @Resource
    private EventDao eventDao;
    @Resource
    private KafkaDao kafkaDao;
    @Resource
    private EventAggrRootConverter converter;


    @Override
    public void save(EventAggrRoot aggrRoot) {
        if (aggrRoot.isNeedAdd()) {
            eventDao.insert(converter.toEventPO(aggrRoot));
            kafkaDao.sendAsyncOutsideOfTransaction(converter.toKafkaPO(aggrRoot));
        } else if (aggrRoot.isNeedModify()) {
            int affectRow = eventDao.update(converter.toEventPO(aggrRoot));
            if (affectRow <= 0) {
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.CONCURRENT_MODIFY_ERROR);
            }
        }
        aggrRoot.clearMark();
    }

    @Override
    public void batchSave(List<EventAggrRoot> aggrRoots) {
        if (CollectionUtils.isNotEmpty(aggrRoots)) {
            List<EventAggrRoot> needAddAggrRoots = aggrRoots.stream()
                    .filter(EventAggrRoot::isNeedAdd).toList();
            List<EventAggrRoot> needModifyAggrRoots = aggrRoots.stream()
                    .filter(EventAggrRoot::isNeedModify).toList();
            if (CollectionUtils.isNotEmpty(needAddAggrRoots)) {
                eventDao.batchInsert(converter.toEventPOList(needAddAggrRoots));
                kafkaDao.batchSendAsyncOutsideOfTransaction(converter
                        .toKafkaPOList(needAddAggrRoots));
            }
            if (CollectionUtils.isNotEmpty(needModifyAggrRoots)) {
                needModifyAggrRoots.forEach(this::save);
            }
            aggrRoots.forEach(EventAggrRoot::clearMark);
        }
    }

    @Override
    public void saveWithoutMQ(EventAggrRoot aggrRoot) {
        if (aggrRoot.isNeedAdd()) {
            eventDao.insert(converter.toEventPO(aggrRoot));
        } else if (aggrRoot.isNeedModify()) {
            int affectRow = eventDao.update(converter.toEventPO(aggrRoot));
            if (affectRow <= 0) {
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.CONCURRENT_MODIFY_ERROR);
            }
        }
        aggrRoot.clearMark();
    }

    @Override
    public EventAggrRoot query(EventAggrQuery aggrQuery) {
        return converter.toEventAggrRoot(eventDao.select(EventQuery.genEventQuery(aggrQuery)));
    }

    @Override
    public EventAggrRoot queryWithLockSkipLocked(EventAggrQuery aggrQuery) {
        return converter.toEventAggrRoot(eventDao
                .selectForUpdateSkipLocked(EventQuery.genEventQuery(aggrQuery)));
    }

    @Override
    public List<EventAggrRoot> batchQuery(EventAggrQuery aggrQuery) {
        return converter.toEventAggrRootList(eventDao.batchSelect(EventQuery
                .genEventQuery(aggrQuery)));
    }

    @Override
    public void batchSend(List<EventAggrRoot> eventAggrRoots) {
        kafkaDao.batchSendAsync(converter.toKafkaPOList(eventAggrRoots));
    }
}
