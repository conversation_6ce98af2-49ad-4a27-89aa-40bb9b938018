package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import lombok.Builder;
import lombok.extern.jackson.Jacksonized;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/12/1 Time: 17:56
 */
@Builder(toBuilder = true)
@Jacksonized
public class MerchantTradeParamsQueryResult {
    public static final MerchantTradeParamsQueryResult EMPTY_INSTANCE = MerchantTradeParamsQueryResult.builder().build();

    private final Integer provider;
    private final Integer payTool;



    public static MerchantTradeParamsQueryResult newInstance(Integer payTool, Map<?, ?> result) {
        MerchantTradeParamsQueryResult queryResult = JsonUtils.convertToObject(result, new TypeReference<>() {});
        return queryResult.toBuilder().payTool(payTool).build();
    }

    public static MerchantTradeParamsQueryResult newEmptyInstance() {
        return EMPTY_INSTANCE;
    }


    public boolean isEnabled() {
        return Objects.nonNull(payTool);
    }


}
