package com.wosai.trade.optimus.export.infrastructure.repository.domain.converter;

import com.google.common.collect.Lists;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRoot;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.ExportOrderAggrRootFactory;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.enums.ExportStateEnum;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportInfoVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportOperatorVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.ExportResultVO;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.vo.OrderExportExtVO;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.po.ExportMallOrderPO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/7/18 Time: 13:45
 */
@Component
public class ExportMallOrderAggrRootConverter {

    public ExportMallOrderPO toMallExportOrderPO(ExportOrderAggrRoot aggrRoot) {
        if (Objects.isNull(aggrRoot) || aggrRoot.isNotExist()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.MALL_EXPORT_ORDER_NOT_EXIST);
        }
        return new ExportMallOrderPO()
                .setId(aggrRoot.getId())
                .setState(aggrRoot.getState().getCode())
                .setOwnerUserId(aggrRoot.getOperator().getOwnerUserId())
                .setOwnerId(aggrRoot.getOperator().getOwnerId())
                .setOperator(aggrRoot.getOperator().toJsonNode())
                .setExportInfo(aggrRoot.getExportInfo().toJsonNode())
                .setExportResult(aggrRoot.getExportResult().toJsonNode())
                .setExt(aggrRoot.getExt().toJsonNode())
                .setExpiredAt(aggrRoot.getExpiredAt())
                .setCtime(aggrRoot.getCtime())
                .setMtime(aggrRoot.getMtime())
                .setVersion(aggrRoot.getVersion());
    }

    public ExportOrderAggrRoot toMallExportOrderAggrRoot(ExportMallOrderPO po) {
        if (Objects.isNull(po)) {
            return ExportOrderAggrRoot.newEmptyInstance();
        }
        return ExportOrderAggrRootFactory.builder()
                .coreBuilder()
                    .id(po.getId())
                    .state(ExportStateEnum.ofCode(po.getState()))
                    .operator(ExportOperatorVO.genFromJsonObject(po.getOperator(), ExportOperatorVO.class))
                    .exportInfo(ExportInfoVO.genFromJsonObject(po.getExportInfo(), ExportInfoVO.class))
                .optionalBuilder()
                    .exportResult(ExportResultVO.genFromJsonObject(po.getExportResult(), ExportResultVO.class))
                    .ext(OrderExportExtVO.genFromJsonObject(po.getExt(), OrderExportExtVO.class))
                    .expireAt(po.getExpiredAt())
                    .ctime(po.getCtime())
                    .mtime(po.getMtime())
                    .version(po.getVersion())
                .rebuild();
    }

    public List<ExportOrderAggrRoot> toMallExportOrderAggrRoots(List<ExportMallOrderPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return List.of();
        }
        List<ExportOrderAggrRoot> exportOrderAggrRoots = Lists.newArrayListWithCapacity(poList.size());
        for (ExportMallOrderPO po : poList) {
            ExportOrderAggrRoot exportOrderAggrRoot = toMallExportOrderAggrRoot(po);
            if (exportOrderAggrRoot.isExist()) {
                exportOrderAggrRoots.add(exportOrderAggrRoot);
            }
        }
        return exportOrderAggrRoots;
    }

}
