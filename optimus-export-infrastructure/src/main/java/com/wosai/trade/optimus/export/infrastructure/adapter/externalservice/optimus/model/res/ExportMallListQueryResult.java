package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res;

import com.wosai.trade.optimus.api.result.mall.MallListQueryForInnerResult;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> Date: 2024/5/10 Time: 12:06
 */
@Getter
@Setter
@ToString(callSuper = true)
@Accessors(chain = true)
public class ExportMallListQueryResult {

    private List<MallListQueryForInnerResult> mallListQueryResults;

    public static ExportMallListQueryResult newInstance(List<MallListQueryForInnerResult> data) {
        ExportMallListQueryResult moduleMallQueryResult = new ExportMallListQueryResult();
        moduleMallQueryResult.setMallListQueryResults(data);
        return moduleMallQueryResult;
    }

    public boolean isMallExist() {
        return CollectionUtils.isNotEmpty(mallListQueryResults);
    }

    public boolean isMallNotExist() {
        return !isMallExist();
    }

    public void checkExist() {
        if (isMallNotExist()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.MALL_NOT_EXIST);
        }
    }

    public void checkMallPermission(int mallSize, List<String> ownerMerchantSnList) {
        if (mallSize != mallListQueryResults.size()) {
            throw new OptimusExportBizException(OptimusExportRespCodeEnum.MALL_NOT_EXIST);
        }

        mallListQueryResults.stream()
                .filter(m -> !ownerMerchantSnList.contains(m.getSeller().getMerchantSn()))
                .findAny()
                .ifPresent(m -> {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.MALL_NO_PERMISSION_ERROR);
                });
    }

    public void checkTerminalSnList() {
        for (MallListQueryForInnerResult mallListQueryForInnerResult : mallListQueryResults) {
            if (CollectionUtils.isEmpty(mallListQueryForInnerResult.getAllUsedTerminalSnList())) {
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.TERMINAL_INFO_NOT_FOUND);
            }
        }
    }

    public MallIterator mallIterator() {
        return new MallIterator();
    }


    public ExportMallHolder getFirstMallHolder() {
        return Optional.ofNullable(mallListQueryResults)
                .flatMap(list -> list.stream().findFirst())
                .map(ExportMallHolder::newInstance)
                .orElse(ExportMallHolder.DEFAULT_EMPTY_INSTANCE);
    }


    public class MallIterator {
        private int currentPosition;
        private List<MallListQueryForInnerResult> items;


        private void lazyLoad() {
            if (Objects.isNull(items)) {
                items = mallListQueryResults;
            }
        }

        public boolean hasNext() {
            lazyLoad();
            return currentPosition < items.size();
        }

        public ExportMallHolder next() {
//            currentOrderItemModel = items.get(currentPosition++);
            return ExportMallHolder.builder()
                    .mall(items.get(currentPosition++))
                    .build();
        }

    }


    @Builder(access = AccessLevel.PRIVATE)
    public static final class ExportMallHolder {
        private static final ExportMallHolder DEFAULT_EMPTY_INSTANCE = ExportMallHolder.builder().build();
        private final MallListQueryForInnerResult mall;

        private static ExportMallHolder newInstance(MallListQueryForInnerResult mall) {
            return ExportMallHolder.builder()
                    .mall(mall)
                    .build();
        }

        public boolean isMallExist(){
            return !isMallNotExist();
        }

        public boolean isMallNotExist() {
            return Objects.isNull(mall);
        }

        public String getMallSn() {
            if (isMallExist()) {
                return mall.getMallSn();
            }
            return null;
        }

        public String getMallSignature() {
            if (isMallExist()) {
                return mall.getSignature();
            }
            return null;
        }

        public String getMallName() {
            if (isMallExist()) {
                return mall.getMallName();
            }
            return null;
        }

        public String getMerchantSn() {
            if (isMallExist()) {
                return mall.getSeller().getMerchantSn();
            }
            return null;
        }

        public String getMerchantName() {
            if (isMallExist()) {
                return mall.getSeller().getMerchantName();
            }
            return null;
        }

        public String getStoreSn() {
            if (isMallExist()) {
                return mall.getSeller().getStoreSn();
            }
            return null;
        }

        public String getStoreName() {
            if (isMallExist()) {
                return mall.getSeller().getStoreName();
            }
            return null;
        }
    }
}
