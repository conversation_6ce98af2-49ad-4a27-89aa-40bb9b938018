package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.threadpoolconfig.po;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Date: 2023/5/5 Time: 3:57 PM
 */
@Data
public class ThreadPoolConfigPO {

    @NotNull(message = "核心线程数不能为空")
    private Integer corePoolSize;
    @NotNull(message = "最大线程数不能为空")
    private Integer maxPoolSize;
    @NotNull(message = "最大线程数保持时长不能为空")
    private Long keepAlive;
    @NotNull(message = "最大线程数保持时长单位不能为空")
    private TimeUnit keepAliveTimeUnit;
    @NotNull(message = "线程池队列长度不能为空")
    private Integer queueSize;
    @NotNull(message = "线程池队列是否公平不能为空")
    private Boolean queueFair;

}
