package com.wosai.trade.optimus.export.infrastructure.support;

import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.CoreBusinessClient;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res.SimpleStoreInfoQueryResult;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.merchantuser.MerchantUserClient;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> Date: 2023/7/21 Time: 11:36
 */
@Component
public class MerchantUserOwnerStoreFacade {

    private final CoreBusinessClient coreBusinessClient;
    private final MerchantUserClient merchantUserClient;

    private MerchantUserOwnerStoreFacade(CoreBusinessClient coreBusinessClient, MerchantUserClient merchantUserClient) {
        this.coreBusinessClient = coreBusinessClient;
        this.merchantUserClient = merchantUserClient;
    }


    public SimpleStoreInfoQueryResult queryOwnerStore(String merchantUserId) {
        List<String> storeIds = merchantUserClient.getOwnerStoreIdsByMerchantUserId(merchantUserId);
        if (CollectionUtils.isEmpty(storeIds)) {
            return SimpleStoreInfoQueryResult.EMPTY_INSTANCE;
        }
        return coreBusinessClient.getSimpleStoreInfoByIds(storeIds);
    }


}
