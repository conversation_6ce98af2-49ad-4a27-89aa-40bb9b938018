package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.req;

import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR>
 * @date 2023/7/24
 */
@Getter
@SuperBuilder(toBuilder = true)
@Jacksonized
@ToString(callSuper = true)
public class StoreInfoQueryRequest extends BaseRequest {

    private String storeId;
    private String storeSn;
}
