package com.wosai.trade.optimus.export.infrastructure.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.wosai.middleware.aliyun.oss.DynamicCredentialsProvider;
import com.wosai.middleware.vault.Vault;
import com.wosai.middleware.vault.VaultConfig;
import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> Date: 2021/9/10 Time: 6:25 下午
 */
@Configuration
@EnableConfigurationProperties({AliyunOssConfig.OssProperties.class})
public class AliyunOssConfig {

    private final OssProperties ossProperties;

    public AliyunOssConfig(OssProperties ossProperties) {
        this.ossProperties = ossProperties;
    }


    @Bean
    @SneakyThrows
    public CredentialsProvider credentialsProvider() {
        return new DynamicCredentialsProvider(Vault.autoload(ossProperties.getGroupName(), VaultConfig.builder().build()));
    }

    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(ossProperties.getEndpoint(), credentialsProvider());
    }


    @Data
    @ConfigurationProperties(prefix = "aliyun.oss")
    public static class OssProperties {
        private String endpoint;
        private String groupName;
    }
}
