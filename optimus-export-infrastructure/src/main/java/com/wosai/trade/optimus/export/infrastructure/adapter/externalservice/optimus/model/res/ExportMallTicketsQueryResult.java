package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.optimus.model.res;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.optimus.api.model.res.MallInfoModel;
import com.wosai.trade.optimus.api.model.res.OptimusTicketsSellerModel;
import com.wosai.trade.optimus.api.result.aftersale.MallTicketsForInnerQueryResult;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view.MallTicketDetailItemView;
import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.view.MallTicketDetailView;
import lombok.Builder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.*;

/**
 * <AUTHOR> Date: 2024/8/7 Time: 17:34
 */
@Builder
public class ExportMallTicketsQueryResult {

    public static final ExportMallTicketsQueryResult DEFAULT_EMPTY_INSTANCE = ExportMallTicketsQueryResult.builder()
            .tickets(List.of())
            .build();


    private final Integer querySize;
    private final List<ExportTicketsModel> tickets;


    public static ExportMallTicketsQueryResult newInstance(Integer querySize, List<MallTicketsForInnerQueryResult> tickets) {
        if (CollectionUtils.isEmpty(tickets)) {
            return DEFAULT_EMPTY_INSTANCE;
        }
        return ExportMallTicketsQueryResult.builder()
                .querySize(querySize)
                .tickets(JsonUtils.convertToObject(tickets, new TypeReference<>() {}))
                .build();
    }


    public boolean isTicketNotExist() {
        return this == DEFAULT_EMPTY_INSTANCE || CollectionUtils.isEmpty(tickets);
    }

    public boolean isTicketExist() {
        return !isTicketNotExist();
    }

    public boolean isQueryEnd() {
        return (Objects.nonNull(querySize) && querySize > tickets.size()) || isTicketNotExist();
    }

    public String getTailTicketSn() {
        return tickets.get(tickets.size() - 1).getSn();
    }


    public MallTicketDetailView genTicketFileMallDetailView() {
        MallTicketDetailView.TicketFileViewBuilder builder = MallTicketDetailView.builder();
        if (CollectionUtils.isNotEmpty(tickets)) {
            Map<Integer, String> sceneFileHeaders = null;
            Map<Integer, String> floatFileHeaders = null;
            for (ExportTicketsModel ticket : tickets) {
                //改造后单次只能导出一个商城的订单，场景信息文件头是相同的，不重复生成
                if (MapUtils.isEmpty(sceneFileHeaders)) {
                    sceneFileHeaders = ticket.getSceneFieldExpandedNames();
                    builder.sceneFileHeaders(sceneFileHeaders);
                }
//                if (MapUtils.isEmpty(floatFileHeaders)) {
//                    floatFileHeaders = ticket.genFloatFileHeaders();
//                    builder.floatFileHeaders(floatFileHeaders);
//                }

                builder.item(ticket.genTicketFileItemView())
                        .sceneContentItem(ticket.getSceneFieldExpandedContents());
            }
        }
        return builder.build();
    }

    public static class ExportTicketsModel extends MallTicketsForInnerQueryResult {
//        private static final Pair<Integer/*电子发票处于浮动头的位置*/, String> INVOICE_HEADER = Pair.of(0, "电子发票开具");
        public MallTicketDetailItemView genTicketFileItemView() {
            OptimusTicketsSellerModel seller = getSeller();
            MallInfoModel mallInfo = getMall();
            return MallTicketDetailItemView.builder()
                    .tradeTime(getTime().getCreatedAt())
                    .orderSn(getOrderSn())
                    .refundTsnList(getRefundTsnList())
                    .merchantSn(seller.getMerchantSn())
                    .merchantName(seller.getMerchantName())
                    .storeSn(seller.getStoreSn())
                    .storeName(seller.getStoreName())
                    .mallSn(mallInfo.getMallSn())
                    .mallName(mallInfo.getMallName())
                    .aftersaleTypeDesc(getAftersaleTypeDesc())
                    .aftersaleReason(getAftersaleReason())
                    .aftersaleAmount(getAmount().getApplyAmount())
                    .refundAmount(getAmount().getRefundedAmount())
                    .ticketStateDesc(getStateDesc())
                    .aftersaleItems(Optional.ofNullable(getItems().getItems())
                            .map(list -> list.stream()
                                    .map(item -> MallTicketDetailItemView.ExportAftersaleItem.builder()
                                            .title(item.getTitle())
                                            .skuDesc(item.getSkuDesc())
                                            .quantity(item.getQuantity())
                                            .unit(item.getUnit())
                                            .build())
                                    .toList())
                            .orElse(Collections.emptyList()))
                    .build();
        }

//        public Map<Integer, String> genFloatFileHeaders() {
//            Map<Integer, String> floatHeaders = Maps.newHashMap();
//            if (isExistInvoiceStatus()) {
//                floatHeaders.put(INVOICE_HEADER.getKey(), INVOICE_HEADER.getValue());
//            }
//            return floatHeaders;
//        }

//        public Map<Integer, String> genFloatContent() {
//            Map<Integer, String> floatContent = Maps.newHashMap();
//            if (isExistInvoiceStatus()) {
//                floatContent.put(INVOICE_HEADER.getKey(), getInvoiceStatusDesc());
//            }
//            return floatContent;
//        }

//        private boolean isExistInvoiceStatus() {
//            return StringUtils.isNotEmpty(getInvoiceStatusDesc());
//        }

//        private String getInvoiceStatusDesc() {
//            return Optional.ofNullable(getInvoice())
//                    .map(OrderInvoiceSimpleModel::getInvoiceStatusDesc).orElse(StringUtils.EMPTY);
//        }
    }
    
}
