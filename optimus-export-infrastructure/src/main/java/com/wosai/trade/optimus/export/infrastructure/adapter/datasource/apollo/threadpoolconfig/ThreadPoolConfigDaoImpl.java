package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.threadpoolconfig;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.general.util.ValidationUtils;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.BaseNamespaceDao;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.NamespaceEnum;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo.threadpoolconfig.po.ThreadPoolConfigPO;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR> Date: 2023/5/5 Time: 3:56 PM
 */
@Slf4j
@Component("threadPoolConfigDao")
public class ThreadPoolConfigDaoImpl extends BaseNamespaceDao implements ThreadPoolConfigDao {
    private static final ConcurrentMap<String, ThreadPoolExecutor> EXECUTOR_SERVICE_MAP
            = new ConcurrentHashMap<>(16);

    @Override
    public ThreadPoolExecutor query(String scene) {
        if (Objects.nonNull(scene)) {
            ThreadPoolExecutor executorService = EXECUTOR_SERVICE_MAP.get(scene);
            if (Objects.nonNull(executorService) && !executorService.isShutdown()) {
                return executorService;
            }
        }
        return null;
    }

    @PostConstruct
    private void loadAll() {
        Set<String> propNames = getAllPropNames();
        for (String propName : propNames) {
            upsertExecutor(propName);
        }
    }

    @PreDestroy
    private void destroy() {
        EXECUTOR_SERVICE_MAP.values().forEach(ExecutorService::shutdown);
    }

    @Override
    protected void reload(Set<String> changedKeys) {
        if (CollectionUtils.isEmpty(changedKeys)) {
            return;
        }
        for (String key : changedKeys) {
            upsertExecutor(key);
        }
    }


    @Override
    protected String getNameSpace() {
        return NamespaceEnum.EXTERNAL_INVOKE_THREAD_POOL.getCode();
    }

    private void upsertExecutor(String key) {
        ThreadPoolConfigPO threadPoolConfigPO = loadConfig(new TypeReference<>() {}, key);
        if (Objects.nonNull(threadPoolConfigPO)) {
            ValidationUtils.ValidationResult validationResult = ValidationUtils.validate(threadPoolConfigPO);
            if (validationResult.isPass()) {
                ThreadPoolExecutor oriExecutor = EXECUTOR_SERVICE_MAP.get(key);
                ThreadPoolExecutor newExecutor = new ThreadPoolExecutor(
                        threadPoolConfigPO.getCorePoolSize()
                        , threadPoolConfigPO.getMaxPoolSize()
                        , threadPoolConfigPO.getKeepAlive()
                        , threadPoolConfigPO.getKeepAliveTimeUnit()
                        , new ArrayBlockingQueue<>(threadPoolConfigPO.getQueueSize()
                            , threadPoolConfigPO.getQueueFair())
                        , new ExternalInvokeThreadFactory(key)
                        , new ThreadPoolExecutor.AbortPolicy());
                EXECUTOR_SERVICE_MAP.put(key, newExecutor);
                if (Objects.nonNull(oriExecutor)) {
                    oriExecutor.shutdown();
                }
                return;
            }
            log.error("[线程池配置更新]>>>>>>数据完整性校验失败, 错误信息: {}", validationResult.getMsg());
        }
    }

    private static class ExternalInvokeThreadFactory implements ThreadFactory {
        private final String namePrefix;
        private final AtomicLong sequence = new AtomicLong(0);

        public ExternalInvokeThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(@NotNull Runnable r) {
            Thread thread = new Thread(r, namePrefix + "-external-invoke-pool-" + sequence.getAndIncrement());
            if (thread.isDaemon()) {
                thread.setDaemon(false);
            }
            return thread;
        }
    }
}
