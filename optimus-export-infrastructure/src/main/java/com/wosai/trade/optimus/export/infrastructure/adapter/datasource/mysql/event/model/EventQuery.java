package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.model;

import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventStateEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.enums.EventTypeEnum;
import com.wosai.trade.optimus.export.domain.aggregate.event.model.query.EventAggrQuery;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.BaseQuery;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/7/15
 */
@Setter
@SuperBuilder(toBuilder = true)
public class EventQuery extends BaseQuery {

    private Long id;
    private Byte state;
    private Byte type;
    private String associatedSn;
    private LocalDateTime nextProcessTime;
    private List<Long> idList;


    public static EventQuery genEventQuery(EventAggrQuery aggrQuery) {
        EventQuery eventQuery =  EventQuery.builder()
                .id(aggrQuery.getId())
                .associatedSn(aggrQuery.getAssociatedSn())
                .nextProcessTime(aggrQuery.getNextProcessTime())
                .idList(aggrQuery.getIdList())
                .querySize(aggrQuery.getCount())
                .build();
        EventStateEnum state = aggrQuery.getState();
        if (Objects.nonNull(state)) {
            eventQuery.setState(state.getCode());
        }
        EventTypeEnum type = aggrQuery.getType();
        if (Objects.nonNull(type)) {
            eventQuery.setType(type.getCode());
        }

        return eventQuery;
    }
}
