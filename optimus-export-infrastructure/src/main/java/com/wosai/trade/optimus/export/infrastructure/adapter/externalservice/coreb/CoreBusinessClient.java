package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb;

import com.google.common.collect.ImmutableMap;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.template.InvokeProcessor;
import com.wosai.trade.optimus.export.common.template.InvokeTemplate;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.req.*;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res.*;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.exception.CoreException;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Date: 2023/6/12 Time: 5:34 PM
 */
@Slf4j
@Component
public class CoreBusinessClient {

    private static final String STORE_SN = "store_sn";
    private static final String STORE_IDS = "store_ids";
    private static final PageInfo pageInfoSingle = new PageInfo(1, 1);
    private static final int PAGE1 = 1;

    @Resource
    private TerminalService terminalService;
    @Resource
    private MerchantService merchantService;
    @Resource
    private StoreService storeService;
    @Resource
    private SupportService supportService;


    public MerchantQueryResult queryMerchant(MerchantQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected MerchantQueryResult invoke(MerchantQueryRequest request) throws Throwable {
                log.info("[商户中心查询商户信息]>>>>>>入参: {}", request.toJsonString());
                Map<?, ?> merchant = null;
                if (StringUtils.isNotEmpty(request.getMerchantId())) {
                    merchant = merchantService.getMerchantByMerchantId(request.getMerchantId());
                } else if (StringUtils.isNotEmpty(request.getMerchantSn())) {
                    merchant = merchantService.getMerchantByMerchantSn(request.getMerchantSn());
                }
                log.info("[商户中心查询商户信息]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(merchant));
                if (Objects.isNull(merchant)) {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.MERCHANT_INFO_NOT_FOUND);
                }
                return JsonUtils.convertToObject(merchant, MerchantQueryResult.class);
            }
        });
    }

    public List<MerchantQueryResult> getMerchantList(MerchantQueryListRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected List<MerchantQueryResult> invoke(MerchantQueryListRequest request) throws Throwable {
                log.info("[商户中心批量查询商户信息]>>>>>>入参: {}", request.toJsonString());

                ListResult listResult = merchantService.findMerchants(request.getPageInfo(), request.getMap());

                log.info("[商户中心批量查询商户信息]>>>>>>出参数量: {}", JsonUtils.toJsonStringIgnoreException(listResult.getTotal()));
                if(CollectionUtils.isEmpty(listResult.getRecords())){
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.MERCHANT_INFO_NOT_FOUND);
                }
                return listResult.getRecords().stream().map(t -> JsonUtils.convertToObject(t, MerchantQueryResult.class)).collect(Collectors.toList());
            }
        });
    }

    public SimpleStoreInfoQueryResult getSimpleStoreInfoByIds(List<String> storeIds) {
        return InvokeProcessor.process(storeIds, new InvokeTemplate<>() {
            @Override
            protected SimpleStoreInfoQueryResult invoke(List<String> storeIds) throws Throwable {
                Map<String, Object> request = ImmutableMap.of("store_ids", storeIds);
                log.info("[商户中心查询门店基本信息]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(storeIds));
                ListResult listResult = storeService.getSimpleStoreListByMerchantIdOrStoreIdsFromSlaveDb(request, null);
                log.info("[商户中心查询门店基本信息]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(listResult));
                if (Objects.isNull(listResult)) {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.STORE_INFO_NOT_FOUND);
                }
                return SimpleStoreInfoQueryResult.genSimpleStoreInfoQueryResult(listResult.getRecords());
            }
        });
    }

    public List<StoreInfoQueryResult> getStoreInfoByIdList(List<String> storeIdList) {
        return InvokeProcessor.process(storeIdList, new InvokeTemplate<>() {
            @Override
            protected List<StoreInfoQueryResult> invoke(List<String> storeIds) throws Throwable {
                Map<String, Object> request = ImmutableMap.of(STORE_IDS, storeIds);
                log.info("[商户中心查询门店信息]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(request));
                PageInfo pageInfo = new PageInfo(PAGE1, storeIdList.size());
                ListResult listResult = storeService.findStores(pageInfo, request);
                log.info("[商户中心查询门店信息]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(listResult));
                if (Objects.isNull(listResult)) {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.STORE_INFO_NOT_FOUND);
                }
                return StoreInfoQueryResult.genStoreQueryResultList(listResult.getRecords());
            }
        });
    }

    public StoreInfoQueryResult getStoreInfoBySn(String storeSn) {
        return InvokeProcessor.process(storeSn, new InvokeTemplate<>() {
            @Override
            protected StoreInfoQueryResult invoke(String storeSn)  {
                Map<String, Object> request = ImmutableMap.of(STORE_SN, storeSn);
                log.info("[商户中心查询门店信息]>>>>>>入参: {}", JsonUtils.toJsonStringIgnoreException(request));
                ListResult listResult = storeService.findStores(pageInfoSingle, request);
                log.info("[商户中心查询门店信息]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(listResult));
                if (Objects.isNull(listResult)) {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.STORE_INFO_NOT_FOUND);
                }
                return StoreInfoQueryResult.genStoreQueryResult(listResult.getRecords());
            }
        });
    }

    public StoreInfoQueryResult queryStoreInfo(StoreInfoQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected StoreInfoQueryResult invoke(StoreInfoQueryRequest request) throws Throwable {
                log.info("[商户中心查询门店信息]>>>>>>入参: {}", request.toJsonString());
                Map<?, ?> store = null;
                if (StringUtils.isNotEmpty(request.getStoreId())) {
                    store = storeService.getStoreByStoreId(request.getStoreId());
                } else if (StringUtils.isNotEmpty(request.getStoreSn())) {
                    store = storeService.getStoreByStoreSn(request.getStoreSn());
                }
                log.info("[商户中心查询门店信息]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(store));
                if (Objects.isNull(store)) {
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.STORE_INFO_NOT_FOUND);
                }
                return JsonUtils.convertToObject(store, StoreInfoQueryResult.class);
            }
        });
    }

    public MerchantBaseInfoQueryResult queryMerchantBaseInfo(MerchantBaseInfoQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected MerchantBaseInfoQueryResult invoke(MerchantBaseInfoQueryRequest merchantBaseInfoQueryRequest) throws Throwable {
                log.info("[商户中心查询基础参数]>>>>>>入参: {}", merchantBaseInfoQueryRequest);
                Map<?, ?> result = supportService.getBasicParams(request.getStoreSn(), request.getTerminalSn());
                log.info("[商户中心查询基础参数]>>>>>>出参: {}", result);
                if (Objects.nonNull(result)) {
                    return JsonUtils.convertToObject(result, MerchantBaseInfoQueryResult.class);
                }
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.MERCHANT_INFO_NOT_FOUND);
            }

            @Override
            protected MerchantBaseInfoQueryResult onFailure(MerchantBaseInfoQueryRequest merchantBaseInfoQueryRequest
                    , Throwable throwable) throws Throwable {
                if (throwable instanceof CoreException) {
                    log.warn("[商户中心查询基础参数]>>>>>>商户中心业务异常, 异常栈: ", throwable);
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum
                            .EXTERNAL_SERVICE_ERROR.getCode(), throwable.getMessage());
                }
                //异常重试一次
                Map<?, ?> result = supportService.getBasicParams(null, request.getTerminalSn());
                if (Objects.nonNull(result)) {
                    return JsonUtils.convertToObject(result, MerchantBaseInfoQueryResult.class);
                }
                throw throwable;
            }
        });
    }

    public MerchantTradeParamsQueryResult queryMerchantTradeParams(MerchantTradeParamsQueryRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {

            @Override
            protected MerchantTradeParamsQueryResult invoke(MerchantTradeParamsQueryRequest request) throws Throwable {
                log.info("[查询商户支付参数]>>>>>>入参: {}", request.toJsonString());
                Map<?, ?> result = supportService.getAllParamsWithTradeApp(request.getStoreSn(), null
                        , request.getPayToolCode(), request.getPayMode(), request.getTradeApp());
                log.info("[查询商户支付参数]>>>>>>出参: {}", JsonUtils.toJsonStringIgnoreException(result));
                if (MapUtils.isEmpty(result)) {
                    return MerchantTradeParamsQueryResult.builder()
                            .payTool(request.getPayToolCode())
                            .build();
                }
                return MerchantTradeParamsQueryResult.newInstance(request.getPayToolCode(), result);
            }

            @Override
            protected MerchantTradeParamsQueryResult onFailure(MerchantTradeParamsQueryRequest request, Throwable throwable) throws Throwable {
                if (throwable instanceof CoreException) {
                    log.warn("[查询商户支付参数]>>>>>>商户中心业务异常, 异常栈: ", throwable);
                    return MerchantTradeParamsQueryResult.newEmptyInstance();
                }
                log.warn("[查询商户支付参数]>>>>>>调用失败, 异常栈: ", throwable);
                return MerchantTradeParamsQueryResult.builder()
                        .payTool(request.getPayToolCode())
                        .build();
            }
        });
    }



}
