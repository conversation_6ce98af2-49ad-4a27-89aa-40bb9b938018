package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.apollo;

import com.ctrip.framework.apollo.Config;

import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> Date: 2023/5/5 Time: 4:40 PM
 */
public abstract class BaseNamespaceDao extends BaseDao {


    protected Set<String> getAllPropNames() {
        Config config = getApolloConfig();
        if (Objects.nonNull(config)) {
            return config.getPropertyNames();
        }
        return null;
    }


    @Override
    protected void load() {}

    @Override
    protected String getNameSpace() {
        return null;
    }

    @Override
    protected String getConfigName() {
        return null;
    }
}
