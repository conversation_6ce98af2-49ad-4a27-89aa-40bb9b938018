package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.req;

import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.BaseRequest;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2023/7/6
 */
@Getter
@SuperBuilder(toBuilder = true)
public class ActivatedTerminalCreateRequest extends BaseRequest {

    private String storeSn;
    private String name;
    private String vendorAppAppId;
}
