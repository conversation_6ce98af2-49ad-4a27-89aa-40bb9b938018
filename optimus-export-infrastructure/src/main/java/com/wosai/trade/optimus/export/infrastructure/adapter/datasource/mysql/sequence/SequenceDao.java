package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.sequence;

import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.sequence.po.SequencePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/7/2
 */
@Mapper
public interface SequenceDao {

    int update(SequencePO po);

    SequencePO selectForUpdate(@Param("namespace") String namespace);
}
