package com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.model;


import com.wosai.trade.optimus.export.domain.aggregate.mall.order.model.query.ExportMallOrderAggrQuery;
import com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.BaseQuery;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/7/16 Time: 17:35
 */
@Getter
@SuperBuilder(toBuilder = true)
public class ExportMallOrderQuery extends BaseQuery {

    private Long id;
    private String ownerId;
    private String ownerUserId;
    private String exportInfoDigest;
    private Byte receiveMethod;

    public static ExportMallOrderQuery genMallExportOrderQuery(ExportMallOrderAggrQuery aggrQuery) {
        ExportMallOrderQuery.ExportMallOrderQueryBuilder<?, ?> builder = ExportMallOrderQuery
                .builder()
                .id(aggrQuery.getId())
                .ownerId(aggrQuery.getOwnerId())
                .ownerUserId(aggrQuery.getOwnerUserId())
                .exportInfoDigest(aggrQuery.getExportInfoDigest())
                .offset(aggrQuery.getOffset())
                .querySize(aggrQuery.getQuerySize())
                .sortField(aggrQuery.getSortField())
                .isDesc(aggrQuery.isDesc());
        if (Objects.nonNull(aggrQuery.getReceiveMethod())) {
            builder.receiveMethod(aggrQuery.getReceiveMethod().getCode());
        }
        return builder.build();
    }


    {
        checkParams();
    }

}
