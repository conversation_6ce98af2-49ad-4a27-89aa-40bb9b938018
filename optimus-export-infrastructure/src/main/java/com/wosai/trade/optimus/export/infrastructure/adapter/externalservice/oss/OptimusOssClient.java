package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.wosai.trade.optimus.export.common.exception.OptimusExportBizException;
import com.wosai.trade.optimus.export.common.exception.enums.OptimusExportRespCodeEnum;
import com.wosai.trade.optimus.export.common.template.InvokeProcessor;
import com.wosai.trade.optimus.export.common.template.InvokeTemplate;
import com.wosai.trade.optimus.export.common.template.WithoutResultTemplate;
import com.wosai.trade.optimus.export.common.util.FileUtils;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.oss.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.net.URL;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2021/9/10 Time: 4:24 下午
 */
@Slf4j
@Component
public class OptimusOssClient {
    @Resource
    private OSS ossClient;

    public void upload(OssUploadRequest request) {
        InvokeProcessor.processWithoutResult(request, new WithoutResultTemplate<>() {
            @Override
            protected void invoke(OssUploadRequest ossUploadRequest) {
                log.info("[阿里云OSS对象存储]>>>>>>入参: {}", JsonUtils.toJsonString(request));
                PutObjectRequest putObjectRequest = new PutObjectRequest(request.getBucketName()
                        , request.getPath(), new ByteArrayInputStream(request.getContent()));

                ObjectMetadata objectMetadata = new ObjectMetadata();
                objectMetadata.setExpirationTime(request.getExpirationTime(7, ChronoUnit.DAYS));
                objectMetadata.setContentDisposition(request.genAttachmentDisposition());
                putObjectRequest.setMetadata(objectMetadata);
                ossClient.putObject(putObjectRequest);
            }

            @Override
            protected void onFailure(OssUploadRequest ossUploadRequest, Throwable throwable) {
                log.error("[阿里云OSS对象存储]>>>>>>上传异常, 异常栈: ", throwable);
            }
        });
    }

    public byte[] download(OssDownloadRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected byte[] invoke(OssDownloadRequest ossDownloadRequest) throws Throwable {
                log.info("[阿里云OSS对象下载]>>>>>>入参: {}", JsonUtils.toJsonString(request));
                OSSObject ossObject = ossClient.getObject(request.getBucketName(), request.getPath());
                byte[] contentByteArr = IOUtils.toByteArray(ossObject.getObjectContent());
                if (Objects.isNull(contentByteArr) || contentByteArr.length == 0) {
                    //文件不存在
                    throw new OptimusExportBizException(OptimusExportRespCodeEnum.OSS_CONTENT_NOT_FOUND);
                }
                return contentByteArr;
            }

            @Override
            protected byte[] onFailure(OssDownloadRequest ossDownloadRequest, Throwable throwable) throws Throwable {
                log.error("[阿里云OSS对象下载]>>>>>>下载异常, 异常栈: ", throwable);
                throw new OptimusExportBizException(OptimusExportRespCodeEnum.OSS_CONTENT_NOT_FOUND);
            }
        });
    }

    public boolean isObjectExist(OssCheckObjectExistRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected Boolean invoke(OssCheckObjectExistRequest ossCheckObjectExistRequest) throws Throwable {
                log.info("[阿里云OSS判断对象存在性]>>>>>>入参: {}", JsonUtils.toJsonString(request));
                return ossClient.doesObjectExist(request.getBucketName(), request.getPath());
            }

            @Override
            protected Boolean onFailure(OssCheckObjectExistRequest ossCheckObjectExistRequest, Throwable throwable) throws Throwable {
                log.error("[阿里云OSS判断对象存在性]>>>>>调用异常, 异常栈: ", throwable);
                return false;
            }
        });
    }

    public String isObjectExistReturnUrl(OssCheckObjectExistRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected String invoke(OssCheckObjectExistRequest request) throws Throwable {
                log.info("[阿里云OSS判断对象存在性]>>>>>>入参: {}", JsonUtils.toJsonString(request));
                boolean exist = ossClient.doesObjectExist(request.getBucketName(), request.getPath());
                if (!exist) {
                   return null;
                }
                // 对象存在，生成临时访问链接
                Date expiration = request.getExpirationTime(1, ChronoUnit.HOURS); // 有效期为1小时
                GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(request.getBucketName(), request.getPath());
                generatePresignedUrlRequest.setExpiration(expiration);
                URL url = ossClient.generatePresignedUrl(generatePresignedUrlRequest);
                log.info("[阿里云OSS判断对象存在性]>>>>>>出参: {}", JsonUtils.toJsonString(url));
                return url.toString();
            }

            @Override
            protected String onFailure(OssCheckObjectExistRequest ossCheckObjectExistRequest, Throwable throwable) throws Throwable {
                log.error("[阿里云OSS判断对象存在性]>>>>>调用异常, 异常栈: ", throwable);
                return null;
            }
        });
    }



    public OssUrlUploadAndReturnResult uploadAndReturnUrl(OssUploadRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected OssUrlUploadAndReturnResult invoke(OssUploadRequest ossUploadRequest) throws Throwable {
                log.info("[阿里云OSS对象存储]>>>>>>入参: {}", JsonUtils.toJsonString(request));
                PutObjectRequest putObjectRequest = new PutObjectRequest(request.getBucketName()
                        , request.getPath(), new ByteArrayInputStream(request.getContent()));

                ObjectMetadata objectMetadata = new ObjectMetadata();
                Date expirationTime = request.getExpirationTime(7, ChronoUnit.DAYS);
                objectMetadata.setExpirationTime(expirationTime);
                putObjectRequest.setMetadata(objectMetadata);
                ossClient.putObject(putObjectRequest);
                String url = ossClient.generatePresignedUrl(request.getBucketName(), request.getPath(), expirationTime).toString();
                log.info("[阿里云OSS对象存储]>>>>>>出参: {}", JsonUtils.toJsonString(url));
                return OssUrlUploadAndReturnResult.newInstance(url);
            }

            @Override
            protected OssUrlUploadAndReturnResult onFailure(OssUploadRequest ossUploadRequest, Throwable throwable) {
                log.error("[阿里云OSS对象存储]>>>>>>上传异常, 异常栈: ", throwable);
                return OssUrlUploadAndReturnResult.DEFAULT_INSTANCE;
            }
        });
    }


    public OssGeneratePreSignedUrlResult generatePresignedUrl(OssUploadRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected OssGeneratePreSignedUrlResult invoke(OssUploadRequest ossUploadRequest) throws Throwable {
                log.info("[获取阿里云OSS对象存储链接]>>>>>>入参: {}", JsonUtils.toJsonString(request));

                String url = ossClient.generatePresignedUrl(request.getBucketName(), request.getPath(),
                        request.getExpirationTime(7, ChronoUnit.DAYS)).toString();
                log.info("[获取阿里云OSS对象存储链接]>>>>>>出参: {}", JsonUtils.toJsonString(url));
                return OssGeneratePreSignedUrlResult.newInstance(FileUtils.genHttpsUrl(url));
            }

            @Override
            protected OssGeneratePreSignedUrlResult onFailure(OssUploadRequest ossUploadRequest, Throwable throwable) {
                log.error("[获取阿里云OSS对象存储链接]>>>>>>上传异常, 异常栈: ", throwable);
                return OssGeneratePreSignedUrlResult.DEFAULT_INSTANCE;
            }
        });
    }

}
