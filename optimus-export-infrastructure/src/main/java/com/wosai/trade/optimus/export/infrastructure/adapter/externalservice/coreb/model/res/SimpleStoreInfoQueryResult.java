package com.wosai.trade.optimus.export.infrastructure.adapter.externalservice.coreb.model.res;

import com.beust.jcommander.internal.Maps;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.trade.optimus.export.common.util.JsonUtils;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/18
 */
@Getter
@Builder(access = AccessLevel.PROTECTED)
public class SimpleStoreInfoQueryResult {
    public static final SimpleStoreInfoQueryResult EMPTY_INSTANCE = SimpleStoreInfoQueryResult.builder().build();

    private List<OptimusSimpleStoreModel> stores;

    public static SimpleStoreInfoQueryResult genSimpleStoreInfoQueryResult(List<Map> records) {
        if (CollectionUtils.isEmpty(records)) {
            return EMPTY_INSTANCE;
        }
        return SimpleStoreInfoQueryResult.builder()
                .stores(JsonUtils.convertToObject(records, new TypeReference<>() {}))
                .build();
    }

    public List<String> genContainsStoreSnList(List<String> storeSnList) {
        if (CollectionUtils.isEmpty(storeSnList) || CollectionUtils.isEmpty(stores)) {
            return List.of();
        }
        return stores.stream()
                .map(OptimusSimpleStoreModel::getSn)
                .filter(storeSnList::contains)
                .collect(Collectors.toList());
    }

    public List<String> getStoreSnList() {
        if (isExistStore()) {
            return stores.stream().map(OptimusSimpleStoreModel::getSn).toList();
        }
        return null;
    }


    public Map<String, String> genStoreNameMap() {
        if (isExistStore()) {
            return stores.stream()
                    .collect(Collectors.toMap(
                            OptimusSimpleStoreModel::getId,
                            OptimusSimpleStoreModel::getName,
                            (v1 ,v2) -> v2));
        }
        return Maps.newHashMap();
    }

    public boolean isExistStore() {
        return CollectionUtils.isNotEmpty(stores);
    }

    @JsonIgnore
    public Map<String, String> genSnNameMap() {
        if(isExistStore()){
            return stores.stream()
                    .collect(Collectors.toMap(
                            OptimusSimpleStoreModel::getSn,
                            OptimusSimpleStoreModel::getName,
                            (v1 ,v2) -> v2));
        }
        return Map.of();
    }

}
