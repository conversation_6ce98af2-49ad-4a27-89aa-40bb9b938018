spring.application.name=optimus-export
spring.mvc.servlet.load-on-startup=1
server.tomcat.mbeanregistry.enabled=true

app.id=optimus-export

#servletå®¹å¨éç½®
server.tomcat.threads.max=1000
server.tomcat.threads.min-spare=100
server.tomcat.accept-count=1000
server.tomcat.connection-timeout=10s

mybatis.config-location=classpath:mybatis/mybatis-config.xml
mybatis.mapper-locations=classpath:mybatis/**/*Mapper.xml

#é£ä¹¦éç¥éç½®
feishu.notify.url=https://open.feishu.cn/open-apis/bot/v2/hook/b21cfb32-61a5-4d06-bfea-23cbd93a841d
feishu.notify.userids=005218

#kafkaæ¶è´¹èå¼å³
kafka.consumer.enable=true
#kafkaæ¶æ¯å ç§¯çæ§å¼å³
kafka.monitor.enable=true

#ç«å±±appid
volcano.app.id=10000038
#ç«å±±æ°æ®åæ­¥topic
spring.kafka.producer.topic.volcano=analytics_data_volcengine_push

#email
spring.mail.host=smtpdm.aliyun.com
spring.mail.port=465
spring.mail.username=<EMAIL>
spring.mail.password=ABwosai123456
spring.mail.protocol=smtp
spring.mail.properties[mail.smtp.auth]=true
spring.mail.properties[mail.smtp.starttls.enable]=true
spring.mail.properties[mail.smtp.starttls.required]=true
spring.mail.properties[mail.smtp.socketFactory.class]=javax.net.ssl.SSLSocketFactory
spring.mail.properties[mail.smtp.socketFactory.port]=465
spring.mail.properties[mail.smtp.connectiontimeout]=2000
spring.mail.properties[mail.smtp.timeout]=10000
spring.mail.properties[mail.smtp.writetimeout]=60000

#é¿éäºossèº«ä»½æ è¯
aliyun.oss.endpoint=http://oss-cn-hangzhou.aliyuncs.com
aliyun.oss.group-name=trade
aliyun.oss.bucket-name=sqb-trade