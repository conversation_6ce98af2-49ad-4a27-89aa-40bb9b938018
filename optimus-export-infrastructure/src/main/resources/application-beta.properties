
#æ°æ®æºéç½®
spring.datasource.type=com.zaxxer.hikari.util.DriverDataSource

#æ ¸å¿æ°æ®æº
spring.datasource.core.hikari.pool-name=core-pool
spring.datasource.core.hikari.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.core.hikari.jdbc-url=tk-optimus-export-optimus_export-3782?serverTimezone=Asia/Shanghai&socketTimeout=300000
spring.datasource.core.hikari.minimum-idle=5
spring.datasource.core.hikari.idle-timeout=300000
spring.datasource.core.hikari.maximum-pool-size=50
spring.datasource.core.hikari.connection-timeout=5000
spring.datasource.core.hikari.connection-test-query=select 1
spring.datasource.core.hikari.connection-init-sql=set names utf8mb4

#è¾¹ç¼æ°æ®æº
spring.datasource.periphery.hikari.pool-name=periphery-pool
spring.datasource.periphery.hikari.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.periphery.hikari.jdbc-url=tk-optimus-export-optimus_export-3782?serverTimezone=Asia/Shanghai&socketTimeout=300000
spring.datasource.periphery.hikari.minimum-idle=10
spring.datasource.periphery.hikari.idle-timeout=300000
spring.datasource.periphery.hikari.maximum-pool-size=100
spring.datasource.periphery.hikari.connection-timeout=5000
spring.datasource.periphery.hikari.connection-test-query=select 1
spring.datasource.periphery.hikari.connection-init-sql=set names utf8mb4

#ä¸å¡äºä»¶kafkaçäº§è&æ¶è´¹èéç½®
spring.kafka.bootstrap-servers=***************:9092,***************:9092,***************:9092
spring.kafka.listener.type=batch
spring.kafka.consumer.max-poll-records=500
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.group-id=optimus-export
spring.kafka.producer.batch-size=1000
spring.kafka.producer.acks=all
spring.kafka.producer.retries=10
spring.kafka.producer.properties.linger.ms=50
spring.kafka.producer.properties.max.block.ms=5000
spring.kafka.producer.properties.enable.idempotence=false

#ä¸å¡äºä»¶
spring.kafka.producer.topic.module=events_TRADE_optimus-export-event
spring.kafka.consumer.topic.module=events_TRADE_optimus-export-event

#ok-httpçè¾¹ç¼è¯·æ±éç½®
ok.http.periphery.core-pool-size=10
ok.http.periphery.max-pool-size=100
ok.http.periphery.call-timeout=2500

#ok-httpçæ ¸å¿è¯·æ±éç½®
ok.http.core.core-pool-size=20
ok.http.core.max-pool-size=100
ok.http.core.call-timeout=6500

#é£ä¹¦éç¥éç½®
feishu.notify.url=https://open.feishu.cn/open-apis/bot/v2/hook/760e1809-4d50-463b-a7af-d57ba472debe

#ååç³»ç»
service.rpc.optimus-core=http://optimus-core
service.rpc.core-business=http://core-business
service.rpc.merchant-user-service=http://merchant-user-service
service.rpc.upay-task-center=http://upay-task-center
#å®åç³»ç»
service.rpc.trade-aftersale=http://trade-aftersale