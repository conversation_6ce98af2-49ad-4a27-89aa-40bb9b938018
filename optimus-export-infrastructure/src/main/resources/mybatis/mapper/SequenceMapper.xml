<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.sequence.SequenceDao">

    <resultMap type="com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.sequence.po.SequencePO" id="SequenceMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="namespace" column="namespace" jdbcType="VARCHAR"/>
        <result property="serialNoBase" column="serial_no_base" jdbcType="BIGINT"/>
        <result property="batchSize" column="batch_size" jdbcType="INTEGER"/>
        <result property="ctime" column="ctime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="mtime" column="mtime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="table">
        `sequence`
    </sql>

    <sql id="allColumn">
        id,
        namespace,
        serial_no_base,
        batch_size,
        ctime,
        mtime,
        version
    </sql>

    <update id="update">
        update
        <include refid="table"/>
        <set>
            <if test="serialNoBase != null">
                serial_no_base = #{serialNoBase},
            </if>
            mtime = #{mtime},
            version = version + 1
        </set>
        where namespace = #{namespace} and version = #{version}
    </update>

    <select id="selectForUpdate" resultMap="SequenceMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        where namespace = #{namespace} for update
    </select>

</mapper>