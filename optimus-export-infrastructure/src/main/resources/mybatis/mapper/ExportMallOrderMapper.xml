<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.ExportMallOrderDao">
    <resultMap type="com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.order.po.ExportMallOrderPO" id="mallExportOrderResultMap">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="state" property="state" jdbcType="TINYINT"/>
        <result property="ownerId" column="owner_id" jdbcType="VARCHAR"/>
        <result property="ownerUserId" column="owner_user_id" jdbcType="VARCHAR"/>
        <result column="operator" property="operator"
                typeHandler="com.wosai.trade.optimus.export.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result column="export_info" property="exportInfo"
                typeHandler="com.wosai.trade.optimus.export.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result column="export_result" property="exportResult"
                typeHandler="com.wosai.trade.optimus.export.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result column="ext" property="ext"
                typeHandler="com.wosai.trade.optimus.export.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result column="expired_at" property="expiredAt"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result column="ctime" property="ctime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result column="mtime" property="mtime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>


    <sql id="table">
        export_mall_order
    </sql>

    <sql id="allColumn">
        id,
        `state`,
        owner_id,
        owner_user_id,
        operator,
        export_info,
        export_result,
        ext,
        expired_at,
        ctime,
        mtime,
        version
    </sql>

    <sql id="insertColumnValue">
        #{id},
        #{state},
        #{ownerId},
        #{ownerUserId},
        #{operator},
        #{exportInfo},
        #{exportResult},
        #{ext},
        #{expiredAt},
        #{ctime},
        #{mtime},
        #{version}
    </sql>

    <insert id="insert">
        insert into
        <include refid="table"/>
        (
        <include refid="allColumn"/>
        )
        values
        (
        <include refid="insertColumnValue"/>
        )
    </insert>

    <update id="update">
        update
        <include refid="table"/>
        <set>
            <if test="state != null">
                state = #{state},
            </if>
            <if test="exportInfo != null">
                export_info = #{exportInfo},
            </if>
            <if test="exportResult != null">
                export_result = #{exportResult},
            </if>
            <if test="ext != null">
                ext = #{ext},
            </if>
            mtime = #{mtime},
            version = version + 1
        </set>
        where id = #{id} and version = #{version}
    </update>

    <select id="select" resultMap="mallExportOrderResultMap" timeout="5">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <choose>
                <when test="id != null">
                    id = #{id}
                </when>
                <when test="ownerId != null and exportInfoDigest != null">
                    owner_id = #{ownerId}
                    and JSON_VALUE(export_info, '$.digest') = #{exportInfoDigest}
                </when>
                <otherwise>
                    false
                </otherwise>
            </choose>
        </where>
        limit 1
    </select>

    <select id="batchSelect" resultMap="mallExportOrderResultMap" timeout="5">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        where owner_id = #{ownerId}
            and owner_user_id = #{ownerUserId}
            <if test="receiveMethod != null">
                and JSON_VALUE(export_info, '$.receiveMethod') = #{receiveMethod}
            </if>
        <if test="sortField != null">
            order by `${sortField}`
            <if test="isDesc == false">
                asc
            </if>
            <if test="isDesc == true">
                desc
            </if>
        </if>
        <choose>
            <!--偏移量分页-->
            <when test="offset != null and querySize != null">
                limit ${offset}, ${querySize}
            </when>
            <when test="offset == null and querySize != null">
                limit ${querySize}
            </when>
            <otherwise>
                limit 1000
            </otherwise>
        </choose>
    </select>

    <select id="count" resultType="java.lang.Long" timeout="5">
        select
            count(*)
        from
        <include refid="table"/>
        where owner_id = #{ownerId}
        and owner_user_id = #{ownerUserId}
        <if test="receiveMethod != null">
            and JSON_VALUE(export_info, '$.receiveMethod') = #{receiveMethod}
        </if>
    </select>

    <delete id="delete">
        delete from
        <include refid="table"/>
        where expired_at <![CDATA[<=]]> #{expiredAt}
        limit ${count}
    </delete>

</mapper>