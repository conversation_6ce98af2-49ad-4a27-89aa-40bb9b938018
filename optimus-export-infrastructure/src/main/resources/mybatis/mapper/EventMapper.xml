<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.EventDao">

    <resultMap type="com.wosai.trade.optimus.export.infrastructure.adapter.datasource.mysql.event.po.EventPO" id="EventMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="content" column="content"
                typeHandler="com.wosai.trade.optimus.export.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="state" column="state" jdbcType="TINYINT"/>
        <result property="result" column="result" jdbcType="VARCHAR"/>
        <result property="delayRule" column="delay_rule"
                typeHandler="com.wosai.trade.optimus.export.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="nextProcessTime" column="next_process_time"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="associatedSn" column="associated_sn" jdbcType="VARCHAR"/>
        <result property="ext" column="ext"
                typeHandler="com.wosai.trade.optimus.export.infrastructure.plugin.mybatis.JsonTypeHandler"/>
        <result property="ctime" column="ctime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="mtime" column="mtime"
                typeHandler="org.apache.ibatis.type.LocalDateTimeTypeHandler"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="table">
        event
    </sql>

    <sql id="allColumn">
        id,
        `type`,
        content,
        `state`,
        `result`,
        delay_rule,
        next_process_time,
        associated_sn,
        ext,
        ctime,
        mtime,
        version
    </sql>


    <sql id="insertColumnValue">
        #{id},
        #{type},
        #{content},
        #{state},
        #{result},
        #{delayRule},
        #{nextProcessTime},
        #{associatedSn},
        #{ext},
        #{ctime},
        #{mtime},
        #{version}
    </sql>


    <insert id="insert">
        insert into
        <include refid="table"/>
        (
            <include refid="allColumn"/>
        )
        values
        (
            <include refid="insertColumnValue"/>
        )
    </insert>

    <insert id="batchInsert">
        insert into
        <include refid="table"/>
        (
            <include refid="allColumn"/>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.type}, #{item.content}, #{item.state}
                , #{item.result}, #{item.delayRule}, #{item.nextProcessTime}
                , #{item.associatedSn}, #{item.ext}, #{item.ctime}, #{item.mtime}
                , #{item.version}
            )
        </foreach>
    </insert>

    <update id="update">
        update
        <include refid="table"/>
        <set>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="result != null">
                `result` = #{result},
            </if>
            <if test="state != null">
                `state` = #{state},
            </if>
            <if test="delayRule != null">
                delay_rule = #{delayRule},
            </if>
            <if test="nextProcessTime != null">
                next_process_time = #{nextProcessTime},
            </if>
            <if test="associatedSn != null">
                associated_sn = #{associatedSn},
            </if>
            mtime = #{mtime},
            version = version + 1
        </set>
        where id = #{id} and version = #{version}
    </update>

    <select id="select" resultMap="EventMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <choose>
                <when test="id != null">
                    id = #{id}
                    <if test="type != null">
                        and `type` = #{type}
                    </if>
                </when>
                <when test="associatedSn != null and type != null">
                    associated_sn = #{associatedSn} and `type` = #{type}
                </when>
                <otherwise>false</otherwise>
            </choose>
        </where>
    </select>

    <select id="selectForUpdate" resultMap="EventMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        where id = #{id} for update
    </select>

    <select id="selectForUpdateSkipLocked" resultMap="EventMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        where id = #{id} for update skip locked
    </select>

    <select id="batchSelect" resultMap="EventMap">
        select
        <include refid="allColumn"/>
        from
        <include refid="table"/>
        <where>
            <if test="state != null">
                and state = #{state}
            </if>
            <if test="nextProcessTime != null">
                and next_process_time <![CDATA[<=]]> #{nextProcessTime}
            </if>
            <if test="idList != null and idList.size() > 0">
                and id in
                <foreach collection="idList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="sortField != null">
            order by `${sortField}`
            <if test="isDesc == false">
                asc
            </if>
            <if test="isDesc == true">
                desc
            </if>
        </if>
        <choose>
            <when test="querySize != null">
                limit ${querySize}
            </when>
            <otherwise>
                limit 1000
            </otherwise>
        </choose>
    </select>

    <delete id="delete">
        delete from
        <include refid="table"/>
        where id <![CDATA[<]]> #{idBefore} and `state` = #{state}
        limit ${count}
    </delete>

</mapper>