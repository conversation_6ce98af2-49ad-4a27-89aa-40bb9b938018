<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wosai.trade</groupId>
        <artifactId>optimus-export</artifactId>
        <version>1.0.12</version>
    </parent>

    <groupId>com.wosai.trade.optimus</groupId>
    <artifactId>optimus-export-infrastructure</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!--一方库-->
        <dependency>
            <groupId>com.wosai.trade.optimus</groupId>
            <artifactId>optimus-export-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.trade.optimus</groupId>
            <artifactId>optimus-export-domain</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--二方库-->
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>aop-gateway-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-web-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.common</groupId>
            <artifactId>wosai-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.trade.optimus</groupId>
            <artifactId>optimus-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.app</groupId>
            <artifactId>merchant-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-task-center-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--三方库-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>kafka-avro-serializer</artifactId>
        </dependency>
    </dependencies>



    <build>
        <plugins>
            <!--maven install|deploy时，跳过本模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>